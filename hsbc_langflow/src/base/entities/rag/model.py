from abc import ABC, abstractmethod
from collections.abc import Sequence
from typing import Any, Optional, List, Dict
from datetime import datetime

from pydantic import BaseModel, Field


class ChunkInfo(BaseModel):
    """
    分块信息模型，用于存储与 Chunk 相关的额外信息
    如关键词、标签等可扩展信息
    """
    chunk_info_id: str = Field(description="分块信息ID (UUID)")
    chunk_id: str = Field(description="关联的分块ID")
    info_type: str = Field(description="信息类型（如content,keyword）")
    info_value: Any = Field(description="信息值 (JSON/文本)")
    created_time: datetime = Field(description="创建时间")
    updated_time: Optional[datetime] = Field(default=None, description="更新时间")
    is_active: bool = Field(description="是否生效")


class Chunk(BaseModel):
    """
    实际入库的chunk，可以形成树状结构
    """
    chunk_id: str = Field(description="内容ID (UUID)")
    doc_id: str = Field(description="关联文档ID")
    chapter_layer: Optional[str] = Field(default=None, description="当前层级（如\"1.2.3\"）")
    parent_id: Optional[str] = Field(default=None, description="父分块ID（如为顶级节点可以为空）")
    sub_chunk_ids: Optional[List[str]] = Field(default=None, description="子分块ID列表（JSON数组）")
    is_active: bool = Field(description="是否生效")
    # 非数据库字段，用于内存中组织结构
    sub_chunks: Optional[List["Chunk"]] = Field(default=None, description="子分块对象实体列表", exclude=True)
    chunk_info: Optional[List[ChunkInfo]] = Field(default=None, description="增强信息列表", exclude=True)
    chunk_info_map: Optional[Dict[str, List[Any]]] = Field(default=None, description="按类型组织的增强信息映射", exclude=True)


class Document(BaseModel):
    """
    文档模型，对应文档表
    存储文档的基本元信息
    """
    doc_id: str = Field(description="文档ID (UUID)")
    doc_name: str = Field(description="文档标题")
    doc_format: str = Field(description="文档格式（如PDF/Markdown）")
    doc_type: Optional[str] = Field(default=None, description="文档类型")
    author: Optional[str] = Field(default=None, description="作者")
    vector_similarity_weight: Optional[float] = Field(default=None, description="向量相似度权重")
    similarity_threshold: Optional[float] = Field(default=None, description="相似度阈值")
    user_id: str = Field(description="创建人ID")
    create_time: datetime = Field(description="创建时间")
    is_active: bool = Field(description="是否生效")
    # 非数据库字段，用于内存中组织结构
    contents: Optional[List[Chunk]] = Field(default=None, description="文档内容")


# VS
class DocumentEmbedding(BaseModel):
    """
    文档嵌入模型，对应document_embedding表
    存储分块信息的向量嵌入
    """
    id: str = Field(description="索引ID (UUID)")
    doc_id: str = Field(description="关联文档ID")
    chunk_info_id: str = Field(description="关联分块信息ID")
    embedding: List[float] = Field(description="向量嵌入")
    info_type: str = Field(description="信息类型 (content, title)")
    partition_key: Optional[str] = Field(default=None, description="分区字段")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="元数据 (JSON对象)")