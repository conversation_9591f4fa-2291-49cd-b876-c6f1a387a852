from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field

from base.model_serve.model_runtime.entities.message_entities import PromptMessage, PromptMessageRole


class ChatMessage(BaseModel):
    """
    聊天消息模型类
    """
    id: UUID = Field(default_factory=uuid4, description="消息ID")
    role: PromptMessageRole = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class ChatHistory(BaseModel):
    """
    聊天历史记录模型类，用于存储LLM交互的历史聊天信息
    """
    id: UUID = Field(default_factory=uuid4, description="历史记录ID")
    session_id: str = Field(..., description="会话ID")
    messages: List[ChatMessage] = Field(default_factory=list, description="消息列表")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    def add_message(self, content: str, role: PromptMessageRole) -> ChatMessage:
        """
        添加消息到历史记录
        
        :param content: 消息内容
        :param role: 消息角色
        :return: 添加的消息
        """
        message = ChatMessage(
            role=role,
            content=content
        )
        self.messages.append(message)
        self.updated_at = datetime.now()
        return message
    
    def get_messages(self) -> List[ChatMessage]:
        """
        获取所有消息
        
        :return: 消息列表
        """
        return self.messages
    
    def clear(self) -> None:
        """
        清空历史记录中的所有消息
        """
        self.messages = []
        self.updated_at = datetime.now()
    
    def to_prompt_messages(self) -> List[PromptMessage]:
        """
        将历史记录中的消息转换为PromptMessage列表，用于LLM交互
        
        :return: PromptMessage列表
        """
        return [
            PromptMessage(role=msg.role, content=msg.content)
            for msg in self.messages
        ]