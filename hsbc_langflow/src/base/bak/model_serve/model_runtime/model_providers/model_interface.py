'''
File Created: Monday, 9th June 2025 10:12:35 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 10:16:10 am
'''

from abc import ABC, abstractmethod
from collections.abc import Generator, Sequence, AsyncGenerator
from typing import Optional, Union, Any, Dict, Type, List

from base.model_serve.model_runtime.callbacks.base_callback import Callback
from base.model_serve.model_runtime.entities.llm_entities import <PERSON><PERSON>esult, LLMResultChunk, LLMUsage
from base.model_serve.model_runtime.entities.message_entities import (
    AssistantPromptMessage,
    PromptMessage,
    PromptMessageTool,
)
from base.model_serve.model_runtime.entities.model_entities import ModelType
from base.model_serve.model_runtime.entities.rerank_entities import RerankResult
from base.model_serve.model_runtime.entities.text_embedding_entities import (
    TextEmbeddingResult,
    EmbeddingUsage,
)
from base.model_serve.model_runtime.model_providers.__base.large_language_model import LargeLanguageModel
from base.model_serve.model_runtime.model_providers.__base.text_embedding_model import TextEmbeddingModel
from base.model_serve.model_runtime.model_providers.__base.rerank_model import RerankModel
from base.model_serve.model_runtime.model_providers.llm_model.sample_llm import SampleLLM

# 导入不同供应商的模型实现
from base.model_serve.model_runtime.model_providers.llm_model.zhipu import Zhipu
from base.model_serve.model_runtime.model_providers.embedding_model.sample_embedding import SampleEmbedding
from base.model_serve.model_runtime.model_providers.llm_model.opentrek_llm import OpenTrekLLM


# 可以根据需要导入更多模型实现
# from model_serve.model_runtime.model_providers.rerank_model.xxx import XXX

# 删除UnifiedModelInterface类

class UnifiedLLMInterface:
    """
    统一的大语言模型接口类，用于管理不同供应商的模型实现
    """

    def __init__(self):
        # 注册可用的模型实现
        self._model_registry: Dict[str, Type[LargeLanguageModel]] = {
            "zhipu": Zhipu,
            "qwen_vllm": SampleLLM,
            "opentrek": OpenTrekLLM
            # 可以添加更多模型
            # "other_provider": OtherProviderClass,
        }
        self.tenant_id = ""

    def get_model_instance(self, provider: str) -> LargeLanguageModel:
        """
        获取指定供应商的模型实例
        
        :param provider: 模型供应商名称
        :return: 模型实例
        """
        if provider not in self._model_registry:
            raise ValueError(f"不支持的模型供应商: {provider}")

        return self._model_registry[provider]()

    def invoke(
            self,
            model: str,
            credentials: dict,
            prompt_messages: list[PromptMessage],
            model_parameters: Optional[dict] = None,
            tools: Optional[list[PromptMessageTool]] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
            user: Optional[str] = None,
            callbacks: Optional[list[Callback]] = None,
    ) -> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        """
        调用大语言模型
        
        :param model: 模型名称
        :param credentials: 模型凭证
        :param prompt_messages: 提示消息列表
        :param model_parameters: 模型参数
        :param tools: 工具列表
        :param stop: 停止词列表
        :param stream: 是否流式响应
        :param user: 用户ID
        :param callbacks: 回调函数列表
        :return: 返回模型结果或流式结果生成器
        """
        # 从凭证中获取供应商信息
        provider = credentials.get("provider")
        if not provider:
            raise ValueError("凭证中缺少供应商信息")

        # 获取对应供应商的模型实例
        model_instance = self.get_model_instance(provider)
        model_instance.tenant_id = self.tenant_id
        model_instance.provider_name = provider

        # 调用模型实例的invoke方法
        return model_instance.invoke(
            model=model,
            credentials=credentials,
            prompt_messages=prompt_messages,
            model_parameters=model_parameters,
            tools=tools,
            stop=stop,
            stream=stream,
            user=user,
            callbacks=callbacks
        )

    async def ainvoke(
            self,
            model: str,
            credentials: dict,
            prompt_messages: list[PromptMessage],
            model_parameters: Optional[dict] = None,
            tools: Optional[list[PromptMessageTool]] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
            user: Optional[str] = None,
            callbacks: Optional[list[Callback]] = None,
    ) -> Union[LLMResult, AsyncGenerator[LLMResultChunk, None]]:
        """
        异步调用大语言模型
        
        :param model: 模型名称
        :param credentials: 模型凭证
        :param prompt_messages: 提示消息列表
        :param model_parameters: 模型参数
        :param tools: 工具列表
        :param stop: 停止词列表
        :param stream: 是否流式响应
        :param user: 用户ID
        :param callbacks: 回调函数列表
        :return: 返回模型结果或流式结果生成器
        """
        # 从凭证中获取供应商信息
        provider = credentials.get("provider")
        if not provider:
            raise ValueError("凭证中缺少供应商信息")

        # 获取对应供应商的模型实例
        model_instance = self.get_model_instance(provider)
        model_instance.tenant_id = self.tenant_id
        model_instance.provider_name = provider

        # 调用模型实例的ainvoke方法
        return await model_instance.ainvoke(
            model=model,
            credentials=credentials,
            prompt_messages=prompt_messages,
            model_parameters=model_parameters,
            tools=tools,
            stop=stop,
            stream=stream,
            user=user,
            callbacks=callbacks
        )

    def get_available_providers(self) -> List[str]:
        """
        获取所有可用的模型供应商
        
        :return: 供应商名称列表
        """
        return list(self._model_registry.keys())

    def register_model(self, provider: str, model_class: Type[LargeLanguageModel]) -> None:
        """
        注册新的模型实现
        
        :param provider: 供应商名称
        :param model_class: 模型类
        """
        self._model_registry[provider] = model_class


class UnifiedTextEmbeddingInterface:
    """
    文本嵌入模型统一接口类，用于生成文本嵌入向量。
    """

    def __init__(self):
        # 注册可用的模型实现
        self._model_registry: Dict[str, Type[TextEmbeddingModel]] = {
            "moka-m3e-base": SampleEmbedding,
            # 可以添加更多文本嵌入模型实现
            # "other_provider": OtherProviderClass,
        }
        self.tenant_id = ""

    def get_model_instance(self, provider: str) -> TextEmbeddingModel:
        """
        获取指定供应商的模型实例
        
        :param provider: 模型供应商名称
        :return: 模型实例
        """
        if provider not in self._model_registry:
            raise ValueError(f"不支持的模型供应商: {provider}")

        return self._model_registry[provider]()

    def invoke(
            self,
            model: str,
            credentials: dict,
            texts: list[str],
            user: Optional[str] = None
    ) -> TextEmbeddingResult:
        """
        创建文本嵌入。
        
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 文本列表
        :param user: 唯一用户ID
        :return: 文本嵌入结果
        """
        # 从凭证中获取供应商信息
        provider = credentials.get("provider")
        if not provider:
            raise ValueError("凭证中缺少供应商信息")

        # 获取对应供应商的模型实例
        model_instance = self.get_model_instance(provider)
        model_instance.tenant_id = self.tenant_id
        model_instance.provider_name = provider

        # 调用模型实例的create_embedding方法
        return model_instance.invoke(
            model=model,
            credentials=credentials,
            texts=texts,
            user=user,
        )

    async def ainvoke(
            self,
            model: str,
            credentials: dict,
            texts: list[str],
            user: Optional[str] = None,
    ) -> TextEmbeddingResult:
        """
        异步创建文本嵌入。
        
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 文本列表
        :param user: 唯一用户ID
        :param callbacks: 回调函数
        :return: 文本嵌入结果
        """
        # 从凭证中获取供应商信息
        provider = credentials.get("provider")
        if not provider:
            raise ValueError("凭证中缺少供应商信息")

        # 获取对应供应商的模型实例
        model_instance = self.get_model_instance(provider)
        model_instance.tenant_id = self.tenant_id
        model_instance.provider_name = provider

        # 调用模型实例的ainvoke方法
        return await model_instance.ainvoke(
            model=model,
            credentials=credentials,
            texts=texts,
            user=user,
        )

    def get_num_tokens(
            self,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> int:
        """
        获取文本的令牌数。
        
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 文本列表
        :return: 令牌数
        """
        # 从凭证中获取供应商信息
        provider = credentials.get("provider")
        if not provider:
            raise ValueError("凭证中缺少供应商信息")

        # 获取对应供应商的模型实例
        model_instance = self.get_model_instance(provider)
        model_instance.tenant_id = self.tenant_id
        model_instance.provider_name = provider

        # 调用模型实例的get_num_tokens方法
        return model_instance.get_num_tokens(
            model=model,
            credentials=credentials,
            texts=texts
        )

    def get_available_providers(self) -> List[str]:
        """
        获取所有可用的模型供应商
        
        :return: 供应商名称列表
        """
        return list(self._model_registry.keys())

    def register_model(self, provider: str, model_class: Type[TextEmbeddingModel]) -> None:
        """
        注册新的模型实现
        
        :param provider: 供应商名称
        :param model_class: 模型类
        """
        self._model_registry[provider] = model_class


class UnifiedRerankInterface:
    """
    重排序模型统一接口类，用于对文档进行相关性排序。
    """

    def __init__(self):
        # 注册可用的模型实现
        self._model_registry: Dict[str, Type[RerankModel]] = {
            # 可以添加重排序模型实现
            # "provider_name": ProviderRerankClass,
        }
        self.tenant_id = ""

    def get_model_instance(self, provider: str) -> RerankModel:
        """
        获取指定供应商的模型实例
        
        :param provider: 模型供应商名称
        :return: 模型实例
        """
        if provider not in self._model_registry:
            raise ValueError(f"不支持的模型供应商: {provider}")

        return self._model_registry[provider]()

    def invoke(
            self,
            model: str,
            credentials: dict,
            query: str,
            documents: list[str],
            model_parameters: Optional[dict] = None,
            user: Optional[str] = None,
            callbacks: Optional[list[Callback]] = None,
    ) -> RerankResult:
        """
        对文档进行重排序。
        
        :param model: 模型名称
        :param credentials: 模型凭证
        :param query: 查询文本
        :param documents: 文档列表
        :param model_parameters: 模型参数
        :param user: 唯一用户ID
        :param callbacks: 回调函数
        :return: 重排序结果
        """
        # 从凭证中获取供应商信息
        provider = credentials.get("provider")
        if not provider:
            raise ValueError("凭证中缺少供应商信息")

        # 获取对应供应商的模型实例
        model_instance = self.get_model_instance(provider)
        model_instance.tenant_id = self.tenant_id
        model_instance.provider_name = provider

        # 调用模型实例的invoke方法
        return model_instance.invoke(
            model=model,
            credentials=credentials,
            query=query,
            docs=documents,
            score_threshold=model_parameters.get("score_threshold") if model_parameters else None,
            top_n=model_parameters.get("top_n") if model_parameters else None,
            user=user
        )

    async def ainvoke(
            self,
            model: str,
            credentials: dict,
            query: str,
            documents: list[str],
            model_parameters: Optional[dict] = None,
            user: Optional[str] = None,
            callbacks: Optional[list[Callback]] = None,
    ) -> RerankResult:
        """
        异步对文档进行重排序。
        
        :param model: 模型名称
        :param credentials: 模型凭证
        :param query: 查询文本
        :param documents: 文档列表
        :param model_parameters: 模型参数
        :param user: 唯一用户ID
        :param callbacks: 回调函数
        :return: 重排序结果
        """
        # 从凭证中获取供应商信息
        provider = credentials.get("provider")
        if not provider:
            raise ValueError("凭证中缺少供应商信息")

        # 获取对应供应商的模型实例
        model_instance = self.get_model_instance(provider)
        model_instance.tenant_id = self.tenant_id
        model_instance.provider_name = provider

        # 调用模型实例的ainvoke方法
        return await model_instance.ainvoke(
            model=model,
            credentials=credentials,
            query=query,
            docs=documents,
            score_threshold=model_parameters.get("score_threshold") if model_parameters else None,
            top_n=model_parameters.get("top_n") if model_parameters else None,
            user=user
        )

    def get_available_providers(self) -> List[str]:
        """
        获取所有可用的模型供应商
        
        :return: 供应商名称列表
        """
        return list(self._model_registry.keys())

    def register_model(self, provider: str, model_class: Type[RerankModel]) -> None:
        """
        注册新的模型实现
        
        :param provider: 供应商名称
        :param model_class: 模型类
        """
        self._model_registry[provider] = model_class
