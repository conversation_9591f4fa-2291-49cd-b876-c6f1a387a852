'''
File Created: Monday, 9th June 2025 10:12:35 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 10:15:31 am
'''

from typing import Optional, Union, Generator, AsyncGenerator, List, Dict, Any, Callable
import aiohttp
import json
import asyncio
from ..__base.large_language_model import LargeLanguageModel
from ...entities import PromptMessage, PromptMessageTool, LLMResult, LLMResultChunk, AssistantPromptMessage, \
    LLMResultChunkDelta
import logging
import requests

# 日志设置（保持不变）
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class OpentrekStream:
    """异步迭代器，用于处理Opentrek的流式响应，并包装成兼容OpenAI的格式。"""

    def __init__(self, response: aiohttp.ClientResponse, session: aiohttp.ClientSession, model: str):
        self.response = response
        self.session = session
        self.model = model
        self.index = 0
        self.iterator = self.response.content.iter_chunks()
        logger.debug("OpentrekStream initialized.")
        # 调试打印（可选，可删除）
        print(dir(response))
        print(dir(response.content))
        print(response.content.iter_chunks)

    def __aiter__(self):
        return self

    async def __anext__(self) -> LLMResultChunk:
        # logger.debug(f"Response headers: {self.response.headers}")
        try:
            async for chunk in self.iterator:
                line_bytes, _ = chunk
                if not line_bytes:
                    logger.info("Stream finished (empty chunk).")
                    if not self.session.closed:
                        await self.session.close()
                    raise StopAsyncIteration

                line = line_bytes.decode('utf-8').strip()
                # logger.debug(f"Decoded line: {line}")
                if not line or line == "data: [DONE]":
                    # logger.info("Stream finished ([DONE] marker).")
                    if not self.session.closed:
                        await self.session.close()
                    raise StopAsyncIteration

                if line.startswith("data:"):
                    llm_datas = line.split("data:")
                    for li in llm_datas:
                        data_str = li.strip()
                        if not data_str:
                            continue
                        try:
                            # logger.info(f"这是data数据([DONE] marker).{data_str}")
                            chunk_data = json.loads(data_str)
                            # print(chunk_data)
                            if "choices" in chunk_data and chunk_data["choices"]:
                                delta = chunk_data["choices"][0].get("delta", {})
                                finish_reason = chunk_data["choices"][0].get("finish_reason", "")
                                if "content" in delta:
                                    content_chunk = delta["content"]
                                    message = AssistantPromptMessage(content=content_chunk, tool_calls=[])
                                    result = LLMResultChunk(
                                        model=self.model,
                                        delta=LLMResultChunkDelta(
                                            index=self.index,
                                            message=message,
                                            finish_reason=finish_reason
                                        )
                                    )
                                    self.index += 1
                                    return result
                        except json.JSONDecodeError:
                            logger.warning(f"Failed to parse JSON: {data_str}")
                            continue
            # 如果 async for 循环自然结束（例如流耗尽）
            if not self.session.closed:
                await self.session.close()
            raise StopAsyncIteration
        except StopAsyncIteration:
            if not self.session.closed:
                await self.session.close()
            raise
        except Exception as e:
            if not self.session.closed:
                await self.session.close()
            logger.error(f"Error in stream: {str(e)}")
            raise


class OpenTrekLLM(LargeLanguageModel):
    async def async_call_openai_sdk(self, key: str, url: str, **payload) -> Any:
        headers = {
            "Authorization": f"Bearer {key}",
            "Content-Type": "application/json"
        }
        session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60, sock_read=30))
        try:
            response = await session.post(url, headers=headers, json=payload)
            if response.status != 200:
                error_text = await response.text()
                logger.error(f"Server returned status {response.status}: {error_text}")
                raise aiohttp.ClientResponseError(
                    response.request_info,
                    response.history,
                    status=response.status,
                    message=error_text
                )
            if payload.get('stream', False):
                logger.debug(f"Stream response headers: {response.headers}")
                return OpentrekStream(response, session,  payload.get('model', 'gpt-3.5-turbo'))
            else:

                result_dict = await response.json()
                logger.info(f"response:{result_dict}")
                await session.close()
                return result_dict
        except Exception as e:
            logger.error(f"Request failed: {str(e)}")
            await session.close()
            raise

    def call_openai_sdk(self, key: str, url: str, **payload) -> Any:
        headers = {
            "Authorization": f"{key}",
            "Content-Type": "application/json"
        }
        with requests.Session() as session:
            if payload['stream']:
                response = session.post(url, json=payload, headers=headers, timeout=None,
                                        verify=False, stream=True)
                return response
            else:
                response = session.post(url, json=payload, headers=headers, timeout=None,
                                        verify=False)
                return json.loads(response.content.decode('utf-8'))

    def invoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: List[PromptMessage],
            tools: Optional[List[PromptMessageTool]] = None,
            stop: Optional[List[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        """调用大语言模型，生成响应或流式响应。"""
        # 转换 PromptMessage 对象为字符串消息
        messages = [
            {"role": msg.role.value if hasattr(msg.role, 'value') else msg.role, "content": msg.content}
            for msg in prompt_messages
        ]

        # 从参数中提取配置，设置默认值
        max_tokens = model_parameters.get("max_tokens", 2048)
        temperature = model_parameters.get("temperature", 0.9)
        base_url = credentials.get("base_url", "http://218.78.129.173:30167/v1")
        api_key = credentials.get("api_key", "empty")

        # 构建请求负载
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
        }

        try:
            if stream:
                def stream_generator() -> Generator[LLMResultChunk, None, None]:
                    response = self.call_openai_sdk(key=api_key, url=base_url, **payload)
                    accumulated_content = ""
                    index = 0
                    buffer = b''

                    for chunk in response.iter_content(chunk_size=8192):
                        if not chunk:
                            continue
                        buffer += chunk
                        while b'\n' in buffer:
                            line, buffer = buffer.split(b'\n', 1)
                            data = line.decode('utf-8').strip()
                            if not data or data == "data: [DONE]":
                                continue
                            if data.startswith("data: "):
                                data = json.loads(data[6:])
                                if "choices" in data and data["choices"]:
                                    delta = data["choices"][0].get("delta", {})
                                    finish_reason = data["choices"][0].get("finish_reason", "")
                                    if "content" in delta:
                                        content_chunk = delta["content"]
                                        accumulated_content += content_chunk
                                        # logging.info(f"Accumulated content: {accumulated_content}")
                                        message = AssistantPromptMessage(content=content_chunk, tool_calls=[])
                                        yield LLMResultChunk(
                                            model=model,
                                            delta=LLMResultChunkDelta(
                                                index=index,
                                                message=message,
                                                finish_reason=finish_reason
                                            )
                                        )
                                        index += 1

                return stream_generator()

            else:
                response = self.call_openai_sdk(key=api_key, url=base_url, **payload)
                choices = response.get("choices", [])
                if not choices:
                    logging.warning("LLM 返回空响应，返回空内容。")
                    content_result = ""
                else:
                    content_result = choices[0]["message"]["content"]
                    logging.info(f"LLM 响应内容: {content_result}")

                return LLMResult(
                    model=model,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(
                        content=content_result.strip() if content_result else "",
                        tool_calls=[],
                    ),
                    usage=self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=0,  # response.usage.prompt_tokens
                        completion_tokens=0,  # response.usage.completion_tokens
                    )
                )

        except Exception as e:
            logging.error(f"调用 LLM 失败: {str(e)}")
            raise Exception(f"获取 LLM 响应失败: {str(e)}")

    async def ainvoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: list[PromptMessage],
            tools: Optional[list['PromptMessageTool']] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, AsyncGenerator[LLMResultChunk, None]]:
        # print('ainvoke in sample_llm')

        # 将 PromptMessage 对象转换为字符串内容
        messages = [{"role": msg.role.value if hasattr(msg.role, 'value') else msg.role, "content": msg.content} for
                    msg in prompt_messages]
        # print(f"messages:{messages}")

        max_tokens = model_parameters.get("max_tokens", 2048)
        temperature = model_parameters.get("temperature", 0.9)
        base_url = credentials.get("base_url", "http://218.78.129.173:30167/v1")
        api_key = credentials.get("api_key", "empty")

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
        }

        try:
            response_generator = await self.async_call_openai_sdk(key=api_key, url=base_url, **payload)

            if stream:
                return response_generator

            else:
                response_json = response_generator
                logger.info(f"response:{response_json}")

                choices = response_json.get('choices', [])
                # print(f"choices:{choices}")
                if not choices:
                    logging.warning("Empty response from LLM. Returning empty content.")
                    content_result = ""
                else:
                    content_result = choices[0].get('message', {}).get('content', '')
                    # print('-------')
                    # print(content_result)

                # usage = response_json.get('usage', {})

                return LLMResult(
                    model=model,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(
                        content=content_result.strip() if content_result else "",
                        tool_calls=[],
                    ),
                    usage=self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=0,  # usage.get('prompt_tokens', 0)
                        completion_tokens=0,  # usage.get('completion_tokens', 0)
                    )
                )

        except Exception as e:
            raise Exception(f"Failed to get LLM response: {str(e)}")
