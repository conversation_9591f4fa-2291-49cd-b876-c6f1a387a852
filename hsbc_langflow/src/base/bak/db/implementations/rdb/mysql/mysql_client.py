import pymysql
from pymysql import MySQLError
from dbutils.pooled_db import PooledDB
from ....base.rdb_client import RDBClient
from ....base.schemas import RDSTableSchema
from typing import Any, Dict, List, Optional, Tuple, Union
import logging

logger = logging.getLogger(__name__)


class MySQLClient(RDBClient):
    """MySQL 客户端实现，使用 pymysql 和 DBUtils.PooledDB 连接池"""

    def __init__(self, host="localhost", port=3306, user="your_username", password="your_password",
                 database="your_database"):
        logger.info("Connecting to MySQL database...")
        self.pool = None
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.charset = "utf8mb4"
        self.current_connection = None

    def connect(self) -> None:
        """连接到数据库"""
        try:
            self.pool = PooledDB(
                creator=pymysql,
                maxconnections=5,
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset=self.charset,
                cursorclass=pymysql.cursors.DictCursor
            )
            logger.info("MySQL connection pool created successfully.")
        except MySQLError as e:
            logger.error(f"Failed to connect to MySQL: {e}")
            raise Exception(f"Failed to connect to MySQL: {str(e)}")

    def disconnect(self) -> None:
        """断开与数据库的连接"""
        try:
            if hasattr(self, "pool") and self.pool is not None:
                self.pool.close()
                self.pool = None
                logger.info("Connection pool closed successfully.")
        except Exception as e:
            logger.error(f"Error closing pool: {e}")
            self.pool = None

    def _get_connection(self):
        """从连接池获取连接"""
        try:
            connection = self.pool.connection()
            connection.autocommit = False  # 关闭自动提交，启用手动事务管理
            logger.debug("Acquired connection from pool.")
            return connection
        except MySQLError as e:
            logger.error(f"Failed to get connection from pool: {e}")
            raise Exception(f"Failed to get connection from pool: {str(e)}")

    # DDL
    def insert(self, table: str, data: List[Dict[str, Any]]) -> bool:
        """
        在指定表中创建一条或多条新记录。

        Args:
            table: 表名
            data: 要插入的数据。列表中每个元素为一条记录的数据

        Returns:
            插入是否成功
        """
        if not data:
            return False

        # 检查是否在事务中
        in_transaction = self.current_connection is not None
        connection = None

        try:
            # 如果在事务中，使用事务连接；否则从池中获取新连接
            if in_transaction:
                connection = self.current_connection
                cursor = connection.cursor()
                logger.debug("Using transaction connection for INSERT.")
            else:
                connection = self._get_connection()
                cursor = connection.cursor()
                logger.debug("Using new connection for INSERT.")

            for record in data:
                columns = ', '.join(record.keys())
                placeholders = ', '.join(['%s'] * len(record))
                query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
                cursor.execute(query, list(record.values()))

            # 只有非事务连接才需要提交
            if not in_transaction:
                connection.commit()
                logger.info(f"INSERT operation completed for {len(data)} records in {table}.")
            else:
                logger.info(f"INSERT operation added to transaction for {len(data)} records in {table}.")

            return True
        except MySQLError as e:
            # 只有非事务连接才需要回滚，事务连接的回滚由rollback()方法处理
            if connection and not in_transaction:
                connection.rollback()
            logger.error(f"Failed to execute INSERT: {e}")
            raise Exception(f"Failed to execute INSERT: {str(e)}")
        finally:
            # 只有非事务连接才需要关闭，事务连接由commit()/rollback()方法关闭
            if connection and not in_transaction:
                connection.close()
                logger.debug("Connection returned to pool after INSERT.")

    def select(
            self,
            table: str,
            columns: Optional[List[str]] = None,
            condition: Optional[Dict[str, Any]] = None,
            limit: Optional[int] = None,
            offset: Optional[int] = None,
            order_by: Optional[Union[str, List[str]]] = None,
    ) -> List[Dict[str, Any]]:
        """
        从指定表中查询记录。
        
        Args:
            table: 表名
            columns: 要查询的列名列表。如果为 None，则选择所有列 (SELECT *)。
            condition: 查询条件字典，键为列名，值为该列需要匹配的值。
            limit: 返回的最大记录数。
            offset: 返回记录的偏移量。
            order_by: 排序依据。可以是单个列名字符串或列名列表。
        
        Returns:
            查询结果列表，列表中每个字典代表一条记录。
        """
        # 检查是否在事务中
        in_transaction = self.current_connection is not None
        connection = None

        try:
            # 如果在事务中，使用事务连接；否则从池中获取新连接
            if in_transaction:
                connection = self.current_connection
                cursor = connection.cursor()
                logger.debug("Using transaction connection for SELECT.")
            else:
                connection = self._get_connection()
                cursor = connection.cursor()
                logger.debug("Using new connection for SELECT.")

            # 构建SELECT部分
            select_clause = "SELECT * FROM"
            if columns:
                select_clause = f"SELECT {', '.join(columns)} FROM"

            # 构建WHERE部分
            where_clause = ""
            params = []
            if condition:
                conditions = []
                for key, value in condition.items():
                    conditions.append(f"{key} = %s")
                    params.append(value)
                if conditions:
                    where_clause = f"WHERE {' AND '.join(conditions)}"

            # 构建ORDER BY部分
            order_clause = ""
            if order_by:
                if isinstance(order_by, list):
                    order_clause = f"ORDER BY {', '.join(order_by)}"
                else:
                    order_clause = f"ORDER BY {order_by}"

            # 构建LIMIT和OFFSET部分
            limit_clause = ""
            if limit is not None:
                limit_clause = f"LIMIT {limit}"
                if offset is not None:
                    limit_clause += f" OFFSET {offset}"

            # 组合完整查询
            query = f"{select_clause} {table} {where_clause} {order_clause} {limit_clause}".strip()

            cursor.execute(query, params)
            results = cursor.fetchall()
            logger.info("SELECT operation completed.")
            return results
        except MySQLError as e:
            logger.error(f"Failed to execute SELECT: {e}")
            raise Exception(f"Failed to execute SELECT: {str(e)}")
        finally:
            # 只有非事务连接才需要关闭，事务连接由commit()/rollback()方法关闭
            if connection and not in_transaction:
                connection.close()
                logger.debug("Connection returned to pool after SELECT.")

    def update(self, table: str, filter: dict, data: Dict[str, Any]) -> bool:
        """
        更新指定表中的一条或多条记录。
    
        Args:
            table: 表名
            filter: 用于指定更新哪些记录的条件字典。
            data: 要更新的数据，键为列名，值为新的列值。
    
        Returns:
            更新是否成功
        """
        if not data:
            return False

        # 检查是否在事务中
        in_transaction = self.current_connection is not None
        connection = None

        try:
            # 如果在事务中，使用事务连接；否则从池中获取新连接
            if in_transaction:
                connection = self.current_connection
                cursor = connection.cursor()
                logger.debug("Using transaction connection for UPDATE.")
            else:
                connection = self._get_connection()
                cursor = connection.cursor()
                logger.debug("Using new connection for UPDATE.")

            # 构建SET部分
            set_clause = ", ".join([f"{key} = %s" for key in data.keys()])
            set_values = list(data.values())

            # 构建WHERE部分
            where_clause = ""
            where_values = []
            if filter:
                conditions = []
                for key, value in filter.items():
                    conditions.append(f"{key} = %s")
                    where_values.append(value)
                where_clause = f"WHERE {' AND '.join(conditions)}"

            # 组合完整查询
            query = f"UPDATE {table} SET {set_clause} {where_clause}"

            cursor.execute(query, set_values + where_values)

            # 只有非事务连接才需要提交
            if not in_transaction:
                connection.commit()
                logger.info(f"UPDATE operation completed. Affected rows: {cursor.rowcount}")
            else:
                logger.info(f"UPDATE operation added to transaction. Affected rows: {cursor.rowcount}")

            return True
        except MySQLError as e:
            # 只有非事务连接才需要回滚，事务连接的回滚由rollback()方法处理
            if connection and not in_transaction:
                connection.rollback()
            logger.error(f"Failed to execute UPDATE: {e}")
            raise Exception(f"Failed to execute UPDATE: {str(e)}")
        finally:
            # 只有非事务连接才需要关闭，事务连接由commit()/rollback()方法关闭
            if connection and not in_transaction:
                connection.close()
                logger.debug("Connection returned to pool after UPDATE.")

    def delete(self, table: str, filter: dict) -> bool:
        """
        删除指定表中的一条或多条记录。
    
        Args:
            table: 表名
            filter: 用于指定删除哪些记录的条件字典。
    
        Returns:
            删除是否成功
        """
        # 检查是否在事务中
        in_transaction = self.current_connection is not None
        connection = None

        try:
            # 如果在事务中，使用事务连接；否则从池中获取新连接
            if in_transaction:
                connection = self.current_connection
                cursor = connection.cursor()
                logger.debug("Using transaction connection for DELETE.")
            else:
                connection = self._get_connection()
                cursor = connection.cursor()
                logger.debug("Using new connection for DELETE.")

            # 构建WHERE部分
            where_clause = ""
            params = []
            if filter:
                conditions = []
                for key, value in filter.items():
                    conditions.append(f"{key} = %s")
                    params.append(value)
                where_clause = f"WHERE {' AND '.join(conditions)}"

            # 组合完整查询
            query = f"DELETE FROM {table} {where_clause}"

            cursor.execute(query, params)

            # 只有非事务连接才需要提交
            if not in_transaction:
                connection.commit()
                logger.info(f"DELETE operation completed. Affected rows: {cursor.rowcount}")
            else:
                logger.info(f"DELETE operation added to transaction. Affected rows: {cursor.rowcount}")

            return True
        except MySQLError as e:
            # 只有非事务连接才需要回滚，事务连接的回滚由rollback()方法处理
            if connection and not in_transaction:
                connection.rollback()
            logger.error(f"Failed to execute DELETE: {e}")
            raise Exception(f"Failed to execute DELETE: {str(e)}")
        finally:
            # 只有非事务连接才需要关闭，事务连接由commit()/rollback()方法关闭
            if connection and not in_transaction:
                connection.close()
                logger.debug("Connection returned to pool after DELETE.")

    # DML
    def create_table(self, table_name: str, schema: RDSTableSchema) -> bool:
        """
        根据提供的 RDSTableSchema 创建新表。

        Args:
            table_name: 新表的名称。
            schema: RDSTableSchema 对象，包含了表名、列定义和可选的表选项。

        Returns:
            创建是否成功。
        """
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            # 从schema中直接获取列定义
            column_definitions = []
            for column in schema.columns:
                col_def = f"`{column.name}` {column.column_type}"

                # 处理附加属性
                if hasattr(column, 'additional_properties') and column.additional_properties:
                    for prop, value in column.additional_properties.items():
                        if prop == "AUTO_INCREMENT" and value:
                            col_def += " AUTO_INCREMENT"
                        elif prop == "NOT NULL" and value:
                            col_def += " NOT NULL"
                        elif prop == "UNIQUE" and value:
                            col_def += " UNIQUE"
                        elif prop == "DEFAULT":
                            if isinstance(value, str) and value != "CURRENT_TIMESTAMP":
                                col_def += f" DEFAULT '{value}'"
                            else:
                                col_def += f" DEFAULT {value}"

                # 处理主键
                if hasattr(column, 'is_primary') and column.is_primary:
                    col_def += " PRIMARY KEY"

                column_definitions.append(col_def)

            # 组合列定义
            columns_def = ", ".join(column_definitions)

            # 处理表选项
            table_options = ""
            if hasattr(schema, 'options') and schema.options:
                options_list = []
                for opt, val in schema.options.items():
                    options_list.append(f"{opt}={val}")
                if options_list:
                    table_options = " " + " ".join(options_list)

            query = f"CREATE TABLE {table_name} ({columns_def}){table_options}"

            cursor.execute(query)
            connection.commit()
            logger.info(f"Table {table_name} created successfully.")
            return True
        except MySQLError as e:
            if connection:
                connection.rollback()
            logger.error(f"Failed to create table: {e}")
            raise Exception(f"Failed to create table: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after CREATE TABLE.")

    def drop_table(self, table_name: str, if_exists: bool = False) -> bool:
        """
        删除表。

        Args:
            table_name: 表名。
            if_exists: 如果为 True，则仅当表存在时才删除。

        Returns:
            删除是否成功。
        """
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            if_exists_clause = "IF EXISTS" if if_exists else ""
            query = f"DROP TABLE {if_exists_clause} {table_name}"

            cursor.execute(query)
            connection.commit()
            logger.info(f"Table {table_name} dropped successfully.")
            return True
        except MySQLError as e:
            if connection:
                connection.rollback()
            logger.error(f"Failed to drop table: {e}")
            raise Exception(f"Failed to drop table: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after DROP TABLE.")

    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在。

        Args:
            table_name: 表名。

        Returns:
            如果表存在则返回 True，否则返回 False。
        """
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            query = f"SELECT 1 FROM information_schema.tables WHERE table_schema = %s AND table_name = %s"
            cursor.execute(query, (self.database, table_name))

            result = cursor.fetchone()
            return result is not None
        except MySQLError as e:
            logger.error(f"Failed to check if table exists: {e}")
            raise Exception(f"Failed to check if table exists: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after checking table existence.")

    def list_tables(self) -> List[str]:
        """
        列出数据库中的所有表。

        Returns:
            表名列表。
        """
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            query = f"SHOW TABLES"
            cursor.execute(query)

            results = cursor.fetchall()
            # 结果是字典列表，需要提取表名
            table_names = [list(row.values())[0] for row in results]
            return table_names
        except MySQLError as e:
            logger.error(f"Failed to list tables: {e}")
            raise Exception(f"Failed to list tables: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after listing tables.")

    def truncate_table(self, table_name: str) -> bool:
        """
        清空表中的所有数据，但保留表结构。

        Args:
            table_name: 表名。

        Returns:
            操作是否成功。
        """
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            query = f"TRUNCATE TABLE {table_name}"
            cursor.execute(query)

            connection.commit()
            logger.info(f"Table {table_name} truncated successfully.")
            return True
        except MySQLError as e:
            if connection:
                connection.rollback()
            logger.error(f"Failed to truncate table: {e}")
            raise Exception(f"Failed to truncate table: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after truncating table.")

    def create_partitioned_table(self, table_name: str, schema: RDSTableSchema, partition_schema: Any) -> bool:
        """
        根据提供的 RDSTableSchema 和分区模式创建新的分区表。

        Args:
            table_name: 新表的名称。
            schema: RDSTableSchema 对象，包含了表名、列定义。
            partition_schema: 定义表如何分区的模式。

        Returns:
            创建是否成功。
        """
        # MySQL分区表创建实现
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            # 从schema中获取列定义，与create_table方法保持一致
            column_definitions = []
            for column in schema.columns:
                col_def = f"`{column.name}` {column.column_type}"

                # 处理附加属性
                if hasattr(column, 'additional_properties') and column.additional_properties:
                    for prop, value in column.additional_properties.items():
                        if prop == "AUTO_INCREMENT" and value:
                            col_def += " AUTO_INCREMENT"
                        elif prop == "NOT NULL" and value:
                            col_def += " NOT NULL"
                        elif prop == "UNIQUE" and value:
                            col_def += " UNIQUE"
                        elif prop == "DEFAULT":
                            if isinstance(value, str) and value != "CURRENT_TIMESTAMP":
                                col_def += f" DEFAULT '{value}'"
                            else:
                                col_def += f" DEFAULT {value}"

                # 处理主键
                if hasattr(column, 'is_primary') and column.is_primary:
                    col_def += " PRIMARY KEY"

                column_definitions.append(col_def)

            # 组合列定义
            columns_def = ", ".join(column_definitions)

            # 处理表选项
            table_options = ""
            if hasattr(schema, 'options') and schema.options:
                options_list = []
                for opt, val in schema.options.items():
                    options_list.append(f"{opt}={val}")
                if options_list:
                    table_options = " " + " ".join(options_list)

            # 处理分区信息
            partition_type = partition_schema.get('type', 'RANGE')
            partition_expr = partition_schema.get('expression', '')
            partition_defs = partition_schema.get('definitions', [])

            partition_clause = f"PARTITION BY {partition_type} ({partition_expr})"

            # 只有当有分区定义时才添加分区子句
            if partition_defs:
                partition_parts = []
                for part in partition_defs:
                    part_name = part.get('name')
                    part_value = part.get('value')
                    if partition_type.upper() == 'RANGE':
                        partition_parts.append(f"PARTITION {part_name} VALUES LESS THAN ({part_value})")
                    elif partition_type.upper() == 'LIST':
                        partition_parts.append(f"PARTITION {part_name} VALUES IN ({part_value})")
                    elif partition_type.upper() == 'HASH':
                        # HASH分区通常不需要VALUES子句
                        partition_parts.append(f"PARTITION {part_name}")

                if partition_parts:
                    partition_clause += " (" + ", ".join(partition_parts) + ")"

            # 组合完整的创建表SQL
            query = f"CREATE TABLE {table_name} ({columns_def}) {table_options} {partition_clause}"

            cursor.execute(query)
            connection.commit()
            logger.info(f"Partitioned table {table_name} created successfully.")
            return True
        except MySQLError as e:
            if connection:
                connection.rollback()
            logger.error(f"Failed to create partitioned table: {e}")
            raise Exception(f"Failed to create partitioned table: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after creating partitioned table.")

    def add_partition(self, table_name: str, partition_definition: Any) -> bool:
        """
        向现有分区表添加新分区。

        Args:
            table_name: 表名。
            partition_definition: 新分区的定义。

        Returns:
            添加分区是否成功。
        """
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            part_name = partition_definition.get('name')
            part_value = partition_definition.get('value')

            query = f"ALTER TABLE {table_name} ADD PARTITION (PARTITION {part_name} VALUES LESS THAN ({part_value}))"

            cursor.execute(query)
            connection.commit()
            logger.info(f"Partition {part_name} added to table {table_name} successfully.")
            return True
        except MySQLError as e:
            if connection:
                connection.rollback()
            logger.error(f"Failed to add partition: {e}")
            raise Exception(f"Failed to add partition: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after adding partition.")

    def drop_partition(self, table_name: str, partition_name: str, if_exists: bool = False) -> bool:
        """
        从分区表中删除一个分区。

        Args:
            table_name: 表名。
            partition_name: 要删除的分区的名称或标识符。
            if_exists: 如果为 True，则仅当分区存在时才删除。

        Returns:
            删除分区是否成功。
        """
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            # MySQL不直接支持IF EXISTS用于分区，需要先检查分区是否存在
            if if_exists:
                check_query = f"SELECT 1 FROM information_schema.partitions WHERE table_schema = %s AND table_name = %s AND partition_name = %s"
                cursor.execute(check_query, (self.database, table_name, partition_name))
                if not cursor.fetchone():
                    return True  # 分区不存在，视为成功

            query = f"ALTER TABLE {table_name} DROP PARTITION {partition_name}"

            cursor.execute(query)
            connection.commit()
            logger.info(f"Partition {partition_name} dropped from table {table_name} successfully.")
            return True
        except MySQLError as e:
            if connection:
                connection.rollback()
            logger.error(f"Failed to drop partition: {e}")
            raise Exception(f"Failed to drop partition: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after dropping partition.")

    def list_partitions(self, table_name: str) -> List[Any]:
        """
        列出指定分区表的所有分区信息。

        Args:
            table_name: 表名。

        Returns:
            分区信息列表。
        """
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            query = f"""
            SELECT partition_name, partition_ordinal_position, partition_description
            FROM information_schema.partitions
            WHERE table_schema = %s AND table_name = %s AND partition_name IS NOT NULL
            ORDER BY partition_ordinal_position
            """

            cursor.execute(query, (self.database, table_name))
            results = cursor.fetchall()
            return results
        except MySQLError as e:
            logger.error(f"Failed to list partitions: {e}")
            raise Exception(f"Failed to list partitions: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after listing partitions.")

    def truncate_partition(self, table_name: str, partition_name: str) -> bool:
        """
        清空指定分区中的所有数据，但保留分区结构。

        Args:
            table_name: 表名。
            partition_name: 要清空的分区的名称或标识符。

        Returns:
            操作是否成功。
        """
        connection = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor()

            # MySQL不直接支持TRUNCATE PARTITION，需要使用ALTER TABLE ... TRUNCATE PARTITION
            query = f"ALTER TABLE {table_name} TRUNCATE PARTITION {partition_name}"

            cursor.execute(query)
            connection.commit()
            logger.info(f"Partition {partition_name} in table {table_name} truncated successfully.")
            return True
        except MySQLError as e:
            if connection:
                connection.rollback()
            logger.error(f"Failed to truncate partition: {e}")
            raise Exception(f"Failed to truncate partition: {str(e)}")
        finally:
            if connection:
                connection.close()
                logger.debug("Connection returned to pool after truncating partition.")

    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行自定义查询语句。

        Args:
            query: SQL查询语句。
            params: 查询参数字典。

        Returns:
            查询结果列表，列表中每个字典代表一条记录。
        """
        # 检查是否在事务中
        in_transaction = self.current_connection is not None
        connection = None

        try:
            # 如果在事务中，使用事务连接；否则从池中获取新连接
            if in_transaction:
                connection = self.current_connection
                cursor = connection.cursor()
                logger.debug("Using transaction connection for custom query.")
            else:
                connection = self._get_connection()
                cursor = connection.cursor()
                logger.debug("Using new connection for custom query.")

            # 执行查询
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # 获取结果（如果是SELECT语句）
            if query.strip().upper().startswith("SELECT"):
                results = cursor.fetchall()
                logger.info("Custom query executed successfully.")
                return results
            else:
                # 非SELECT语句，如果不在事务中则提交
                if not in_transaction:
                    connection.commit()
                    logger.info(f"Custom query executed successfully. Affected rows: {cursor.rowcount}")
                else:
                    logger.info(f"Custom query added to transaction. Affected rows: {cursor.rowcount}")
                return []
        except MySQLError as e:
            # 只有非事务连接才需要回滚，事务连接的回滚由rollback()方法处理
            if connection and not in_transaction:
                connection.rollback()
            logger.error(f"Failed to execute custom query: {e}")
            raise Exception(f"Failed to execute custom query: {str(e)}")
        finally:
            # 只有非事务连接才需要关闭，事务连接由commit()/rollback()方法关闭
            if connection and not in_transaction:
                connection.close()
                logger.debug("Connection returned to pool after custom query.")

    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> bool:
        """
        执行自定义命令（非查询语句，如INSERT, UPDATE, DELETE等）。
    
        Args:
            command: SQL命令。
            params: 命令参数字典。
    
        Returns:
            命令执行是否成功。
        """
        # 检查是否在事务中
        in_transaction = self.current_connection is not None
        connection = None

        try:
            # 如果在事务中，使用事务连接；否则从池中获取新连接
            if in_transaction:
                connection = self.current_connection
                cursor = connection.cursor()
                logger.debug("Using transaction connection for custom command.")
            else:
                connection = self._get_connection()
                cursor = connection.cursor()
                logger.debug("Using new connection for custom command.")

            # 执行命令
            if params:
                cursor.execute(command, params)
            else:
                cursor.execute(command)

            # 只有非事务连接才需要提交
            if not in_transaction:
                connection.commit()
                logger.info(f"Custom command executed successfully. Affected rows: {cursor.rowcount}")
            else:
                logger.info(f"Custom command added to transaction. Affected rows: {cursor.rowcount}")

            return True
        except MySQLError as e:
            # 只有非事务连接才需要回滚，事务连接的回滚由rollback()方法处理
            if connection and not in_transaction:
                connection.rollback()
            logger.error(f"Failed to execute custom command: {e}")
            raise Exception(f"Failed to execute custom command: {str(e)}")
        finally:
            # 只有非事务连接才需要关闭，事务连接由commit()/rollback()方法关闭
            if connection and not in_transaction:
                connection.close()
                logger.debug("Connection returned to pool after custom command.")

    def begin_transaction(self) -> None:
        """
        开始一个事务。
        """
        if self.current_connection:
            raise Exception("已有活动事务，请先提交或回滚当前事务")

        try:
            self.current_connection = self._get_connection()
            logger.info("Transaction started.")
        except MySQLError as e:
            logger.error(f"Failed to start transaction: {e}")
            raise Exception(f"Failed to start transaction: {str(e)}")

    def commit(self) -> None:
        """
        提交当前事务。
        """
        if not self.current_connection:
            raise Exception("No active transaction to commit.")

        try:
            self.current_connection.commit()
            logger.info("Transaction committed.")
        except MySQLError as e:
            logger.error(f"Failed to commit transaction: {e}")
            raise Exception(f"Failed to commit transaction: {str(e)}")
        finally:
            self.current_connection.close()
            self.current_connection = None
            logger.debug("Connection returned to pool after commit.")

    def rollback(self) -> None:
        """
        回滚当前事务。
        """
        if not self.current_connection:
            raise Exception("No active transaction to rollback.")

        try:
            self.current_connection.rollback()
            logger.info("Transaction rolled back.")
        except MySQLError as e:
            logger.error(f"Failed to rollback transaction: {e}")
            raise Exception(f"Failed to rollback transaction: {str(e)}")
        finally:
            self.current_connection.close()
            self.current_connection = None
            logger.debug("Connection returned to pool after rollback.")
