'''
File Created: Wednesday, 28th May 2025 2:03:04 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Thursday, 29th May 2025 8:02:55 am
'''

from typing import Any, Dict, List, Optional, Tuple, Union, Type
from ..base.rdb_client import RDBClient
from ..implementations.rdb.mysql.mysql_client import MySQLClient
from ..implementations.rdb.mysql.mysql_sqlalchemy_client import MySQLSQLAlchemyClient
from ..base.schemas import RDSTableSchema


class RDBInterface:
    """
    关系型数据库接口，对外提供统一的数据库操作接口。
    支持多种数据库类型，根据配置选择合适的数据库客户端实现。
    """

    def __init__(self):
        """
        初始化数据库接口。
        注册可用的数据库客户端实现。
        """
        # 注册可用的数据库客户端实现
        self._client_registry: Dict[str, Type[RDBClient]] = {
            "mysql": MySQLClient,
            "mysql_sqlalchemy": MySQLSQLAlchemyClient,
            # 可以添加更多数据库类型的支持
            # "postgresql": PostgreSQLClient,
            # "sqlite": SQLiteClient,
        }
        self.client = None

    def get_client_instance(self, db_type: str, **config) -> RDBClient:
        """
        获取指定数据库类型的客户端实例。

        Args:
            db_type: 数据库类型，如 'mysql', 'postgresql', 'sqlite' 等
            **config: 数据库连接配置，作为可变参数传入

        Returns:
            数据库客户端实例
        """
        db_type = db_type.lower()
        if db_type not in self._client_registry:
            raise ValueError(f"不支持的数据库类型: {db_type}")
        
        return self._client_registry[db_type](**config)
    
    def connect(self, db_type: str, **config) -> None:
        """
        连接到关系型数据库。

        Args:
            db_type: 数据库类型
            **config: 数据库连接配置
        """
        self.client = self.get_client_instance(db_type, **config)
        self.client.connect()

    def disconnect(self) -> None:
        """
        断开与关系型数据库的连接。
        """
        if self.client:
            self.client.disconnect()
            self.client = None
        else:
            raise Exception("没有活动的数据库连接")

    def insert(self, table: str, data: List[Dict[str, Any]]) -> bool:
        """
        在指定表中创建一条或多条新记录。

        Args:
            table: 表名
            data: 要插入的数据。列表中每个元素为一条记录的数据

        Returns:
            插入是否成功
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.insert(table, data)

    def select(
        self,
        table: str,
        columns: Optional[List[str]] = None,
        condition: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        order_by: Optional[Union[str, List[str]]] = None,
    ) -> List[Dict[str, Any]]:
        """
        从指定表中查询记录。
        
        Args:
            table: 表名
            columns: 要查询的列名列表。如果为 None，则选择所有列 (SELECT *)。
            condition: 查询条件字典，键为列名，值为该列需要匹配的值。
            limit: 返回的最大记录数。
            offset: 返回记录的偏移量。
            order_by: 排序依据。可以是单个列名字符串或列名列表。

        Returns:
            查询结果列表，列表中每个字典代表一条记录。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.select(table, columns, condition, limit, offset, order_by)

    def update(self, table: str, filter: dict, data: Dict[str, Any]) -> bool:
        """
        更新指定表中的一条或多条记录。

        Args:
            table: 表名
            filter: 用于指定更新哪些记录的条件字典。
            data: 要更新的数据，键为列名，值为新的列值。

        Returns:
            更新是否成功
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.update(table, filter, data)

    def delete(self, table: str, filter: dict) -> bool:
        """
        删除指定表中的一条或多条记录。

        Args:
            table: 表名
            filter: 用于指定删除哪些记录的条件字典。

        Returns:
            删除是否成功
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.delete(table, filter)

    def create_table(self, table_name: str, schema: RDSTableSchema) -> bool:
        """
        根据提供的 RDSTableSchema 创建新表。

        Args:
            table_name: 新表的名称。
            schema: RDSTableSchema 对象，包含了表名、列定义和可选的表选项。

        Returns:
            创建是否成功。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.create_table(table_name, schema)

    def drop_table(self, table_name: str, if_exists: bool = False) -> bool:
        """
        删除表。

        Args:
            table_name: 表名。
            if_exists: 如果为 True，则仅当表存在时才删除 (例如 SQL 的 IF EXISTS)。

        Returns:
            删除是否成功。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.drop_table(table_name, if_exists)

    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在。

        Args:
            table_name: 表名。

        Returns:
            如果表存在则返回 True，否则返回 False。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.table_exists(table_name)

    def list_tables(self) -> List[str]:
        """
        列出数据库中的所有表。

        Returns:
            表名列表。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.list_tables()

    def truncate_table(self, table_name: str) -> bool:
        """
        清空表中的所有数据，但保留表结构。

        Args:
            table_name: 表名。

        Returns:
            操作是否成功。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.truncate_table(table_name)

    def create_partitioned_table(self, table_name: str, schema: RDSTableSchema, partition_schema: Any) -> bool:
        """
        根据提供的 RDSTableSchema 和分区模式创建新的分区表。

        Args:
            table_name: 新表的名称。
            schema: RDSTableSchema 对象，包含了表名、列定义。
            partition_schema: 定义表如何分区的模式。
                              具体结构可能因数据库而异 (例如，按范围、列表、哈希以及分区键)。

        Returns:
            创建是否成功。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.create_partitioned_table(table_name, schema, partition_schema)

    def add_partition(self, table_name: str, partition_definition: Any) -> bool:
        """
        向现有分区表添加新分区。

        Args:
            table_name: 表名。
            partition_definition: 新分区的定义 (例如，分区的边界或值)。

        Returns:
            添加分区是否成功。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.add_partition(table_name, partition_definition)

    def drop_partition(self, table_name: str, partition_name: str, if_exists: bool = False) -> bool:
        """
        从分区表中删除一个分区。

        Args:
            table_name: 表名。
            partition_name: 要删除的分区的名称或标识符。
            if_exists: 如果为 True，则仅当分区存在时才删除。

        Returns:
            删除分区是否成功。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.drop_partition(table_name, partition_name, if_exists)

    def list_partitions(self, table_name: str) -> List[Any]:
        """
        列出指定分区表的所有分区信息。

        Args:
            table_name: 表名。

        Returns:
            分区信息列表。每个元素的具体结构可能因数据库而异。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.list_partitions(table_name)

    def truncate_partition(self, table_name: str, partition_name: str) -> bool:
        """
        清空指定分区中的所有数据，但保留分区结构。

        Args:
            table_name: 表名。
            partition_name: 要清空的分区的名称或标识符。

        Returns:
            操作是否成功。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.truncate_partition(table_name, partition_name)

    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行自定义查询语句。

        Args:
            query: SQL查询语句
            params: 查询参数

        Returns:
            查询结果列表
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.execute_query(query, params)

    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> int:
        """
        执行自定义命令语句。

        Args:
            command: SQL命令语句
            params: 命令参数

        Returns:
            受影响的行数
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        return self.client.execute_command(command, params)

    def begin_transaction(self) -> None:
        """
        开始一个事务。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        self.client.begin_transaction()

    def commit(self) -> None:
        """
        提交当前事务。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        self.client.commit()

    def rollback(self) -> None:
        """
        回滚当前事务。
        """
        if not self.client:
            raise Exception("没有活动的数据库连接，请先调用connect方法")
        self.client.rollback()
        
    def get_available_db_types(self) -> List[str]:
        """
        获取所有可用的关系型数据库类型
        
        Returns:
            数据库类型列表
        """
        return list(self._client_registry.keys())
    
    def register_client(self, db_type: str, client_class: Type[RDBClient]) -> None:
        """
        注册新的关系型数据库客户端实现
        
        Args:
            db_type: 数据库类型名称
            client_class: 数据库客户端类
        """
        self._client_registry[db_type.lower()] = client_class
