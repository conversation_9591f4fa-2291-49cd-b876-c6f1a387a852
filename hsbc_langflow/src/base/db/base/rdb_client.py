from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple, Union
from .schemas import RDSTableSchema

class RDBClient(ABC):
    """
    关系型数据库客户端的抽象基类。
    定义了与关系型数据库交互的基本操作接口。
    """

    @abstractmethod
    def connect(self) -> None:
        """
        连接到数据库。
        """
        pass

    @abstractmethod
    def disconnect(self) -> None:
        """
        断开与数据库的连接。
        """
        pass

    @abstractmethod
    def insert(self, table: str, data: List[Dict[str, Any]]) -> bool:
        """
        在指定表中创建一条或多条新记录。

        Args:
            table: 表名
            data: 要插入的数据。列表中每个元素为一条记录的数据

        Returns:
            插入是否成功
        """
        pass

    @abstractmethod
    def select(
        self,
        table: str,
        columns: Optional[List[str]] = None,
        condition: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        order_by: Optional[Union[str, List[str]]] = None,
    ) -> List[Dict[str, Any]]:
        """
        从指定表中查询记录。
        复杂查询建议使用execute_query方法
        Args:
            table: 表名
            columns: 要查询的列名列表。如果为 None，则选择所有列 (SELECT *)。
            condition: 查询条件字典，键为列名，值为该列需要匹配的值。
                       例如: {"name": "Alice", "age": 30} 会生成类似 WHERE name = 'Alice' AND age = 30 的条件。
                       更复杂的条件（如 >, <, LIKE）可能需要通过 execute_query 实现或在子类中扩展。
            limit: 返回的最大记录数。
            offset: 返回记录的偏移量。
            order_by: 排序依据。可以是单个列名字符串（例如 "age DESC"）或列名列表（例如 ["name ASC", "age DESC"]）。

        Returns:
            查询结果列表，列表中每个字典代表一条记录。
        """
        pass

    @abstractmethod
    def update(self, table: str, filter: dict, data: Dict[str, Any]) -> bool:
        """
        更新指定表中的一条或多条记录。

        Args:
            table: 表名
            filter: 用于指定更新哪些记录的条件字典。
                    例如: {"id": 123} 或 {"name": "Alice", "status": "active"}
            data: 要更新的数据，键为列名，值为新的列值。

        Returns:
            更新是否成功
        """
        pass


    @abstractmethod
    def delete(self, table: str, filter: dict) -> bool:
        """
        删除指定表中的一条或多条记录。

        Args:
            table: 表名
            filter: 用于指定删除哪些记录的条件字典。
                    例如: {"id": 123} 或 {"status": "inactive"}

        Returns:
            删除是否成功
        """
        pass

    @abstractmethod
    def create_table(self, table_name: str, schema: RDSTableSchema) -> bool:
        """
        根据提供的 RDSTableSchema 创建新表。

        Args:
            table_name: 新表的名称。
            schema: RDSTableSchema 对象，包含了表名、列定义和可选的表选项。

        Returns:
            创建是否成功。
        """
        pass

    @abstractmethod
    def drop_table(self, table_name: str, if_exists: bool = False) -> bool:
        """
        删除表。

        Args:
            table_name: 表名。
            if_exists: 如果为 True，则仅当表存在时才删除 (例如 SQL 的 IF EXISTS)。

        Returns:
            删除是否成功。
        """
        pass

    @abstractmethod
    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在。

        Args:
            table_name: 表名。

        Returns:
            如果表存在则返回 True，否则返回 False。
        """
        pass

    @abstractmethod
    def list_tables(self) -> List[str]:
        """
        列出数据库中的所有表。

        Returns:
            表名列表。
        """
        pass

    @abstractmethod
    def truncate_table(self, table_name: str) -> bool:
        """
        清空表中的所有数据，但保留表结构。

        Args:
            table_name: 表名。

        Returns:
            操作是否成功。
        """
        pass


    def create_partitioned_table(self, table_name: str, schema: RDSTableSchema, partition_schema: Any) -> bool:
        """
        根据提供的 RDSTableSchema 和分区模式创建新的分区表。

        Args:
            table_name: 新表的名称。
            schema: RDSTableSchema 对象，包含了表名、列定义。
            partition_schema: 定义表如何分区的模式。
                              具体结构可能因数据库而异 (例如，按范围、列表、哈希以及分区键)。

        Returns:
            创建是否成功。
        """
        raise NotImplementedError

    def add_partition(self, table_name: str, partition_definition: Any) -> bool:
        """
        向现有分区表添加新分区。

        Args:
            table_name: 表名。
            partition_definition: 新分区的定义 (例如，分区的边界或值)。

        Returns:
            添加分区是否成功。
        """
        raise NotImplementedError


    def drop_partition(self, table_name: str, partition_name: str, if_exists: bool = False) -> bool:
        """
        从分区表中删除一个分区。

        Args:
            table_name: 表名。
            partition_name: 要删除的分区的名称或标识符。
            if_exists: 如果为 True，则仅当分区存在时才删除。

        Returns:
            删除分区是否成功。
        """
        raise NotImplementedError


    def list_partitions(self, table_name: str) -> List[Any]:
        """
        列出指定分区表的所有分区信息。

        Args:
            table_name: 表名。

        Returns:
            分区信息列表。每个元素的具体结构可能因数据库而异。
        """
        raise NotImplementedError

    def truncate_partition(self, table_name: str, partition_name: str) -> bool:
        """
        清空指定分区中的所有数据，但保留分区结构。

        Args:
            table_name: 表名。
            partition_name: 要清空的分区的名称或标识符。

        Returns:
            操作是否成功。
        """
        raise NotImplementedError

    @abstractmethod
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行自定义查询语句。

        Args:
            query: SQL查询语句
            params: 查询参数

        Returns:
            查询结果列表
        """
        pass

    @abstractmethod
    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> int:
        """
        执行自定义命令语句。

        Args:
            command: SQL命令语句
            params: 命令参数

        Returns:
            受影响的行数
        """
        pass

    @abstractmethod
    def begin_transaction(self) -> None:
        """
        开始一个事务。
        """
        pass

    @abstractmethod
    def commit(self) -> None:
        """
        提交当前事务。
        """
        pass

    @abstractmethod
    def rollback(self) -> None:
        """
        回滚当前事务。
        """
        pass