'''
File Created: Wednesday, 28th May 2025 2:03:04 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 2:18:34 am
'''

from dataclasses import dataclass
from typing import Any, Dict, List, Optional

@dataclass
class FieldSchema:
    """字段schema定义"""
    name: str
    field_type: str
    description: str = ""
    is_primary: bool = False
    additional_properties: Optional[Dict[str, Any]] = None


@dataclass
class CollectionSchema:
    """集合模式定义"""
    fields: List[FieldSchema]
    description: str = ""

    def __post_init__(self):
        # 验证字段名称唯一性
        field_names = [field.name for field in self.fields]
        if len(field_names) != len(set(field_names)):
            raise ValueError("字段名称必须唯一")

@dataclass
class ColumnSchema:
    """关系型数据库表的列定义"""
    name: str
    column_type: str
    description: str = ""
    is_primary: bool = False
    additional_properties: Optional[Dict[str, Any]] = None

@dataclass
class RDSTableSchema:
    """关系型数据库的表模式定义"""
    columns: List[ColumnSchema]
    description: str = ""
    # 例如: {"ENGINE": "InnoDB", "CHARSET": "utf8mb4"}
    options: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        # 验证列名唯一性
        column_names = [col.name for col in self.columns]
        if len(column_names) != len(set(column_names)):
            raise ValueError("列名称必须唯一")

@dataclass
class RDBConnectionConfig:
    """关系型数据库连接配置"""
    host: str
    port: int
    user: str
    password: str
    db_name: str


@dataclass
class VDBConnectionConfig:
    """向量数据库连接配置"""
    host: str
    port: int
    user: str
    password: str
    db_name: str