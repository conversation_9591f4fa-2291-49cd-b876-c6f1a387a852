import mysql.connector
from mysql.connector import Error
from config.db_config import mysql_config


def check_create_tables():
    connection = None  # 初始化连接为None
    cursor = None  # 初始化游标为None
    try:
        # 连接到MySQL数据库
        connection = mysql.connector.connect(**mysql_config)

        if connection.is_connected():
            cursor = connection.cursor()

            # 检查并创建 user 表
            cursor.execute("SHOW TABLES LIKE 'user'")
            if not cursor.fetchone():
                cursor.execute("""
                    CREATE TABLE user (
                        user_id VARCHAR(255) PRIMARY KEY COMMENT '用户ID (唯一标识)',
                        user_name VARCHAR(255) NOT NULL COMMENT '用户名称',
                        create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
                    ) COMMENT='用户表';
                """)
                print("Table 'user' created successfully.")
                # 插入默认数据，create_time 由数据库自动填充
                cursor.execute("""
                    INSERT INTO user (user_id, user_name)
                    VALUES ('001', 'test');
                """)
                print("Default user data inserted: user_id='001', user_name='test'.")
            else:
                print("Table 'user' already exists.")

            # 检查并创建 knowledge 表，使用联合主键 (user_id, knowledge_id)
            cursor.execute("SHOW TABLES LIKE 'knowledge'")
            if not cursor.fetchone():
                cursor.execute("""
                    CREATE TABLE knowledge (
                        user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
                        knowledge_id VARCHAR(255) NOT NULL COMMENT '知识库ID',
                        knowledge_name VARCHAR(255) NOT NULL COMMENT '知识库名称',
                        doc_nums INT NOT NULL DEFAULT 0 COMMENT '文档总数',
                        create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                        PRIMARY KEY (user_id, knowledge_id),
                        FOREIGN KEY (user_id) REFERENCES user(user_id)
                    ) COMMENT='知识库表';
                """)
                print("Table 'knowledge' created successfully.")
            else:
                print("Table 'knowledge' already exists.")

            # 检查并创建 doc 表，调整外键引用 knowledge 表的联合主键
            cursor.execute("SHOW TABLES LIKE 'doc'")
            if not cursor.fetchone():
                cursor.execute("""
                    CREATE TABLE doc (
                        user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
                        knowledge_id VARCHAR(255) NOT NULL COMMENT '知识库ID',
                        doc_id VARCHAR(255) PRIMARY KEY COMMENT '文档ID (uuid)',
                        doc_name VARCHAR(255) NOT NULL COMMENT '文档名称',
                        chunk_nums INT NOT NULL DEFAULT 0 COMMENT '分块总数',
                        create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        parse_type VARCHAR(50) NOT NULL COMMENT '解析方式',
                        status VARCHAR(50) NOT NULL COMMENT '解析状态',
                        location VARCHAR(255) NOT NULL COMMENT '文件存储位置',
                        source_type VARCHAR(50) NOT NULL COMMENT '文件格式 (txt, xlsx文档)',
                        metadata TEXT COMMENT '文档源数据',
                        percentage FLOAT NOT NULL DEFAULT 0.0 COMMENT '分块进度',
                        is_use BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否启用',
                        FOREIGN KEY (user_id) REFERENCES user(user_id),
                        FOREIGN KEY (user_id, knowledge_id) REFERENCES knowledge(user_id, knowledge_id)
                    ) COMMENT='文档表';
                """)
                print("Table 'doc' created successfully.")
            else:
                print("Table 'doc' already exists.")

            # 检查并创建 chunk 表，使用联合主键 (chunk_id, doc_id)
            cursor.execute("SHOW TABLES LIKE 'chunk'")
            if not cursor.fetchone():
                cursor.execute("""
                    CREATE TABLE chunk (
                        doc_id VARCHAR(255) NOT NULL COMMENT '文档ID',
                        chunk_id VARCHAR(255) NOT NULL COMMENT '分块ID',
                        content TEXT NOT NULL COMMENT '文档内容',
                        title VARCHAR(255) NOT NULL COMMENT '标题',
                        keywords TEXT COMMENT '关键字段',
                        create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        is_use BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否启用',
                        PRIMARY KEY (chunk_id, doc_id),
                        FOREIGN KEY (doc_id) REFERENCES doc(doc_id)
                    ) COMMENT='分块表';
                """)
                print("Table 'chunk' created successfully.")
            else:
                print("Table 'chunk' already exists.")

            # 提交事务
            connection.commit()
            print("All changes committed successfully.")

    except Error as e:
        print(f"Error: {e}")
        if connection and connection.is_connected():
            connection.rollback()  # 发生错误时回滚事务
            print("Transaction rolled back.")

    finally:
        # 确保游标和连接被正确关闭
        if cursor is not None:
            cursor.close()
            print("Cursor closed.")
        if connection is not None and connection.is_connected():
            connection.close()
            print("MySQL connection closed.")
        else:
            print("No active connection to close.")


if __name__ == "__main__":
    check_create_tables()
