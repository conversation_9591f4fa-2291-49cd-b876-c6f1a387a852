'''
File Created: Wednesday, 28th May 2025 2:03:04 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 2:19:10 am
'''

from typing import Any, Dict, List, Optional, Tuple, Union, Iterable, Generator
from psycopg2.extensions import quote_ident
from contextlib import contextmanager
from psycopg2.extras import execute_values
import psycopg2
from psycopg2.pool import SimpleConnectionPool
from psycopg2.extras import RealDictCursor
from base.db.base.vs_client import VSClient
from base.db.base.schemas import FieldSchema, CollectionSchema, VDBConnectionConfig

# 导入配置中使用的dataclass，以便进行类型提示


class PGVectorClient(VSClient):
    """pgvector向量数据库客户端实现"""

    def __init__(
            self,
            connection: VDBConnectionConfig,
            db_type: str = "pgvector",
            usage: Optional[Dict] = None,
            min_connection: int = 1,
            max_connection: int = 10,
            **kwargs
    ):
        """
        使用结构化的配置对象进行初始化。

        Args:
            connection (VDBConnectionConfig): 包含主机、端口、用户、密码、数据库名的配置对象。
            db_type (str): 数据库类型。
            usage (Optional[Dict]): 用途定义。
            min_connection (int): 连接池最小连接数。
            max_connection (int): 连接池最大连接数。
            **kwargs: 允许未来扩展。
        """
        self.host = connection.host
        self.port = connection.port
        self.db_name = connection.db_name
        self.user = connection.user
        self.password = connection.password
        self.min_connection = min_connection
        self.max_connection = max_connection
        self.pool = None

    def _create_pool(self) -> SimpleConnectionPool:
        """创建并返回数据库连接池"""
        try:
            pool = SimpleConnectionPool(
                minconn=self.min_connection,
                maxconn=self.max_connection,
                dbname=self.db_name,
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password
            )
            print(f"Database connection pool created with {self.min_connection} to {self.max_connection} connections.")
            return pool
        except Exception as e:
            raise ConnectionError(f"无法连接到 PostgreSQL: {str(e)}")

    @contextmanager
    def _get_connection(self) -> Generator[psycopg2.extensions.connection, None, None]:
        """从连接池获取连接，并在使用后归还"""
        conn = self.pool.getconn()
        try:
            yield conn
        finally:
            self.pool.putconn(conn)

    @contextmanager
    def _get_cursor(self) -> Generator[psycopg2.extensions.cursor, None, None]:
        """获取游标，自动处理事务和资源清理"""
        with self._get_connection() as conn:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            try:
                yield cur
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e
            finally:
                cur.close()

    def connect(self) -> None:
        """连接到向量数据库"""
        if self.pool is None:
            self.pool = self._create_pool()
        else:
            print("Already connected to the database.")

    def disconnect(self) -> None:
        """断开与向量数据库的连接"""
        if self.pool is not None:
            self.pool.closeall()
            self.pool = None
            print("Database connection pool closed.")
        else:
            print("No active connection pool to close.")

    def create_collection(
            self,
            collection_name: str,
            schema: CollectionSchema,
            metric_type: Optional[str] = "COSINE",
            partition_names: Optional[List[str]] = None
    ) -> bool:
        """创建新的集合（表）"""
        try:
            # 验证metric_type
            valid_metrics = {"COSINE", "L2", "IP"}
            if metric_type not in valid_metrics:
                raise ValueError(f"不支持的metric_type: {metric_type}, 必须是 {valid_metrics}")

            # 检查集合是否已存在
            if self.has_collection(collection_name):
                print(f"创建集合失败: <{collection_name}> 已存在")
                return False

            # 准备字段列表
            fields = schema.fields or []

            # 检查是否已有 partition_key
            has_partition_key = any(field.name == "partition_key" for field in fields)
            if not has_partition_key:
                fields.append(FieldSchema(
                    name="partition_key",
                    field_type="varchar",
                    description="分区字段",
                    additional_properties={"label": "not null", "max_length": 200}
                ))

            # 构造列定义并收集主键字段和注释
            columns = []
            column_comments = []
            primary_key_fields = []
            vector_field = None
            for field in fields:
                name = field.name
                dtype = field.field_type.lower()
                col_def = f"{name} "

                # 处理字段类型
                if dtype == "vector":
                    if field.additional_properties.get("max_length", None) is None:
                        raise ValueError(f"向量字段 {field.name} 必须指定维度")
                    dimension = field.additional_properties.get("max_length", 768)
                    col_def += f"vector({dimension})"
                    vector_field = field.name
                elif dtype == "varchar":
                    length = field.additional_properties.get("max_length", 255) if field.additional_properties else 255
                    col_def += f"VARCHAR({length})"
                elif dtype in {"int", "integer"}:
                    col_def += "INTEGER"
                elif dtype == "float":
                    col_def += "FLOAT"
                elif dtype == "text":
                    col_def += "TEXT"
                elif dtype == "json":
                    col_def += "JSON"
                else:
                    raise ValueError(f"不支持的字段类型: {field.field_type}")

                # 处理label（约束）
                if field.additional_properties and "label" in field.additional_properties:
                    label = field.additional_properties["label"]
                    if label.lower() == "not null":
                        col_def += " NOT NULL"
                    elif label:
                        col_def += f" {label}"

                # 主键
                if field.is_primary:
                    primary_key_fields.append(name)

                columns.append(col_def)
                if field.description:
                    column_comments.append((collection_name, name, field.description))

            # 处理主键
            if not primary_key_fields:
                print("警告: 未指定主键，表将不包含主键约束")
            else:
                if "partition_key" not in primary_key_fields:
                    primary_key_fields.append("partition_key")
                columns.append(f"PRIMARY KEY ({', '.join(primary_key_fields)})")

            # 设置默认分区名称
            if partition_names is None:
                partition_names = ["9888"]

            # 构造CREATE TABLE SQL，始终使用PARTITION BY LIST (partition_key)
            columns_sql = ", ".join(columns)
            create_table_sql = f"CREATE TABLE {collection_name} ({columns_sql}) PARTITION BY LIST (partition_key)"
            # print(create_table_sql)

            with self._get_cursor() as cur:
                # 创建表
                cur.execute(create_table_sql)

                # 添加列注释
                for table, col, comment in column_comments:
                    cur.execute(f"COMMENT ON COLUMN {table}.{col} IS %s", (comment,))

                # 添加表注释
                if schema.description:
                    cur.execute(f"COMMENT ON TABLE {collection_name} IS %s", (schema.description,))

            # 创建分区
            for partition_name in partition_names:
                self.create_partition(collection_name.lower(), partition_name)

            print(f"<{collection_name}> 创建成功")
            return True
        except Exception as e:
            print(f"创建集合失败: {str(e)}")
            return False

    def drop_collection(self, collection_name: str) -> bool:
        """删除集合（表）"""
        try:
            with self._get_cursor() as cur:
                cur.execute(f"DROP TABLE IF EXISTS {collection_name} CASCADE")
                # cur.execute("DELETE FROM collection_metadata WHERE collection_name = %s", (collection_name,))
            return True
        except Exception as e:
            print(f"删除集合失败: {str(e)}")
            return False

    def has_collection(self, collection_name: str) -> bool:
        """检查集合是否存在"""
        try:
            with self._get_cursor() as cur:
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    )
                """, (collection_name,))
                return cur.fetchone()['exists']
        except Exception as e:
            print(f"检查集合失败: {str(e)}")
            return False

    def list_collections(self) -> List[str]:
        """列出所有集合"""
        try:
            with self._get_cursor() as cur:
                cur.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                    AND table_name != 'collection_metadata'
                """)
                return [row['table_name'] for row in cur.fetchall()]
        except Exception as e:
            print(f"列出集合失败: {str(e)}")
            return []

    def create_partition(self, collection_name: str, partition_name: str) -> bool:
        """创建分区（子表）"""
        try:
            with self._get_cursor() as cur:
                # 检查集合是否存在
                if not self.has_collection(collection_name):
                    raise ValueError(f"集合 {collection_name} 不存在")

                # 查询现有分区
                cur.execute("""
                    SELECT 
                        p.relname, 
                        pg_get_expr(p.relpartbound, p.oid)
                    FROM pg_class t
                    JOIN pg_partitioned_table pt ON t.oid = pt.partrelid
                    JOIN pg_inherits i ON i.inhparent = t.oid
                    JOIN pg_class p ON p.oid = i.inhrelid
                    WHERE t.relname = %s 
                        AND t.relkind = 'p'
                        AND p.relispartition
                """, (collection_name,))
                partitions = cur.fetchall()

                # 提取分区值和表名
                existing_partitions = {}
                for part_name, partbound in partitions:
                    if "FOR VALUES IN" in partbound:
                        try:
                            value = partbound.split("FOR VALUES IN (")[1].strip(")").strip("'")
                            existing_partitions[value] = part_name
                        except (IndexError, ValueError) as e:
                            print(f"警告: 无法解析分区边界 '{partbound}': {str(e)}")
                            continue

                # 检查分区值是否已存在
                if partition_name in existing_partitions:
                    print(f"分区 {partition_name} 已存在，分区值: {partition_name}")
                    return True

                # 创建子分区
                expected_partition_name = f"{collection_name}_partition_for_{partition_name}"
                print(f"正在创建分区 {expected_partition_name} 对于 partition_key={partition_name}...")

                safe_partition_name = quote_ident(expected_partition_name, cur)
                safe_collection_name = quote_ident(collection_name, cur)
                sql = f"""
                    CREATE TABLE {safe_partition_name} 
                    PARTITION OF {safe_collection_name} 
                    FOR VALUES IN (%s)
                """
                cur.execute(sql, (partition_name,))

            print(f"分区 {expected_partition_name} 创建成功")
            return True
        except Exception as e:
            print(f"创建分区失败: {str(e)}")
            return False

    def drop_partition(self, collection_name: str, partition_name: str) -> bool:
        """删除分区（子表）"""
        try:
            with self._get_cursor() as cur:
                # 检查集合是否存在
                if not self.has_collection(collection_name):
                    print(f"删除分区失败: 集合 {collection_name} 不存在")
                    return False

                # 构造分区表名
                expected_partition_name = f"{collection_name}_partition_for_{partition_name}"

                # 检查分区是否存在
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM pg_inherits 
                        WHERE inhparent = (SELECT oid FROM pg_class WHERE relname = %s)
                        AND inhrelid = (SELECT oid FROM pg_class WHERE relname = %s)
                    )
                """, (collection_name, expected_partition_name))
                if not cur.fetchone()['exists']:
                    print(f"删除分区失败: 分区 {expected_partition_name} 不存在")
                    return False

                # 删除分区
                safe_partition_name = quote_ident(expected_partition_name, cur)
                cur.execute(f"DROP TABLE IF EXISTS {safe_partition_name} CASCADE")

            print(f"分区 {expected_partition_name} 删除成功")
            return True
        except Exception as e:
            print(f"删除分区失败: {str(e)}")
            return False

    def has_partition(self, collection_name: str, partition_name: str) -> bool:
        """检查分区是否存在"""
        try:
            with self._get_cursor() as cur:
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM pg_inherits 
                        WHERE inhparent = (SELECT oid FROM pg_class WHERE relname = %s)
                        AND inhrelid = (SELECT oid FROM pg_class WHERE relname = %s)
                    )
                """, (collection_name, partition_name))
                return cur.fetchone()['exists']
        except Exception as e:
            print(f"检查分区失败: {str(e)}")
            return False

    def list_partitions(self, collection_name: str) -> List[str]:
        """列出集合的所有分区"""
        try:
            with self._get_cursor() as cur:
                cur.execute("""
                    SELECT c.relname 
                    FROM pg_inherits i
                    JOIN pg_class c ON i.inhrelid = c.oid
                    WHERE i.inhparent = (SELECT oid FROM pg_class WHERE relname = %s)
                """, (collection_name,))
                return [row['relname'] for row in cur.fetchall()]
        except Exception as e:
            print(f"列出分区失败: {str(e)}")
            return []

    def insert(self, collection_name: str, data: List[Dict], partition_name: Optional[str] = None) -> bool:
        """插入向量数据"""
        try:
            # 默认分区名为 "9888"
            if partition_name is None:
                partition_name = "9888"
            # partition_name = partition_name or "9888"
            partition_table_name = f"{collection_name}_partition_for_{partition_name}"

            # 检查表是否存在
            if not self.has_collection(collection_name):
                print(f"插入失败: 表 <{collection_name}> 不存在")
                return False

            # 转换为列表格式
            # data_list = [data] if isinstance(data, dict) else data
            data_list = data
            if not data_list:
                print("插入失败: 数据为空")
                return True

            # 检查或创建分区
            if not self.has_partition(collection_name, partition_table_name):
                print(f"分区 {partition_table_name} 不存在，正在创建...")
                if not self.create_partition(collection_name, partition_name):
                    print(f"插入失败: 创建分区 {partition_table_name} 失败")
                    return False
                print(f"分区 {partition_table_name} 创建成功")

            with self._get_cursor() as cur:
                # 获取字段名并添加 partition_key
                columns = list(data_list[0].keys())
                if "partition_key" not in columns:
                    columns.append("partition_key")

                # 准备插入数据
                values = []
                for item in data_list:
                    row = [item.get(col) for col in columns if col != "partition_key"]
                    row.append(partition_name)  # 添加 partition_key 值
                    values.append(row)

                # 构造插入语句
                columns_str = ", ".join(columns)
                safe_table_name = quote_ident(collection_name, cur)
                execute_values(
                    cur,
                    f"INSERT INTO {safe_table_name} ({columns_str}) VALUES %s",
                    values
                )

            print(f"成功插入 {len(data_list)} 条数据到 {collection_name}，partition_key={partition_name}")
            return True
        except Exception as e:
            print(f"插入数据失败: {str(e)}")
            return False

    def delete(self, collection_name: str, filter: Dict, partition_name: Optional[str] = None) -> bool:
        """从指定集合中删除向量数据

        Args:
            collection_name (str): 集合名称
            filter (Dict): 过滤条件，包含 field_name, op, val
            partition_name (Optional[str]): 分区名称，默认为 None

        Returns:
            bool: 删除操作是否成功
        """
        try:
            # 确定表名
            table_name = f"{collection_name}_partition_for_{partition_name}" if partition_name else collection_name

            # 检查表是否存在
            if not self.has_collection(collection_name):
                print(f"删除失败: 表 {collection_name} 不存在")
                return False

            # 如果指定了分区，检查分区是否存在
            if partition_name and not self.has_partition(collection_name, table_name):
                print(f"删除失败: 分区 {table_name} 不存在")
                return False

            # 验证 filter 格式
            if not all(key in filter for key in ['field_name', 'op', 'val']):
                print("删除失败: filter 格式错误，需包含 'field_name', 'op', 'val'")
                return False

            # 构建过滤条件
            field_name = filter['field_name']
            op = filter['op'].lower()
            val = filter['val']

            # 支持的操作符
            valid_operators = {'=', '>', '<', '>=', '<=', '!=', 'in'}
            if op not in valid_operators:
                print(f"删除失败: 不支持的操作符 {op}，必须是 {valid_operators}")
                return False

            # 处理值类型
            if op == 'in':
                if not isinstance(val, (list, tuple)) or not val:
                    print(f"删除失败: IN 操作符需要非空的列表或元组，当前值为 {val}")
                    return False
                placeholders = ", ".join(["%s"] * len(val))
                condition = f"{field_name} IN ({placeholders})"
                params = list(val)
            else:
                if isinstance(val, (list, tuple)):
                    print(f"删除失败: {op} 操作符不支持列表或元组值，当前值为 {val}")
                    return False
                condition = f"{field_name} {op} %s"
                params = [val]

            where_clause = f" WHERE {condition}"

            with self._get_cursor() as cur:
                safe_table_name = quote_ident(table_name, cur)
                delete_sql = f"DELETE FROM {safe_table_name}{where_clause}"
                # print(f"执行 SQL: {delete_sql} 参数: {params}")
                cur.execute(delete_sql, params)

            # print(f"从 {table_name} 删除数据成功")
            return True
        except Exception as e:
            print(f"删除数据失败: {str(e)}")
            return False

    def search(
            self,
            collection_name: str,
            vector: Union[List[list], list],
            top_k: int = 10,
            metric_type: Optional[str] = None,
            filter: Optional[str] = None,
            partition_names: Optional[List[str]] = None,
            search_params: Optional[Dict[str, Any]] = None
    ) -> List[List[Dict[str, Any]]]:
        """在指定集合中搜索与查询向量最相似的向量"""
        try:
            # 检查集合是否存在
            if not self.has_collection(collection_name):
                print(f"搜索失败: 集合 {collection_name} 不存在")
                return []

            # 默认 metric_type
            metric_type = metric_type or "cosine"
            metric_op = {"l2": "<->", "cosine": "<=>", "ip": "<#>"}
            if metric_type not in metric_op:
                print(f"搜索失败: 不支持的 metric_type: {metric_type}, 必须是 {list(metric_op.keys())}")
                return []

            # 获取向量字段名
            vector_field = search_params.get("vector_field") if search_params else None
            if not vector_field:
                with self._get_cursor() as cur:
                    cur.execute("""
                        SELECT column_name 
                        FROM information_schema.columns 
                        WHERE table_name = %s AND data_type = 'USER-DEFINED'
                    """, (collection_name,))
                    vector_field_row = cur.fetchone()
                    if not vector_field_row:
                        print(f"搜索失败: 表 {collection_name} 中未找到向量字段")
                        return []
                    vector_field = vector_field_row['column_name']

            # 处理输出字段
            output_fields = search_params.get("output_fields", ["id"]) if search_params else ["id"]
            if not output_fields:
                print("搜索失败: output_fields 不能为空")
                return []

            # 处理查询向量
            query_vectors = [vector] if isinstance(vector[0], float) else vector
            results = []

            # 确定搜索的表
            tables = [f"{collection_name}_partition_for_{p}" for p in partition_names] if partition_names else [
                collection_name]

            for vector in query_vectors:
                table_results = []
                for table in tables:
                    # 检查表/分区是否存在
                    with self._get_cursor() as cur:
                        cur.execute("""
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_name = %s
                            )
                        """, (table,))
                        if not cur.fetchone()['exists']:
                            print(f"警告: 表或分区 {table} 不存在，跳过")
                            continue

                        # 构建 WHERE 条件
                        where_conditions = []
                        params = []
                        if filter:
                            where_conditions.append(filter)
                        if table != collection_name:  # 分区表
                            partition_value = table.split("_partition_for_")[-1]
                            where_conditions.append("partition_key = %s")
                            params.append(partition_value)

                        where_clause = f" WHERE {' AND '.join(where_conditions)}" if where_conditions else ""

                        # 构造查询
                        safe_table_name = quote_ident(table, cur)
                        query = (
                            f"SELECT {', '.join(output_fields)}, "
                            f"{vector_field} {metric_op[metric_type]} %s::vector AS distance "
                            f"FROM {safe_table_name} "
                            f"{where_clause} "
                            f"ORDER BY distance LIMIT %s"
                        )
                        # 修正参数顺序：vector 用于向量运算，params 用于 where_clause，top_k 用于 LIMIT
                        all_params = [vector] + params + [top_k]

                        # 执行查询
                        cur.execute(query, all_params)
                        table_results.extend(
                            {
                                "distance": row["distance"],
                                **{field: row[field] for field in output_fields}
                            }
                            for row in cur.fetchall()
                        )

                # 合并并排序结果
                table_results.sort(key=lambda x: x['distance'])
                results.extend(table_results[:top_k])

            return results

        except Exception as e:
            print(f"搜索失败: {str(e)}")
            return []

    def count(self, collection_name: str, filter: Optional[Dict] = None, partition_name: Optional[str] = None) -> int:
        """计算指定集合中的向量数量"""
        try:
            # 确定表名
            table_name = f"{collection_name}_partition_for_{partition_name}" if partition_name else collection_name

            # 检查表是否存在
            if not self.has_collection(collection_name):
                print(f"计数失败: 表 {collection_name} 不存在")
                return 0

            # 如果指定了分区，检查分区是否存在
            if partition_name and not self.has_partition(collection_name, table_name):
                print(f"计数失败: 分区 {table_name} 不存在")
                return 0

            # 构建过滤条件
            where_clause = ""
            params = []
            if filter:
                # 验证 filter 格式
                if not all(key in filter for key in ['field_name', 'op', 'val']):
                    print("计数失败: filter 格式错误，需包含 'field_name', 'op', 'val'")
                    return 0

                field_name = filter['field_name']
                op = filter['op']
                val = filter['val']

                # 支持的操作符
                valid_operators = {'=', '>', '<', '>=', '<=', '!='}
                if op not in valid_operators:
                    print(f"计数失败: 不支持的操作符 {op}，必须是 {valid_operators}")
                    return 0

                # 处理值类型
                if isinstance(val, list):
                    if op != '=':
                        print(f"计数失败: 列表值仅支持 '=' 操作符，当前操作符为 {op}")
                        return 0
                    condition = f"{field_name} = ANY(%s)"
                    params = [val]
                else:
                    condition = f"{field_name} {op} %s"
                    params = [val]

                where_clause = f" WHERE {condition}"

            with self._get_cursor() as cur:
                safe_table_name = quote_ident(table_name, cur)
                count_sql = f"SELECT COUNT(*) FROM {safe_table_name}{where_clause}"
                cur.execute(count_sql, params)
                return cur.fetchone()['count']
        except Exception as e:
            print(f"计数失败: {str(e)}")
            return 0

    def create_index(self, collection_name: str, index_type: str, params: Dict[str, Any],
                     partition_name: Optional[str] = None) -> bool:
        """创建索引"""
        try:
            # 验证索引类型
            if index_type not in {"HNSW", "IVFFLAT"}:
                raise ValueError(f"不支持的索引类型: {index_type}, 必须是 'HNSW' 或 'IVFFLAT'")

            # 确定表名
            if partition_name:
                table_name = f"{collection_name}_partition_for_{partition_name}"
            else:
                table_name = collection_name

            # 检查表或分区是否存在
            with self._get_cursor() as cur:
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    )
                """, (table_name,))
                if not cur.fetchone()['exists']:
                    print(f"创建索引失败: 表或分区 {table_name} 不存在")
                    return False

                # 获取向量字段名,pg数据库向量化字段data_type = 'USER-DEFINED'
                cur.execute("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = %s AND data_type = 'USER-DEFINED'
                """, (table_name,))
                vector_field = cur.fetchone()
                print(vector_field)
                if not vector_field:
                    raise ValueError(f"表 {table_name} 中未找到向量字段")
                vector_field = vector_field['column_name']

            # 获取metric_type（假设从create_collection的调用上下文获取，这里暂时硬编码，实际应通过参数传递）
            metric_type = params.get("metric_type", "COSINE").upper()
            valid_metrics = {"COSINE", "L2", "IP"}
            if metric_type not in valid_metrics:
                raise ValueError(f"不支持的metric_type: {metric_type}, 必须是 {valid_metrics}")

            # 构建索引语句
            operator_class = {"COSINE": "vector_cosine_ops", "L2": "vector_l2_ops", "IP": "vector_ip_ops"}[metric_type]
            index_name = f"{table_name}_{vector_field}_idx"

            with self._get_cursor() as cur:
                safe_table_name = quote_ident(table_name, cur)
                safe_index_name = quote_ident(index_name, cur)

                if index_type == "HNSW":
                    m = params.get("m", 16)
                    ef_construction = params.get("ef_construction", 64)
                    # 验证参数
                    if not (2 <= m <= 100):
                        raise ValueError(f"HNSW 参数 m 必须在 [2, 100] 范围内，当前值: {m}")
                    if not (10 <= ef_construction <= 200):
                        raise ValueError(f"HNSW 参数 ef_construction 必须在 [10, 200] 范围内，当前值: {ef_construction}")
                    index_sql = f"""
                        CREATE INDEX {safe_index_name} ON {safe_table_name}
                        USING hnsw ({vector_field} {operator_class})
                        WITH (m = {m}, ef_construction = {ef_construction})
                    """
                else:  # IVFFLAT
                    lists = params.get("lists", 100)
                    # 验证参数
                    if not (1 <= lists <= 1000):
                        raise ValueError(f"IVFFLAT 参数 lists 必须在 [1, 1000] 范围内，当前值: {lists}")
                    index_sql = f"""
                        CREATE INDEX {safe_index_name} ON {safe_table_name}
                        USING ivfflat ({vector_field} {operator_class})
                        WITH (lists = {lists})
                    """

                # 执行索引创建
                cur.execute(index_sql)

            print(f"索引 {index_name} 创建成功")
            return True
        except Exception as e:
            print(f"创建索引失败: {str(e)}")
            return False

    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """执行自定义查询"""
        try:
            with self._get_cursor() as cur:
                cur.execute(query, params or ())
                if cur.description:
                    return [dict(row) for row in cur.fetchall()]
                return None
        except Exception as e:
            print(f"执行查询失败: {str(e)}")
            raise

    def load_collection(self, collection_name: str, partition_names: Optional[List[str]] = None) -> bool:
        """将集合加载到内存（pgvector无需显式加载）"""
        return True

    def release_collection(self, collection_name: str, partition_names: Optional[List[str]] = None) -> bool:
        """释放集合（pgvector无需显式释放）"""
        return True

    def hybrid_search(
            self,
            collection_name: str,
            query_vectors: List[List[float]],
            query_text: Optional[str] = None,
            field_name: Optional[str] = None,
            metadata_filter: Optional[Dict[str, Any]] = None,
            top_k: int = 10,
            vector_weight: float = 0.5,
            text_weight: float = 0.5,
            partition_names: Optional[List[str]] = None
    ) -> List[List[Dict[str, Any]]]:
        """混合搜索（向量+文本）"""
        try:
            # 获取metric_type
            with self._get_cursor() as cur:
                cur.execute("SELECT metric_type FROM collection_metadata WHERE collection_name = %s",
                            (collection_name,))
                meta = cur.fetchone()
                if not meta:
                    raise ValueError(f"集合 {collection_name} 不存在")
                metric_type = meta['metric_type']

            operator = {"COSINE": "<=>", "L2": "<->", "IP": "<#>"}[metric_type]
            tables = [collection_name]
            if partition_names:
                tables.extend(partition_names)

            # 构建过滤条件
            conditions = []
            params = []
            if metadata_filter:
                for key, value in metadata_filter.items():
                    conditions.append(f"{key} = %s")
                    params.append(value)
            if query_text and field_name:
                conditions.append(f"{field_name} ILIKE %s")
                params.append(f"%{query_text}%")

            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

            results = []
            for vector in query_vectors:
                with self._get_cursor() as cur:
                    table_results = []
                    for table in tables:
                        query = f"""
                            SELECT *, 
                                vector_field {operator[metric_type]} %s AS vector_distance,
                                {(f'1.0 - ({field_name} <-> %s)' if query_text and field_name else '0')} AS text_distance
                            FROM {table}
                            {where_clause}
                            ORDER BY ({vector_weight} * vector_distance + {text_weight} * text_distance)
                            LIMIT {top_k}
                        """
                        query_params = [vector] + ([query_text] if query_text and field_name else []) + params
                        cur.execute(query, query_params)
                        table_results.extend([dict(row) for row in cur.fetchall()])

                    # 合并并排序结果
                    table_results.sort(
                        key=lambda x: vector_weight * x['vector_distance'] + text_weight * x['text_distance'])
                    results.append(table_results[:top_k])

            return results
        except Exception as e:
            print(f"混合搜索失败: {str(e)}")
            return []

    def update(self, collection_name: str, filter: Dict, data: Dict,
               partition_name: Optional[str] = None) -> bool:
        """更新指定集合中的向量数据"""
        try:
            # 确定表名
            table_name = f"{collection_name}_partition_for_{partition_name}" if partition_name else collection_name

            # 检查表是否存在
            if not self.has_collection(collection_name):
                print(f"更新失败: 表 {collection_name} 不存在")
                return False

            # 如果指定了分区，检查分区是否存在
            if partition_name and not self.has_partition(collection_name, table_name):
                print(f"更新失败: 分区 {table_name} 不存在")
                return False

            # 检查数据是否为空
            if not data:
                print("更新失败: 数据为空")
                return True

            # 验证 filter 格式
            if not all(key in filter for key in ['field_name', 'op', 'val']):
                print("更新失败: filter 格式错误，需包含 'field_name', 'op', 'val'")
                return False

            # 获取表中的列名
            with self._get_cursor() as cur:
                cur.execute("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = %s
                """, (table_name,))
                valid_columns = {row['column_name'] for row in cur.fetchall()}

            # 验证 filter 的 field_name 是否有效
            field_name = filter['field_name']
            if field_name not in valid_columns:
                print(f"更新失败: filter 的 field_name '{field_name}' 不是表 {table_name} 的有效列")
                return False

            # 构建过滤条件
            op = filter['op'].upper()
            val = filter['val']
            valid_operators = {'=', '>', '<', '>=', '<=', '!=', 'BETWEEN'}
            if op not in valid_operators:
                print(f"更新失败: 不支持的操作符 {op}，必须是 {valid_operators}")
                return False

            params = []
            if op == 'BETWEEN':
                if not isinstance(val, list) or len(val) != 2:
                    print(f"更新失败: BETWEEN 操作符的 val 必须是包含两个值的列表")
                    return False
                where_clause = f" WHERE {field_name} BETWEEN %s AND %s"
                params.extend([val[0], val[1]])
            elif isinstance(val, list):
                if op != '=':
                    print(f"更新失败: 列表值仅支持 '=' 操作符，当前操作符为 {op}")
                    return False
                where_clause = f" WHERE {field_name} = ANY(%s)"
                params.append(val)
            else:
                where_clause = f" WHERE {field_name} {op} %s"
                params.append(val)

            # 构建更新字段
            update_fields = list(data.keys())
            if "partition_key" in update_fields:
                update_fields.remove("partition_key")  # 不允许更新 partition_key
            if not update_fields:
                print("更新失败: 没有有效的更新字段")
                return False

            # 验证更新字段是否有效
            for field in update_fields:
                if field not in valid_columns:
                    print(f"更新失败: 更新字段 '{field}' 不是表 {table_name} 的有效列")
                    return False

            set_clause = ", ".join([f"{field} = %s" for field in update_fields])

            with self._get_cursor() as cur:
                safe_table_name = quote_ident(table_name, cur)
                # 验证数据项是否包含所有更新字段
                for field in update_fields:
                    if field not in data:
                        print(f"更新失败: 数据项缺少字段 {field}")
                        return False

                    update_values = [data[field] for field in update_fields]
                    update_sql = f"""
                        UPDATE {safe_table_name}
                        SET {set_clause}
                        {where_clause}
                    """
                    # print(f"执行更新: {update_sql}")
                    # print(f"更新参数: {update_values + params}")
                    cur.execute(update_sql, update_values + params)

            print(f"成功更新 {len(data)} 条数据在 {table_name}")
            return True
        except Exception as e:
            print(f"更新数据失败: {str(e)}")
            return False

