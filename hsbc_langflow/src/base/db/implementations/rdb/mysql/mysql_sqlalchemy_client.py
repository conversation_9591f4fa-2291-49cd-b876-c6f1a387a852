'''
File Created: Wednesday, 28th May 2025 2:27:58 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 10:21:28 am
'''

from sqlalchemy import create_engine, text, MetaData, Table, Column, inspect
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.engine import Engine
from base.db.base.rdb_client import RDBClient
from base.db.base.schemas import RDSTableSchema, RDBConnectionConfig
from typing import Any, Dict, List, Optional, Tuple, Union
from loguru import logger
from urllib.parse import quote_plus

# 导入配置中使用的dataclass，以便进行类型提示




class MySQLSQLAlchemyClient(RDBClient):
    """MySQL 客户端实现，使用 SQLAlchemy ORM"""

    def __init__(self, connection: RDBConnectionConfig, db_type: str = "mysql", usage: Optional[Dict] = None, **kwargs):
        """
        使用结构化的配置对象进行初始化。
        
        Args:
            connection (RDBConnectionConfig): 包含主机、端口、用户、密码、数据库名的配置对象。
            db_type (str): 数据库类型，从配置中传入。
            usage (Optional[Dict]): 用途定义，从配置中传入。
            **kwargs: 允许未来扩展。
        """
        logger.info("Initializing MySQL SQLAlchemy client...")
        # 从结构化配置中解包连接参数
        self.host = connection.host
        self.port = connection.port
        self.user = connection.user
        self.password = connection.password
        self.database = connection.db_name
        self.charset = "utf8mb4"
        
        # SQLAlchemy 相关属性
        self.engine: Optional[Engine] = None
        self.SessionLocal: Optional[sessionmaker] = None
        self.current_session: Optional[Session] = None
        self.metadata = MetaData()

    def connect(self) -> None:
        """连接到数据库"""
        try:
            # 对用户名和密码进行URL编码，处理特殊字符（如@符号）
            encoded_user = quote_plus(self.user)
            encoded_password = quote_plus(self.password)
            
            # 构建连接URL
            connection_url = f"mysql+pymysql://{encoded_user}:{encoded_password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
            
            # 创建引擎，配置连接池
            self.engine = create_engine(
                connection_url,
                poolclass=QueuePool,
                pool_size=5,
                max_overflow=10,
                pool_pre_ping=True,
                echo=False  # 设置为True可以看到SQL语句
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            logger.info("MySQL SQLAlchemy connection established successfully.")
        except SQLAlchemyError as e:
            logger.error(f"Failed to connect to MySQL: {e}")
            raise Exception(f"Failed to connect to MySQL: {str(e)}")

    def disconnect(self) -> None:
        """断开与数据库的连接"""
        try:
            # 关闭当前会话
            if self.current_session:
                self.current_session.close()
                self.current_session = None
            
            # 关闭引擎
            if self.engine:
                self.engine.dispose()
                self.engine = None
                
            self.SessionLocal = None
            logger.info("MySQL SQLAlchemy connection closed successfully.")
        except Exception as e:
            logger.error(f"Error closing connection: {e}")
            self.engine = None
            self.SessionLocal = None

    def _get_session(self) -> Session:
        """获取数据库会话"""
        if self.current_session:
            # 如果在事务中，返回当前会话
            return self.current_session
        else:
            # 否则创建新会话
            if not self.SessionLocal:
                raise Exception("Database not connected. Call connect() first.")
            return self.SessionLocal()

    def insert(self, table: str, data: List[Dict[str, Any]]) -> bool:
        """
        在指定表中创建一条或多条新记录。

        Args:
            table: 表名
            data: 要插入的数据。列表中每个元素为一条记录的数据

        Returns:
            插入是否成功
        """
        if not data:
            return False

        # 检查是否在事务中
        in_transaction = self.current_session is not None
        session = None

        try:
            if in_transaction:
                session = self.current_session
                logger.debug("Using transaction session for INSERT.")
            else:
                session = self._get_session()
                logger.debug("Using new session for INSERT.")

            # 使用原生SQL进行批量插入
            for record in data:
                columns = ', '.join([f"`{col}`" for col in record.keys()])
                placeholders = ', '.join([f":{col}" for col in record.keys()])
                query = text(f"INSERT INTO `{table}` ({columns}) VALUES ({placeholders})")
                session.execute(query, record)

            # 只有非事务会话才需要提交
            if not in_transaction:
                session.commit()
                logger.info(f"INSERT operation completed for {len(data)} records in {table}.")
            else:
                logger.info(f"INSERT operation added to transaction for {len(data)} records in {table}.")

            return True
        except SQLAlchemyError as e:
            # 只有非事务会话才需要回滚
            if session and not in_transaction:
                session.rollback()
            logger.error(f"Failed to execute INSERT: {e}")
            raise Exception(f"Failed to execute INSERT: {str(e)}")
        finally:
            # 只有非事务会话才需要关闭
            if session and not in_transaction:
                session.close()
                logger.debug("Session closed after INSERT.")

    def select(
            self,
            table: str,
            columns: Optional[List[str]] = None,
            condition: Optional[Dict[str, Any]] = None,
            limit: Optional[int] = None,
            offset: Optional[int] = None,
            order_by: Optional[Union[str, List[str]]] = None,
    ) -> List[Dict[str, Any]]:
        """
        从指定表中查询记录。
        
        Args:
            table: 表名
            columns: 要查询的列名列表。如果为 None，则选择所有列 (SELECT *)。
            condition: 查询条件字典，键为列名，值为该列需要匹配的值。
            limit: 返回的最大记录数。
            offset: 返回记录的偏移量。
            order_by: 排序依据。可以是单个列名字符串或列名列表。
        
        Returns:
            查询结果列表，列表中每个字典代表一条记录。
        """
        # 检查是否在事务中
        in_transaction = self.current_session is not None
        session = None

        try:
            if in_transaction:
                session = self.current_session
                logger.debug("Using transaction session for SELECT.")
            else:
                session = self._get_session()
                logger.debug("Using new session for SELECT.")

            # 构建SELECT部分
            if columns:
                select_clause = f"SELECT {', '.join([f'`{col}`' for col in columns])} FROM `{table}`"
            else:
                select_clause = f"SELECT * FROM `{table}`"

            # 构建WHERE部分
            where_clause = ""
            params = {}
            if condition:
                conditions = []
                for key, value in condition.items():
                    conditions.append(f"`{key}` = :{key}")
                    params[key] = value
                if conditions:
                    where_clause = f" WHERE {' AND '.join(conditions)}"

            # 构建ORDER BY部分
            order_clause = ""
            if order_by:
                if isinstance(order_by, list):
                    order_clause = f" ORDER BY {', '.join(order_by)}"
                else:
                    order_clause = f" ORDER BY {order_by}"

            # 构建LIMIT和OFFSET部分
            limit_clause = ""
            if limit is not None:
                limit_clause = f" LIMIT {limit}"
                if offset is not None:
                    limit_clause += f" OFFSET {offset}"

            # 组合完整查询
            query_str = f"{select_clause}{where_clause}{order_clause}{limit_clause}"
            query = text(query_str)

            result = session.execute(query, params)
            # 将结果转换为字典列表
            results = [dict(row._mapping) for row in result]
            
            logger.info("SELECT operation completed.")
            return results
        except SQLAlchemyError as e:
            logger.error(f"Failed to execute SELECT: {e}")
            raise Exception(f"Failed to execute SELECT: {str(e)}")
        finally:
            # 只有非事务会话才需要关闭
            if session and not in_transaction:
                session.close()
                logger.debug("Session closed after SELECT.")

    def update(self, table: str, filter: dict, data: Dict[str, Any]) -> bool:
        """
        更新指定表中的一条或多条记录。
    
        Args:
            table: 表名
            filter: 用于指定更新哪些记录的条件字典。
            data: 要更新的数据，键为列名，值为新的列值。
    
        Returns:
            更新是否成功
        """
        if not data:
            return False

        # 检查是否在事务中
        in_transaction = self.current_session is not None
        session = None

        try:
            if in_transaction:
                session = self.current_session
                logger.debug("Using transaction session for UPDATE.")
            else:
                session = self._get_session()
                logger.debug("Using new session for UPDATE.")

            # 构建SET部分
            set_clause = ", ".join([f"`{key}` = :{key}" for key in data.keys()])

            # 构建WHERE部分
            where_clause = ""
            params = dict(data)  # 复制更新数据
            if filter:
                conditions = []
                for key, value in filter.items():
                    filter_key = f"filter_{key}"
                    conditions.append(f"`{key}` = :{filter_key}")
                    params[filter_key] = value
                where_clause = f" WHERE {' AND '.join(conditions)}"

            # 组合完整查询
            query_str = f"UPDATE `{table}` SET {set_clause}{where_clause}"
            query = text(query_str)

            result = session.execute(query, params)

            # 只有非事务会话才需要提交
            if not in_transaction:
                session.commit()
                logger.info(f"UPDATE operation completed. Affected rows: {result.rowcount}")
            else:
                logger.info(f"UPDATE operation added to transaction. Affected rows: {result.rowcount}")

            return True
        except SQLAlchemyError as e:
            # 只有非事务会话才需要回滚
            if session and not in_transaction:
                session.rollback()
            logger.error(f"Failed to execute UPDATE: {e}")
            raise Exception(f"Failed to execute UPDATE: {str(e)}")
        finally:
            # 只有非事务会话才需要关闭
            if session and not in_transaction:
                session.close()
                logger.debug("Session closed after UPDATE.")

    def delete(self, table: str, filter: dict) -> bool:
        """
        删除指定表中的一条或多条记录。
    
        Args:
            table: 表名
            filter: 用于指定删除哪些记录的条件字典。
    
        Returns:
            删除是否成功
        """
        # 检查是否在事务中
        in_transaction = self.current_session is not None
        session = None

        try:
            if in_transaction:
                session = self.current_session
                logger.debug("Using transaction session for DELETE.")
            else:
                session = self._get_session()
                logger.debug("Using new session for DELETE.")

            # 构建WHERE部分
            where_clause = ""
            params = {}
            if filter:
                conditions = []
                for key, value in filter.items():
                    conditions.append(f"`{key}` = :{key}")
                    params[key] = value
                where_clause = f" WHERE {' AND '.join(conditions)}"

            # 组合完整查询
            query_str = f"DELETE FROM `{table}`{where_clause}"
            query = text(query_str)

            result = session.execute(query, params)

            # 只有非事务会话才需要提交
            if not in_transaction:
                session.commit()
                logger.info(f"DELETE operation completed. Affected rows: {result.rowcount}")
            else:
                logger.info(f"DELETE operation added to transaction. Affected rows: {result.rowcount}")

            return True
        except SQLAlchemyError as e:
            # 只有非事务会话才需要回滚
            if session and not in_transaction:
                session.rollback()
            logger.error(f"Failed to execute DELETE: {e}")
            raise Exception(f"Failed to execute DELETE: {str(e)}")
        finally:
            # 只有非事务会话才需要关闭
            if session and not in_transaction:
                session.close()
                logger.debug("Session closed after DELETE.")

    def create_table(self, table_name: str, schema: RDSTableSchema) -> bool:
        """
        根据提供的 RDSTableSchema 创建新表。

        Args:
            table_name: 新表的名称。
            schema: RDSTableSchema 对象，包含了表名、列定义和可选的表选项。

        Returns:
            创建是否成功。
        """
        session = None
        try:
            session = self._get_session()

            # 从schema中直接获取列定义
            column_definitions = []
            for column in schema.columns:
                col_def = f"`{column.name}` {column.column_type}"

                # 处理附加属性
                if hasattr(column, 'additional_properties') and column.additional_properties:
                    for prop, value in column.additional_properties.items():
                        if prop == "AUTO_INCREMENT" and value:
                            col_def += " AUTO_INCREMENT"
                        elif prop == "NOT NULL" and value:
                            col_def += " NOT NULL"
                        elif prop == "UNIQUE" and value:
                            col_def += " UNIQUE"
                        elif prop == "DEFAULT":
                            if isinstance(value, str) and value != "CURRENT_TIMESTAMP":
                                col_def += f" DEFAULT '{value}'"
                            else:
                                col_def += f" DEFAULT {value}"

                # 处理主键
                if hasattr(column, 'is_primary') and column.is_primary:
                    col_def += " PRIMARY KEY"

                column_definitions.append(col_def)

            # 组合列定义
            columns_def = ", ".join(column_definitions)

            # 处理表选项
            table_options = ""
            if hasattr(schema, 'options') and schema.options:
                options_list = []
                for opt, val in schema.options.items():
                    options_list.append(f"{opt}={val}")
                if options_list:
                    table_options = " " + " ".join(options_list)

            query_str = f"CREATE TABLE `{table_name}` ({columns_def}){table_options}"
            query = text(query_str)

            session.execute(query)
            session.commit()
            logger.info(f"Table {table_name} created successfully.")
            return True
        except SQLAlchemyError as e:
            if session:
                session.rollback()
            logger.error(f"Failed to create table: {e}")
            raise Exception(f"Failed to create table: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after CREATE TABLE.")

    def drop_table(self, table_name: str, if_exists: bool = False) -> bool:
        """
        删除表。

        Args:
            table_name: 表名。
            if_exists: 如果为 True，则仅当表存在时才删除。

        Returns:
            删除是否成功。
        """
        session = None
        try:
            session = self._get_session()

            if_exists_clause = "IF EXISTS " if if_exists else ""
            query_str = f"DROP TABLE {if_exists_clause}`{table_name}`"
            query = text(query_str)

            session.execute(query)
            session.commit()
            logger.info(f"Table {table_name} dropped successfully.")
            return True
        except SQLAlchemyError as e:
            if session:
                session.rollback()
            logger.error(f"Failed to drop table: {e}")
            raise Exception(f"Failed to drop table: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after DROP TABLE.")

    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在。

        Args:
            table_name: 表名。

        Returns:
            如果表存在则返回 True，否则返回 False。
        """
        session = None
        try:
            session = self._get_session()

            query = text("SELECT 1 FROM information_schema.tables WHERE table_schema = :database AND table_name = :table_name")
            result = session.execute(query, {"database": self.database, "table_name": table_name})

            return result.fetchone() is not None
        except SQLAlchemyError as e:
            logger.error(f"Failed to check if table exists: {e}")
            raise Exception(f"Failed to check if table exists: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after checking table existence.")

    def list_tables(self) -> List[str]:
        """
        列出数据库中的所有表。

        Returns:
            表名列表。
        """
        session = None
        try:
            session = self._get_session()

            query = text("SHOW TABLES")
            result = session.execute(query)

            # 结果是元组列表，需要提取表名
            table_names = [row[0] for row in result]
            return table_names
        except SQLAlchemyError as e:
            logger.error(f"Failed to list tables: {e}")
            raise Exception(f"Failed to list tables: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after listing tables.")

    def truncate_table(self, table_name: str) -> bool:
        """
        清空表中的所有数据，但保留表结构。

        Args:
            table_name: 表名。

        Returns:
            操作是否成功。
        """
        session = None
        try:
            session = self._get_session()

            query = text(f"TRUNCATE TABLE `{table_name}`")
            session.execute(query)

            session.commit()
            logger.info(f"Table {table_name} truncated successfully.")
            return True
        except SQLAlchemyError as e:
            if session:
                session.rollback()
            logger.error(f"Failed to truncate table: {e}")
            raise Exception(f"Failed to truncate table: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after truncating table.")

    def create_partitioned_table(self, table_name: str, schema: RDSTableSchema, partition_schema: Any) -> bool:
        """
        根据提供的 RDSTableSchema 和分区模式创建新的分区表。

        Args:
            table_name: 新表的名称。
            schema: RDSTableSchema 对象，包含了表名、列定义。
            partition_schema: 定义表如何分区的模式。

        Returns:
            创建是否成功。
        """
        session = None
        try:
            session = self._get_session()

            # 从schema中获取列定义，与create_table方法保持一致
            column_definitions = []
            for column in schema.columns:
                col_def = f"`{column.name}` {column.column_type}"

                # 处理附加属性
                if hasattr(column, 'additional_properties') and column.additional_properties:
                    for prop, value in column.additional_properties.items():
                        if prop == "AUTO_INCREMENT" and value:
                            col_def += " AUTO_INCREMENT"
                        elif prop == "NOT NULL" and value:
                            col_def += " NOT NULL"
                        elif prop == "UNIQUE" and value:
                            col_def += " UNIQUE"
                        elif prop == "DEFAULT":
                            if isinstance(value, str) and value != "CURRENT_TIMESTAMP":
                                col_def += f" DEFAULT '{value}'"
                            else:
                                col_def += f" DEFAULT {value}"

                # 处理主键
                if hasattr(column, 'is_primary') and column.is_primary:
                    col_def += " PRIMARY KEY"

                column_definitions.append(col_def)

            # 组合列定义
            columns_def = ", ".join(column_definitions)

            # 处理表选项
            table_options = ""
            if hasattr(schema, 'options') and schema.options:
                options_list = []
                for opt, val in schema.options.items():
                    options_list.append(f"{opt}={val}")
                if options_list:
                    table_options = " " + " ".join(options_list)

            # 处理分区信息
            partition_type = partition_schema.get('type', 'RANGE')
            partition_expr = partition_schema.get('expression', '')
            partition_defs = partition_schema.get('definitions', [])

            partition_clause = f"PARTITION BY {partition_type} ({partition_expr})"

            # 只有当有分区定义时才添加分区子句
            if partition_defs:
                partition_parts = []
                for part in partition_defs:
                    part_name = part.get('name')
                    part_value = part.get('value')
                    if partition_type.upper() == 'RANGE':
                        partition_parts.append(f"PARTITION {part_name} VALUES LESS THAN ({part_value})")
                    elif partition_type.upper() == 'LIST':
                        partition_parts.append(f"PARTITION {part_name} VALUES IN ({part_value})")
                    elif partition_type.upper() == 'HASH':
                        # HASH分区通常不需要VALUES子句
                        partition_parts.append(f"PARTITION {part_name}")

                if partition_parts:
                    partition_clause += " (" + ", ".join(partition_parts) + ")"

            # 组合完整的创建表SQL
            query_str = f"CREATE TABLE `{table_name}` ({columns_def}) {table_options} {partition_clause}"
            query = text(query_str)

            session.execute(query)
            session.commit()
            logger.info(f"Partitioned table {table_name} created successfully.")
            return True
        except SQLAlchemyError as e:
            if session:
                session.rollback()
            logger.error(f"Failed to create partitioned table: {e}")
            raise Exception(f"Failed to create partitioned table: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after creating partitioned table.")

    def add_partition(self, table_name: str, partition_definition: Any) -> bool:
        """
        向现有分区表添加新分区。

        Args:
            table_name: 表名。
            partition_definition: 新分区的定义。

        Returns:
            添加分区是否成功。
        """
        session = None
        try:
            session = self._get_session()

            part_name = partition_definition.get('name')
            part_value = partition_definition.get('value')

            query_str = f"ALTER TABLE `{table_name}` ADD PARTITION (PARTITION {part_name} VALUES LESS THAN ({part_value}))"
            query = text(query_str)

            session.execute(query)
            session.commit()
            logger.info(f"Partition {part_name} added to table {table_name} successfully.")
            return True
        except SQLAlchemyError as e:
            if session:
                session.rollback()
            logger.error(f"Failed to add partition: {e}")
            raise Exception(f"Failed to add partition: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after adding partition.")

    def drop_partition(self, table_name: str, partition_name: str, if_exists: bool = False) -> bool:
        """
        从分区表中删除一个分区。

        Args:
            table_name: 表名。
            partition_name: 要删除的分区的名称或标识符。
            if_exists: 如果为 True，则仅当分区存在时才删除。

        Returns:
            删除分区是否成功。
        """
        session = None
        try:
            session = self._get_session()

            # MySQL不直接支持IF EXISTS用于分区，需要先检查分区是否存在
            if if_exists:
                check_query = text("SELECT 1 FROM information_schema.partitions WHERE table_schema = :database AND table_name = :table_name AND partition_name = :partition_name")
                result = session.execute(check_query, {"database": self.database, "table_name": table_name, "partition_name": partition_name})
                if not result.fetchone():
                    return True  # 分区不存在，视为成功

            query_str = f"ALTER TABLE `{table_name}` DROP PARTITION {partition_name}"
            query = text(query_str)

            session.execute(query)
            session.commit()
            logger.info(f"Partition {partition_name} dropped from table {table_name} successfully.")
            return True
        except SQLAlchemyError as e:
            if session:
                session.rollback()
            logger.error(f"Failed to drop partition: {e}")
            raise Exception(f"Failed to drop partition: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after dropping partition.")

    def list_partitions(self, table_name: str) -> List[Any]:
        """
        列出指定分区表的所有分区信息。

        Args:
            table_name: 表名。

        Returns:
            分区信息列表。
        """
        session = None
        try:
            session = self._get_session()

            query = text("""
            SELECT partition_name, partition_ordinal_position, partition_description
            FROM information_schema.partitions
            WHERE table_schema = :database AND table_name = :table_name AND partition_name IS NOT NULL
            ORDER BY partition_ordinal_position
            """)

            result = session.execute(query, {"database": self.database, "table_name": table_name})
            results = [dict(row._mapping) for row in result]
            return results
        except SQLAlchemyError as e:
            logger.error(f"Failed to list partitions: {e}")
            raise Exception(f"Failed to list partitions: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after listing partitions.")

    def truncate_partition(self, table_name: str, partition_name: str) -> bool:
        """
        清空指定分区中的所有数据，但保留分区结构。

        Args:
            table_name: 表名。
            partition_name: 要清空的分区的名称或标识符。

        Returns:
            操作是否成功。
        """
        session = None
        try:
            session = self._get_session()

            # MySQL不直接支持TRUNCATE PARTITION，需要使用ALTER TABLE ... TRUNCATE PARTITION
            query_str = f"ALTER TABLE `{table_name}` TRUNCATE PARTITION {partition_name}"
            query = text(query_str)

            session.execute(query)
            session.commit()
            logger.info(f"Partition {partition_name} in table {table_name} truncated successfully.")
            return True
        except SQLAlchemyError as e:
            if session:
                session.rollback()
            logger.error(f"Failed to truncate partition: {e}")
            raise Exception(f"Failed to truncate partition: {str(e)}")
        finally:
            if session:
                session.close()
                logger.debug("Session closed after truncating partition.")

    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行自定义查询语句。

        Args:
            query: SQL查询语句。
            params: 查询参数字典。

        Returns:
            查询结果列表，列表中每个字典代表一条记录。
        """
        # 检查是否在事务中
        in_transaction = self.current_session is not None
        session = None

        try:
            if in_transaction:
                session = self.current_session
                logger.debug("Using transaction session for custom query.")
            else:
                session = self._get_session()
                logger.debug("Using new session for custom query.")

            # 执行查询
            sql_query = text(query)
            if params:
                result = session.execute(sql_query, params)
            else:
                result = session.execute(sql_query)

            # 获取结果（如果是SELECT语句）
            if query.strip().upper().startswith("SELECT"):
                results = [dict(row._mapping) for row in result]
                logger.info("Custom query executed successfully.")
                return results
            else:
                # 非SELECT语句，如果不在事务中则提交
                if not in_transaction:
                    session.commit()
                    logger.info(f"Custom query executed successfully. Affected rows: {result.rowcount}")
                else:
                    logger.info(f"Custom query added to transaction. Affected rows: {result.rowcount}")
                return []
        except SQLAlchemyError as e:
            # 只有非事务会话才需要回滚
            if session and not in_transaction:
                session.rollback()
            logger.error(f"Failed to execute custom query: {e}")
            raise Exception(f"Failed to execute custom query: {str(e)}")
        finally:
            # 只有非事务会话才需要关闭
            if session and not in_transaction:
                session.close()
                logger.debug("Session closed after custom query.")

    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> bool:
        """
        执行自定义命令（非查询语句，如INSERT, UPDATE, DELETE等）。
    
        Args:
            command: SQL命令。
            params: 命令参数字典。
    
        Returns:
            命令执行是否成功。
        """
        # 检查是否在事务中
        in_transaction = self.current_session is not None
        session = None

        try:
            if in_transaction:
                session = self.current_session
                logger.debug("Using transaction session for custom command.")
            else:
                session = self._get_session()
                logger.debug("Using new session for custom command.")

            # 执行命令
            sql_command = text(command)
            if params:
                result = session.execute(sql_command, params)
            else:
                result = session.execute(sql_command)

            # 只有非事务会话才需要提交
            if not in_transaction:
                session.commit()
                logger.info(f"Custom command executed successfully. Affected rows: {result.rowcount}")
            else:
                logger.info(f"Custom command added to transaction. Affected rows: {result.rowcount}")

            return True
        except SQLAlchemyError as e:
            # 只有非事务会话才需要回滚
            if session and not in_transaction:
                session.rollback()
            logger.error(f"Failed to execute custom command: {e}")
            raise Exception(f"Failed to execute custom command: {str(e)}")
        finally:
            # 只有非事务会话才需要关闭
            if session and not in_transaction:
                session.close()
                logger.debug("Session closed after custom command.")

    def begin_transaction(self) -> None:
        """
        开始一个事务。
        """
        if self.current_session:
            raise Exception("已有活动事务，请先提交或回滚当前事务")

        try:
            self.current_session = self._get_session()
            # SQLAlchemy会话默认就是事务性的，不需要显式开始事务
            logger.info("Transaction started.")
        except SQLAlchemyError as e:
            logger.error(f"Failed to start transaction: {e}")
            raise Exception(f"Failed to start transaction: {str(e)}")

    def commit(self) -> None:
        """
        提交当前事务。
        """
        if not self.current_session:
            raise Exception("No active transaction to commit.")

        try:
            self.current_session.commit()
            logger.info("Transaction committed.")
        except SQLAlchemyError as e:
            logger.error(f"Failed to commit transaction: {e}")
            raise Exception(f"Failed to commit transaction: {str(e)}")
        finally:
            self.current_session.close()
            self.current_session = None
            logger.debug("Session closed after commit.")

    def rollback(self) -> None:
        """
        回滚当前事务。
        """
        if not self.current_session:
            raise Exception("No active transaction to rollback.")

        try:
            self.current_session.rollback()
            logger.info("Transaction rolled back.")
        except SQLAlchemyError as e:
            logger.error(f"Failed to rollback transaction: {e}")
            raise Exception(f"Failed to rollback transaction: {str(e)}")
        finally:
            self.current_session.close()
            self.current_session = None
            logger.debug("Session closed after rollback.") 