'''
File Created: Tuesday, 11th June 2025 2:30:10 pm
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
'''
from dataclasses import dataclass, field
from typing import Optional

@dataclass
class LLMConfig:
    """
    大语言模型 (LLM) 的配置结构。
    这个结构将被 Hydra 用来加载和实例化 LLM provider。
    """
    # Hydra 实例化的目标类路径，例如: 
    # 'base.model_serve.model_runtime.model_providers.llm_model.opentrek_llm.OpenTrekLLM'
    _target_: str
    
    # 连接凭证与参数
    name: str = "default_llm"
    key: str = "EMPTY"
    url: str = "http://localhost:8000/v1/chat/completions"
    model_type: str = "opentrek"
    
    # 模型运行时参数
    stream: bool = True
    max_tokens: int = 8192
    temperature: float = 0.9

@dataclass
class EmbeddingConfig:
    """
    文本嵌入 (Embedding) 模型的配置结构。
    """
    # Hydra 实例化的目标类路径，例如:
    # 'base.model_serve.model_runtime.model_providers.embedding_model.sample_embedding.SampleEmbedding'
    _target_: str
    
    # 连接凭证与参数
    name: str = "default_embedding"
    model: str = "embedding" # 传递给 embedding API 的模型名
    url: str = "http://localhost:8000/v1/embeddings"
    
    # 运行时参数（未来可扩展）
    # batch_size: int = 32 