'''
File Created: Monday, 9th June 2025 10:12:35 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 10:17:06 am
'''

from typing import Optional, Union, Generator, AsyncGenerator, Dict, Any, Callable
import aiohttp
import json
import asyncio
from ..__base.large_language_model import LargeLanguageModel
from ...entities import PromptMessage, PromptMessageTool, LLMResult, LLMResultChunk, AssistantPromptMessage
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


# Helper class for making dicts attribute-accessible
class OpenAICompatibleObject:
    def __init__(self, data):
        self._data = data

    def __getattr__(self, name):
        if name in self._data:
            value = self._data[name]
            if isinstance(value, dict):
                return OpenAICompatibleObject(value)
            elif isinstance(value, list):
                return [OpenAICompatibleObject(item) if isinstance(item, dict) else item for item in value]
            return value
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __getitem__(self, key):
        value = self._data[key]
        if isinstance(value, dict):
            return OpenAICompatibleObject(value)
        elif isinstance(value, list):
            return [OpenAICompatibleObject(item) if isinstance(item, dict) else item for item in value]
        return value

    def __repr__(self):
        return repr(self._data)


async def async_call_opentrek(**params):
    """
    异步方式调用Opentrek API，支持流式和非流式输出，输出格式兼容OpenAI。
    """

    url = params['url']
    key = params['key']
    messages = params['messages']
    is_stream = params.get('stream', False)
    # print(messages)
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"{key}"
    }

    payload = {
        "model": params.get("model", "qwen_vllm"),
        "messages": messages,
        "stream": is_stream,
        **{k: v for k, v in params.items() if k in ["temperature", "max_tokens", "top_p", "stop"]}
    }
    logger.info(f"Opentrek Request Payload: {payload}")
    # print(payload)
    # print(headers)
    session = aiohttp.ClientSession(
        connector=aiohttp.TCPConnector(ssl=False)
    )
    try:
        response = await session.post(url, headers=headers, json=payload)
        # print(response)
        response.raise_for_status()

        if not is_stream:
            response = await response.json()
            await session.close()
            logger.info(f"Opentrek Non-Stream Response: {response}")
            return response
        else:
            logger.info("Opentrek Stream Request Initiated")
            return OpentrekStream(response, session)

    except aiohttp.ClientResponseError as e:
        logger.error(f"Opentrek API HTTP Error: {e.status} {e.message}")
        error_body = await response.text()
        logger.error(f"Error Body: {error_body}")
        await session.close()
        raise
    except aiohttp.ClientError as e:
        logger.error(f"Opentrek API Client Error: {e}")
        await session.close()
        raise
    except Exception as e:
        logger.error(f"Unexpected error during Opentrek call: {e}")
        if 'session' in locals() and not session.closed:
            await session.close()
        raise


class OpentrekStream:
    """异步迭代器，用于处理Opentrek的流式响应，并包装成兼容OpenAI的格式。"""

    def __init__(self, response: aiohttp.ClientResponse, session: aiohttp.ClientSession):
        self.response = response
        self.session = session
        logger.debug("OpentrekStream initialized.")

    def __aiter__(self):
        return self

    async def __anext__(self):
        while True:
            try:
                line_bytes = await self.response.content.readline()
            except (aiohttp.ClientPayloadError, aiohttp.ClientConnectionError) as e:
                logger.error(f"Opentrek stream reading error: {e}")
                if not self.session.closed:
                    await self.session.close()
                raise StopAsyncIteration from e

            if not line_bytes:
                logger.info("Opentrek stream finished (empty readline).")
                if not self.session.closed:
                    await self.session.close()
                raise StopAsyncIteration

            line = line_bytes.decode('utf-8').strip()
            if not line:
                continue

            if line == "data: [DONE]":
                logger.info("Opentrek stream finished ([DONE] marker).")
                if not self.session.closed:
                    await self.session.close()
                raise StopAsyncIteration

            if line.startswith("data:"):
                data_str = line[len("data:"):].strip()
                if not data_str:
                    continue
                try:
                    chunk_data = json.loads(data_str)
                    return OpenAICompatibleObject(chunk_data)
                except json.JSONDecodeError:
                    logger.warning(f"无法解析Opentrek流中的JSON数据: {data_str}")
                    continue


async def async_call_opentrek_stream(callback: Optional[Callable[[str], None]] = None, **params) -> AsyncGenerator:
    """
    异步方式调用Opentrek流式API，支持回调函数处理每个块的内容字符串。
    """
    params['stream'] = True
    try:
        stream = await async_call_opentrek(**params)
        async for chunk in stream:
            if callback:
                content = ""
                try:
                    if hasattr(chunk, 'choices') and chunk.choices and hasattr(chunk.choices[0], 'delta') and hasattr(
                            chunk.choices[0].delta, 'content'):
                        content = chunk.choices[0].delta.content or ""
                    elif hasattr(chunk, 'choices') and chunk.choices and hasattr(chunk.choices[0],
                                                                                 'message') and hasattr(
                        chunk.choices[0].message, 'content'):
                        content = chunk.choices[0].message.content or ""
                except (AttributeError, IndexError, TypeError) as e:
                    logger.warning(f"无法从 Opentrek chunk 中提取 content: {e}, chunk: {chunk!r}")
                    content = ""

                if content:
                    asyncio.create_task(callback(content))

            yield chunk
    except Exception as e:
        logger.error(f"Error in async_call_opentrek_stream: {e}")
        raise


class OpenTrekLLM(LargeLanguageModel):
    def _convert_prompt_message(self, prompt_messages: list[PromptMessage]) -> list[Dict[str, Any]]:
        """将 PromptMessage 转换为 Opentrek 兼容的消息格式。"""
        messages = []
        for msg in prompt_messages:
            message = {
                "role": msg.role.name.lower(),
                "content": msg.content
            }
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                message["tool_calls"] = [
                    {
                        "id": tool.id,
                        "type": "function",
                        "function": {
                            "name": tool.function.name,
                            "arguments": json.dumps(tool.function.arguments)
                        }
                    } for tool in msg.tool_calls
                ]
            messages.append(message)
        return messages

    def _convert_tools(self, tools: Optional[list[PromptMessageTool]]) -> Optional[list[Dict[str, Any]]]:
        """将 PromptMessageTool 转换为 Opentrek 兼容的工具格式。"""
        if not tools:
            return None
        return [
            {
                "type": "function",
                "function": {
                    "name": tool.function.name,
                    "description": tool.function.description,
                    "parameters": tool.function.parameters
                }
            } for tool in tools
        ]

    def _create_llm_result(self, response: OpenAICompatibleObject, model: str, credentials,
                           prompt_messages: list[PromptMessage]) -> LLMResult:
        """从非流式响应创建 LLMResult，usage 固定为 0。"""
        try:
            content = response["choices"][0]["message"]["content"] or ""
            tool_calls = [
                {
                    "id": tool.id,
                    "type": "function",
                    "function": {
                        "name": tool.function.name,
                        "arguments": json.loads(tool.function.arguments)
                    }
                } for tool in response["choices"][0]["message"]["tool_calls"] or []
            ]
        except (AttributeError, IndexError, KeyError) as e:
            logger.warning(f"无法解析响应内容: {e}")
            content = ""
            tool_calls = []

        return LLMResult(
            model=model,
            prompt_messages=prompt_messages,
            message=AssistantPromptMessage(
                content=content,
                tool_calls=tool_calls
            ),
            usage=self._calc_response_usage(
                model=model,
                credentials=credentials,
                prompt_tokens=response["usage"].get("prompt_tokens", 0),  # usage.get('prompt_tokens', 0)
                completion_tokens=response["usage"].get("completion_tokens", 0),  # usage.get('completion_tokens', 0)
            )
        )

    def invoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: list[PromptMessage],
            tools: Optional[list[PromptMessageTool]] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        logger.info(f"Invoking Opentrek LLM (sync) with model: {model}, stream: {stream}")
        params = {
            "url": credentials.get("base_url"),
            "key": credentials.get("api_Key"),
            "model": model,
            "messages": self._convert_prompt_message(prompt_messages),
            "stream": stream,
            **{k: v for k, v in model_parameters.items() if k in ["temperature", "max_tokens", "top_p"]}
        }
        if stop:
            params["stop"] = stop
        if tools:
            params["tools"] = self._convert_tools(tools)

        if not stream:
            # print("这是key的值",params["key"])
            loop = asyncio.get_event_loop()
            response = loop.run_until_complete(async_call_opentrek(**params))
            print(response)
            return self._create_llm_result(response, model, credentials, prompt_messages)
        else:
            def stream_generator():
                loop = asyncio.get_event_loop()
                stream = loop.run_until_complete(async_call_opentrek(**params))
                for chunk in stream:
                    try:
                        content = chunk.choices[0].delta.content or "" if chunk.choices else ""
                        tool_calls = [
                            {
                                "id": tool.id,
                                "type": "function",
                                "function": {
                                    "name": tool.function.name,
                                    "arguments": json.loads(tool.function.arguments)
                                }
                            } for tool in chunk.choices[0].delta.tool_calls or []
                        ]
                        yield LLMResultChunk(
                            model=model,
                            prompt_messages=prompt_messages,
                            delta=AssistantPromptMessage(
                                content=content,
                                tool_calls=tool_calls
                            ),
                            usage={"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                        )
                    except (AttributeError, IndexError, KeyError) as e:
                        logger.warning(f"无法解析流式 chunk: {e}")
                        continue
                yield LLMResultChunk(
                    model=model,
                    prompt_messages=prompt_messages,
                    delta=AssistantPromptMessage(content="", tool_calls=[]),
                    usage={"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                )

                return stream_generator()

    async def ainvoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: list[PromptMessage],
            tools: Optional[list[PromptMessageTool]] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, AsyncGenerator[LLMResultChunk, None]]:
        logger.info(f"Invoking Opentrek LLM (async) with model: {model}, stream: {stream}")
        params = {
            "url": credentials.get("base_url"),
            "key": credentials.get("api_key"),
            "model": model,
            "messages": prompt_messages,
            "stream": stream,
            **{k: v for k, v in model_parameters.items() if k in ["temperature", "max_tokens", "top_p"]}
        }
        if stop:
            params["stop"] = stop
        if tools:
            params["tools"] = self._convert_tools(tools)

        if not stream:
            response = await async_call_opentrek(**params)
            return self._create_llm_result(response, model, credentials, prompt_messages)
        else:
            async def stream_generator():
                async for chunk in async_call_opentrek_stream(**params):
                    try:
                        content = chunk.choices[0].delta.content or "" if chunk.choices else ""
                        tool_calls = [
                            {
                                "id": tool.id,
                                "type": "function",
                                "function": {
                                    "name": tool.function.name,
                                    "arguments": json.loads(tool.function.arguments)
                                }
                            } for tool in chunk.choices[0].delta.tool_calls or []
                        ]
                        yield LLMResultChunk(
                            model=model,
                            prompt_messages=prompt_messages,
                            delta=AssistantPromptMessage(
                                content=content,
                                tool_calls=tool_calls
                            ),
                            usage={"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                        )
                    except (AttributeError, IndexError, KeyError) as e:
                        logger.warning(f"无法解析流式 chunk: {e}")
                        continue
                    yield LLMResultChunk(
                        model=model,
                        prompt_messages=prompt_messages,
                        delta=AssistantPromptMessage(content="", tool_calls=[]),
                        usage={"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                    )

            return stream_generator()
