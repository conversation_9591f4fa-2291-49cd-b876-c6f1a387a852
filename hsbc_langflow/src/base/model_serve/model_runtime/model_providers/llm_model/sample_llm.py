from typing import Optional, Union, Generator, AsyncGenerator

from ..__base.large_language_model import LargeLanguageModel
from ...entities import PromptMessage, PromptMessageTool, LLMResult, LLMResultChunk, AssistantPromptMessage, \
    LLMResultChunkDelta

import logging
from openai import OpenAI
import httpx
import json

# 配置日志记录
logging.basicConfig(level=logging.DEBUG)


class SampleLLM(LargeLanguageModel):
    async def async_call_openai_sdk(self, key: str, url: str, **payload) -> any:
        headers = {
            "Authorization": f"Bearer {key}",
            "Content-Type": "application/json"
        }
        async with httpx.AsyncClient(base_url=url) as client:
            response = await client.post("/chat/completions", json=payload, headers=headers, timeout=None)
            response.raise_for_status()
            return response.aiter_text()

    def invoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: list[PromptMessage],
            tools: Optional[list[PromptMessageTool]] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        print('invoke in sample_llm')

        # 将 PromptMessage 对象转换为字符串内容
        messages = [{"role": msg.role.value if hasattr(msg.role, 'value') else msg.role, "content": msg.content} for msg
                    in prompt_messages]
        print(f"messages:{messages}")

        max_tokens = model_parameters.get("max_tokens", 2048)
        temperature = model_parameters.get("temperature", 0.9)
        base_url = credentials.get("base_url", "http://218.78.129.173:30167/v1")
        api_key = credentials.get("api_key", "empty")

        client = OpenAI(api_key=api_key, base_url=base_url)

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
        }

        try:
            if stream:
                response = client.chat.completions.create(**payload)

                accumulated_content = ""
                index = 0

                def stream_generator():
                    nonlocal accumulated_content, index

                    for chunk in response:
                        delta = chunk.choices[0].delta
                        content_chunk = delta.content if delta.content is not None else ""
                        logging.info(f"Content chunk: {content_chunk}")

                        finish_reason = chunk.choices[0].finish_reason

                        if content_chunk:
                            accumulated_content += content_chunk
                            logging.info(f"Accumulated content: {accumulated_content}")

                            # 构建 AssistantPromptMessage 对象
                            message = AssistantPromptMessage(
                                content=content_chunk,
                                tool_calls=[]
                            )

                            # 构建 LLMResultChunkDelta 对象
                            delta = LLMResultChunkDelta(
                                index=index,
                                message=message,
                                finish_reason=finish_reason
                            )

                            # 构建 LLMResultChunk 对象
                            yield LLMResultChunk(
                                model=model,
                                delta=delta
                            )

                            index += 1

                return stream_generator()

            else:
                response = client.chat.completions.create(**payload)
                print(f"response:{response}")

                choices = response.choices
                print(f"choices:{choices}")
                if not choices:
                    logging.warning("Empty response from LLM. Returning empty content.")
                    content_result = ""
                else:
                    content_result = choices[0].message.content
                    print('-------')
                    print(content_result)

                return LLMResult(
                    model=model,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(
                        content=content_result.strip() if content_result else "",
                        tool_calls=[],
                    ),
                    usage=self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=0,  # response.usage.prompt_tokens
                        completion_tokens=0,  # response.usage.completion_tokens
                    )
                )

        except Exception as e:
            raise Exception(f"Failed to get LLM response: {str(e)}")

    async def ainvoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: list[PromptMessage],
            tools: Optional[list['PromptMessageTool']] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, AsyncGenerator[LLMResultChunk, None]]:
        print('ainvoke in sample_llm')

        # 将 PromptMessage 对象转换为字符串内容
        messages = [{"role": msg.role.value if hasattr(msg.role, 'value') else msg.role, "content": msg.content} for
                    msg in prompt_messages]
        print(f"messages:{messages}")

        max_tokens = model_parameters.get("max_tokens", 2048)
        temperature = model_parameters.get("temperature", 0.9)
        base_url = credentials.get("base_url", "http://218.78.129.173:30167/v1")
        api_key = credentials.get("api_key", "empty")

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
        }

        try:
            response_generator = await self.async_call_openai_sdk(key=api_key, url=base_url, **payload)

            if stream:
                accumulated_content = ""
                index = 0

                async def stream_generator():
                    nonlocal accumulated_content, index

                    async for chunk in response_generator:
                        if not chunk.strip():  # Skip empty chunks
                            continue

                        lines = chunk.split('\n')
                        for line in lines:
                            if line.startswith('data:'):
                                data = line[5:]
                                if data == '[DONE]':
                                    break
                                try:
                                    event_data = json.loads(data)
                                except json.JSONDecodeError as e:
                                    logging.error(f"Failed to decode JSON: {data}")
                                    continue

                                delta = event_data.get('choices', [{}])[0].get('delta', {})
                                content_chunk = delta.get('content', "")
                                logging.info(f"Content chunk: {content_chunk}")

                                finish_reason = event_data.get('choices', [{}])[0].get('finish_reason')

                                if content_chunk:
                                    accumulated_content += content_chunk
                                    logging.info(f"Accumulated content: {accumulated_content}")

                                    # 构建 AssistantPromptMessage 对象
                                    message = AssistantPromptMessage(
                                        content=content_chunk,
                                        tool_calls=[]
                                    )

                                    # 构建 LLMResultChunkDelta 对象
                                    delta_obj = LLMResultChunkDelta(
                                        index=index,
                                        message=message,
                                        finish_reason=finish_reason
                                    )

                                    # 构建 LLMResultChunk 对象
                                    yield LLMResultChunk(
                                        model=model,
                                        delta=delta_obj
                                    )

                                    index += 1

                return stream_generator()

            else:
                full_response = ""
                async for chunk in response_generator:
                    full_response += chunk

                response_json = json.loads(full_response)
                print(f"response:{response_json}")

                choices = response_json.get('choices', [])
                print(f"choices:{choices}")
                if not choices:
                    logging.warning("Empty response from LLM. Returning empty content.")
                    content_result = ""
                else:
                    content_result = choices[0].get('message', {}).get('content', '')
                    # print('-------')
                    # print(content_result)

                # usage = response_json.get('usage', {})

                return LLMResult(
                    model=model,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(
                        content=content_result.strip() if content_result else "",
                        tool_calls=[],
                    ),
                    usage=self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=0,  # usage.get('prompt_tokens', 0)
                        completion_tokens=0,  # usage.get('completion_tokens', 0)
                    )
                )

        except Exception as e:
            raise Exception(f"Failed to get LLM response: {str(e)}")
