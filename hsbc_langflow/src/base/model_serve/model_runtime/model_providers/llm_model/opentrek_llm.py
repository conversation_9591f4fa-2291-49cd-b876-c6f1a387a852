'''
File Created: Monday, 9th June 2025 10:12:35 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 10:15:31 am
'''

from typing import Optional, Union, Generator, AsyncGenerator, List, Dict, Any, Callable
import aiohttp
import json
import asyncio
from ..__base.large_language_model import LargeLanguageModel
from ...entities import PromptMessage, PromptMessageTool, LLMResult, LLMResultChunk, AssistantPromptMessage, \
    LLMResultChunkDelta
from loguru import logger
import requests
import time
import uuid
from collections.abc import Generator, Sequence, AsyncGenerator
from base.model_serve.model_runtime.callbacks.base_callback import Callback
from base.model_serve.model_runtime.entities.llm_entities import LLMUsage
from pydantic import PrivateAttr, Field



class OpentrekStream:
    """异步迭代器，用于处理Opentrek的流式响应，并包装成兼容OpenAI的格式。"""

    def __init__(self, response: aiohttp.ClientResponse, session: aiohttp.ClientSession, model: str):
        self.response = response
        self.session = session
        self.model = model
        self.index = 0
        self.iterator = self.response.content.iter_chunks()
        logger.debug("OpentrekStream initialized.")
        # 调试打印（可选，可删除）
        print(dir(response))
        print(dir(response.content))
        print(response.content.iter_chunks)

    def __aiter__(self):
        return self

    async def __anext__(self) -> LLMResultChunk:
        try:
            async for chunk in self.iterator:
                line_bytes, _ = chunk
                if not line_bytes:
                    await self.response.release()
                    raise StopAsyncIteration

                line = line_bytes.decode('utf-8').strip()
                if not line or line == "data: [DONE]":
                    await self.response.release()
                    raise StopAsyncIteration

                if line.startswith("data:"):
                    data_str = line[len("data:"):].strip()
                    if not data_str:
                        continue
                    try:
                        chunk_data = json.loads(data_str)
                        if "choices" in chunk_data and chunk_data["choices"]:
                            delta = chunk_data["choices"][0].get("delta", {})
                            finish_reason = chunk_data["choices"][0].get("finish_reason", "")
                            if "content" in delta:
                                content_chunk = delta["content"]
                                message = AssistantPromptMessage(content=content_chunk, tool_calls=[])
                                result = LLMResultChunk(
                                    model=self.model,
                                    delta=LLMResultChunkDelta(
                                        index=self.index,
                                        message=message,
                                        finish_reason=finish_reason
                                    )
                                )
                                self.index += 1
                                return result
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse JSON: {data_str}")
                        continue
            
            await self.response.release()
            raise StopAsyncIteration
        except StopAsyncIteration:
            await self.response.release()
            raise
        except Exception as e:
            await self.response.release()
            logger.error(f"Error in stream: {str(e)}")
            raise


class OpenTrekLLM(LargeLanguageModel):

    # --- Field Declarations for Pydantic ---
    # Public fields that come from config
    base_url: str
    api_key: str
    model_name: str
    provider: str
    model_parameters: Dict[str, Any] = Field(default_factory=dict)
    stream: bool = True

    # Internal state, managed via PrivateAttr to avoid Pydantic validation
    _sync_session: Any = PrivateAttr()
    _async_session: Optional[aiohttp.ClientSession] = PrivateAttr(default=None)
    _headers: Dict[str, str] = PrivateAttr()

    def __init__(self, **kwargs):
        """
        Initializes the OpenTrekLLM client.
        Pydantic will validate public fields from kwargs first, then this code runs.
        """
        super().__init__(**kwargs)
        # Initialize private attributes after Pydantic validation is complete
        self._sync_session = requests.Session()
        # _async_session is lazily initialized to avoid RuntimeError in sync contexts
        self._headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    @property
    def async_session(self) -> aiohttp.ClientSession:
        """Lazily creates and returns the aiohttp ClientSession."""
        if self._async_session is None or self._async_session.closed:
            self._async_session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60, sock_read=30))
        return self._async_session
    
    def invoke(
        self,
        prompt_messages: list[PromptMessage],
        model: Optional[str] = None,
        credentials: Optional[dict] = None,
        model_parameters: Optional[dict] = None,
        tools: Optional[list[PromptMessageTool]] = None,
        stop: Optional[list[str]] = None,
        stream: Optional[bool] = None,
        user: Optional[str] = None,
        callbacks: Optional[list[Callback]] = None,
    ) -> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        # --- Start of new logic: Default + Override ---
        model = model or self.model_name
        stream = stream if stream is not None else self.stream
        
        # Merge model parameters: instance defaults < invoke parameters
        merged_params = self.model_parameters.copy()
        if model_parameters:
            merged_params.update(model_parameters)
        # --- End of new logic ---

        self.started_at = time.perf_counter()
        callbacks = callbacks or []
        self._trigger_before_invoke_callbacks(
            model=model, 
            credentials=credentials, 
            prompt_messages=prompt_messages,
            model_parameters=merged_params, 
            tools=tools, 
            stop=stop,
            stream=stream, 
            user=user, 
            callbacks=callbacks,
        )

        try:
            # Here we call our own `invoke_llm` method
            result = self.invoke_llm(
                tenant_id=getattr(self, 'tenant_id', 'unknown'), # get from instance attribute
                user_id=user or "unknown",
                provider=getattr(self, 'provider_name', 'opentrek'), # get from instance attribute
                model=model,
                credentials=credentials,
                model_parameters=merged_params,
                prompt_messages=prompt_messages,
                tools=tools,
                stop=list(stop) if stop else None,
                stream=stream,
            )
            # Dify's original logic has a complex handling of Generator, we simplify it here for now
            # In our client model, the return value is directly the Generator or LLMResult
            return result
        except Exception as e:
            self._trigger_invoke_error_callbacks(
                model=model, 
                ex=e, 
                credentials=credentials, 
                prompt_messages=prompt_messages,
                model_parameters=merged_params, 
                tools=tools, 
                stop=stop,
                stream=stream, 
                user=user, 
                callbacks=callbacks,
            )
            raise self._transform_invoke_error(e)

    def invoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict, # `credentials` are passed in for compatibility with Dify, but we mainly use the configuration in `__init__`
            model_parameters: dict,
            prompt_messages: List[PromptMessage],
            tools: Optional[List[PromptMessageTool]] = None,
            stop: Optional[List[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        """调用大语言模型，生成响应或流式响应。"""
        messages = [
            {"role": msg.role.value if hasattr(msg.role, 'value') else msg.role, "content": msg.content}
            for msg in prompt_messages
        ]

        max_tokens = model_parameters.get("max_tokens", 2048)
        temperature = model_parameters.get("temperature", 0.9)

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
        }
        if stop:
            payload['stop'] = stop
        if tools:
             # Logic for handling tools needs to be added here based on the actual situation
            pass

        try:
            if stream:
                def stream_generator() -> Generator[LLMResultChunk, None, None]:
                    response = self._sync_session.post(self.base_url, json=payload, headers=self._headers, timeout=None, verify=False, stream=True)
                    response.raise_for_status() # Raise an exception for bad status codes
                    
                    buffer = b''
                    for chunk in response.iter_content(chunk_size=8192):
                        if not chunk:
                            continue
                        buffer += chunk
                        while b'\n' in buffer:
                            line, buffer = buffer.split(b'\n', 1)
                            data = line.decode('utf-8').strip()
                            if not data or data == "data: [DONE]":
                                continue
                            if data.startswith("data: "):
                                data_json = json.loads(data[6:])
                                if "choices" in data_json and data_json["choices"]:
                                    delta = data_json["choices"][0].get("delta", {})
                                    finish_reason = data_json["choices"][0].get("finish_reason", "")
                                    if "content" in delta:
                                        content_chunk = delta["content"]
                                        message = AssistantPromptMessage(
                                            content=content_chunk, 
                                            tool_calls=[]
                                            )
                                        yield LLMResultChunk(
                                            model=model,
                                            delta=LLMResultChunkDelta(
                                                index=0, # Simplified index
                                                message=message,
                                                finish_reason=finish_reason
                                            )
                                        )
                return stream_generator()
            else:
                response = self._sync_session.post(self.base_url, json=payload, headers=self._headers, timeout=None, verify=False)
                response.raise_for_status()
                response_data = response.json()
                choices = response_data.get("choices", [])
                content_result = choices[0]["message"]["content"] if choices else ""

                return LLMResult(
                    model=model,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(
                        content=content_result.strip() if content_result else "",
                        tool_calls=[],
                    ),
                    usage=self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=0,  # response.usage.prompt_tokens
                        completion_tokens=0,  # response.usage.completion_tokens
                    )
                )

        except requests.RequestException as e:
            logger.error(f"调用 LLM 失败: {str(e)}")
            raise Exception(f"获取 LLM 响应失败: {str(e)}")

    async def ainvoke(
        self,
        prompt_messages: list[PromptMessage],
        model: Optional[str] = None,
        credentials: Optional[dict] = None, # `credentials` are passed in for compatibility with Dify, but we mainly use the configuration in `__init__`
        model_parameters: Optional[dict] = None,
        tools: Optional[list[PromptMessageTool]] = None,
        stop: Optional[list[str]] = None,
        stream: Optional[bool] = None,
        user: Optional[str] = None,
        callbacks: Optional[list[Callback]] = None,
    ) -> Union[LLMResult, AsyncGenerator[LLMResultChunk, None]]:
        # --- Start of new logic: Default + Override ---
        model = model or self.model_name
        stream = stream if stream is not None else self.stream

        # Merge model parameters: instance defaults < invoke parameters
        merged_params = self.model_parameters.copy()
        if model_parameters:
            merged_params.update(model_parameters)
        # --- End of new logic ---

        self.started_at = time.perf_counter()
        callbacks = callbacks or []
        self._trigger_before_invoke_callbacks(
            model=model, 
            credentials=credentials, 
            prompt_messages=prompt_messages,
            model_parameters=merged_params, 
            tools=tools, 
            stop=stop,
            stream=stream, 
            user=user, 
            callbacks=callbacks,
        )
        try:
            result = await self.ainvoke_llm(
                tenant_id=getattr(self, 'tenant_id', 'unknown'),
                user_id=user or "unknown",
                provider=getattr(self, 'provider_name', 'opentrek'),
                model=model,
                credentials=credentials,
                model_parameters=merged_params,
                prompt_messages=prompt_messages,
                tools=tools,
                stop=list(stop) if stop else None,
                stream=stream,
            )
            return result
        except Exception as e:
            self._trigger_invoke_error_callbacks(
                model=model, 
                ex=e, 
                credentials=credentials, 
                prompt_messages=prompt_messages,
                model_parameters=merged_params, 
                tools=tools, 
                stop=stop,
                stream=stream, 
                user=user, 
                callbacks=callbacks,
            )
            raise self._transform_invoke_error(e)

    async def ainvoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: list[PromptMessage],
            tools: Optional[list['PromptMessageTool']] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, AsyncGenerator[LLMResultChunk, None]]:
        messages = [{"role": msg.role.value if hasattr(msg.role, 'value') else msg.role, "content": msg.content} for msg in prompt_messages]

        max_tokens = model_parameters.get("max_tokens", 2048)
        temperature = model_parameters.get("temperature", 0.9)

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
        }
        if stop:
            payload['stop'] = stop
        if tools:
            pass # tool handling logic

        try:
            response = await self.async_session.post(self.base_url, headers=self._headers, json=payload)
            response.raise_for_status()

            if stream:
                # In async mode, we need to return an async generator
                return OpentrekStream(response, self.async_session, model)
            else:
                response_json = await response.json()
                # Do NOT close the session here. It's shared for the lifetime of the client.
                choices = response_json.get('choices', [])
                content_result = choices[0].get('message', {}).get('content', '') if choices else ""

                return LLMResult(
                    model=model,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(
                        content=content_result.strip() if content_result else "",
                        tool_calls=[],
                    ),
                    usage=self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=0, 
                        completion_tokens=0)
                )
        except aiohttp.ClientError as e:
            # We don't close the session on error either, so it can be reused for retries.
            logger.error(f"Async request failed: {str(e)}")
            raise Exception(f"Failed to get LLM response: {str(e)}")

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._async_session and not self._async_session.closed:
            await self._async_session.close()

    def __del__(self):
        # Ensure the async session is closed when the object is destroyed
        if self._async_session and not self._async_session.closed:
            try:
                loop = asyncio.get_running_loop()
                if loop.is_running():
                    loop.create_task(self._async_session.close())
            except RuntimeError: # Can happen if loop is already closed
                pass
            except Exception as e:
                logger.error(f"Error creating task to close async_session in __del__: {e}")
