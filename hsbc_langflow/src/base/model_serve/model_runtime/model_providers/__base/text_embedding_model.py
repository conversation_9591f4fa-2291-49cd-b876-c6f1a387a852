from typing import Optional
from abc import abstractmethod
from pydantic import ConfigDict

from base.model_serve.model_runtime.entities.model_entities import Model<PERSON>roper<PERSON>Key, ModelType
from base.model_serve.model_runtime.entities.text_embedding_entities import TextEmbeddingResult
from base.model_serve.model_runtime.model_providers.__base.ai_model import AIModel

class TextEmbeddingModel(AIModel):
    """
    Model class for text embedding model.
    """

    model_type: ModelType = ModelType.TEXT_EMBEDDING

    # pydantic configs
    model_config = ConfigDict(protected_namespaces=())

    def invoke(
        self,
        model: str,
        credentials: dict,
        texts: list[str],
        user: Optional[str] = None,
    ) -> TextEmbeddingResult:
        """
        Invoke text embedding model

        :param model: model name
        :param credentials: model credentials
        :param texts: texts to embed
        :param user: unique user id
        :return: embeddings result
        """
        try:
            return self.invoke_text_embedding(
                tenant_id=self.tenant_id,
                user_id=user or "unknown",
                provider=self.provider_name,
                model=model,
                credentials=credentials,
                texts=texts,
            )
        except Exception as e:
            raise self._transform_invoke_error(e)
        
    @abstractmethod
    def invoke_text_embedding(
        self,
        tenant_id: str,
        user_id: str,
        provider: str,
        model: str,
        credentials: dict,
        texts: list[str],
    )-> TextEmbeddingResult:
        """
        Invoke text embedding model

        :param tenant_id: tenant id
        :param user_id: user id
        :param plugin_id: plugin id
        :param provider: provider name
        :param model: model name
        :param credentials: model credentials
        :param texts: texts to embed
        :return: embeddings result
        """
        raise NotImplementedError
    
    async def ainvoke(
        self,
        model: str,
        credentials: dict,
        texts: list[str],
        user: Optional[str] = None,
    ) -> TextEmbeddingResult:
        """
        异步调用文本嵌入模型

        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 要嵌入的文本列表
        :param user: 唯一用户ID
        :return: 嵌入结果
        """
        try:
            return await self.ainvoke_text_embedding(
                tenant_id=self.tenant_id,
                user_id=user or "unknown",
                provider=self.provider_name,
                model=model,
                credentials=credentials,
                texts=texts,
            )
        except Exception as e:
            raise self._transform_invoke_error(e)
    
    @abstractmethod
    async def ainvoke_text_embedding(
        self,
        tenant_id: str,
        user_id: str,
        provider: str,
        model: str,
        credentials: dict,
        texts: list[str],
    )-> TextEmbeddingResult:
        """
        异步调用文本嵌入模型

        :param tenant_id: 租户ID
        :param user_id: 用户ID
        :param provider: 提供商名称
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 要嵌入的文本列表
        :return: 嵌入结果
        """
        raise NotImplementedError

    def get_num_tokens(self, model: str, credentials: dict, texts: list[str]) -> list[int]:
        """
        Get number of tokens for given prompt messages

        :param model: model name
        :param credentials: model credentials
        :param texts: texts to embed
        :return:
        """
        return self.get_text_embedding_num_tokens(
            tenant_id=self.tenant_id,
            user_id="unknown",
            provider=self.provider_name,
            model=model,
            credentials=credentials,
            texts=texts,
        )

    def get_text_embedding_num_tokens(
        self,
        tenant_id: str,
        user_id: str,
        provider: str,
        model: str,
        credentials: dict,
        texts: list[str],
    ) -> list[int]:
        raise NotImplementedError