import time
from typing import Optional, List, Dict, Any
import aiohttp
import json
import requests
from decimal import Decimal
from loguru import logger
from pydantic import PrivateAttr, Field

from ..__base.text_embedding_model import TextEmbeddingModel
from ...entities.text_embedding_entities import TextEmbeddingResult, EmbeddingUsage


class GenericEmbedding(TextEmbeddingModel):
    """
    通用文本嵌入模型实现
    """

    base_url: str = Field(..., description="The base URL of the embedding model API.")
    api_key: Optional[str] = Field(None, description="The API key for authentication.")
    model_name: str = Field(..., description="The default model name for this embedding instance.")
    provider: str = Field(..., description="The provider name for this embedding instance.")

    _sync_session: Any = PrivateAttr()
    _async_session: Optional[aiohttp.ClientSession] = PrivateAttr(default=None)
    _headers: Dict[str, str] = PrivateAttr()

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self._sync_session = requests.Session()
        # _async_session is lazily initialized
        self._headers = {"Content-Type": "application/json"}
        if self.api_key:
            self._headers["Authorization"] = f"Bearer {self.api_key}"

    @property
    def async_session(self) -> aiohttp.ClientSession:
        """Lazily creates and returns the aiohttp ClientSession."""
        if self._async_session is None or self._async_session.closed:
            self._async_session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60, sock_read=30))
        return self._async_session

    def invoke(
        self,
        texts: list[str],
        model: Optional[str] = None,
        credentials: Optional[dict] = None, # `credentials` are passed in for compatibility with Dify, but we mainly use the configuration in `__init__`
        user: Optional[str] = None,
    ) -> TextEmbeddingResult:
        """
        调用文本嵌入模型

        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 要嵌入的文本列表
        :param user: 唯一用户ID
        :return: 嵌入结果
        """
        model = model or self.model_name
        try:
            return self.invoke_text_embedding(
                tenant_id=getattr(self, 'tenant_id', 'unknown'),
                user_id=user or "unknown",
                provider=getattr(self, 'provider_name', 'generic'),
                model=model,
                credentials=credentials,
                texts=texts,
            )
        except Exception as e:
            raise self._transform_invoke_error(e)

    async def ainvoke(
        self,
        texts: list[str],
        model: Optional[str] = None,
        credentials: Optional[dict] = None, # `credentials` are passed in for compatibility with Dify, but we mainly use the configuration in `__init__`
        user: Optional[str] = None,
    ) -> TextEmbeddingResult:
        """
        异步调用文本嵌入模型

        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 要嵌入的文本列表
        :param user: 唯一用户ID
        :return: 嵌入结果
        """
        model = model or self.model_name
        try:
            return await self.ainvoke_text_embedding(
                tenant_id=getattr(self, 'tenant_id', 'unknown'),
                user_id=user or "unknown",
                provider=getattr(self, 'provider_name', 'generic'),
                model=model,
                credentials=credentials,
                texts=texts,
            )
        except Exception as e:
            raise self._transform_invoke_error(e)

    def invoke_text_embedding(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> TextEmbeddingResult:
        """
        调用文本嵌入模型

        :param tenant_id: 租户ID
        :param user_id: 用户ID
        :param provider: 提供商名称
        :param model: 模型名称
        :param credentials: 模型凭证 (not used, configuration from __init__ is used)
        :param texts: 要嵌入的文本列表
        :return: 嵌入结果
        """
        start_time = time.perf_counter()
        
        payload = {
            "input": texts,
            "model": model
        }

        try:
            response = self._sync_session.post(self.base_url, json=payload, headers=self._headers, timeout=30)
            response.raise_for_status()
            response_data = response.json()

            embeddings = [item['embedding'] for item in response_data.get('data', [])]
            
            latency = time.perf_counter() - start_time
            usage = self._calc_embedding_usage(model, credentials, texts)
            usage.latency = latency

            return TextEmbeddingResult(
                model=model,
                embeddings=embeddings,
                usage=usage
            )
        except requests.RequestException as e:
            logger.error(f"Failed to get embeddings: {str(e)}")
            raise Exception(f"Failed to get embeddings: {str(e)}") from e

    async def ainvoke_text_embedding(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> TextEmbeddingResult:
        """
        异步调用文本嵌入模型

        :param tenant_id: 租户ID
        :param user_id: 用户ID
        :param provider: 提供商名称
        :param model: 模型名称
        :param credentials: 模型凭证 (not used, configuration from __init__ is used)
        :param texts: 要嵌入的文本列表
        :return: 嵌入结果
        """
        start_time = time.perf_counter()

        payload = {
            "input": texts,
            "model": model
        }
        
        try:
            async with self.async_session.post(self.base_url, json=payload, headers=self._headers) as response:
                response.raise_for_status()
                response_data = await response.json()
                
                embeddings = [item['embedding'] for item in response_data.get('data', [])]
                
                latency = time.perf_counter() - start_time
                usage = self._calc_embedding_usage(model, credentials, texts)
                usage.latency = latency

                return TextEmbeddingResult(
                    model=model,
                    embeddings=embeddings,
                    usage=usage
                )
        except aiohttp.ClientError as e:
            logger.error(f"Failed to get embeddings asynchronously: {str(e)}")
            raise Exception(f"Failed to get embeddings asynchronously: {str(e)}") from e

    def get_text_embedding_num_tokens(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> list[int]:
        """
        获取文本的令牌数

        :param tenant_id: 租户ID
        :param user_id: 用户ID
        :param provider: 提供商名称
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 文本列表
        :return: 每个文本的令牌数列表
        """
        # 简单示例：假设每个字符算一个令牌
        return [len(text) for text in texts]

    def _calc_embedding_usage(
            self,
            model: str,
            credentials: dict,
            texts: list[str]
    ) -> EmbeddingUsage:
        """
        计算嵌入使用情况
    
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 文本列表
        :return: 嵌入使用情况
        """
        # 计算总令牌数
        total_tokens = sum(len(text) for text in texts)

        # 示例价格计算
        unit_price = Decimal("0.0001")  # 每个令牌的单价
        price_unit = Decimal("1000")  # 价格单位（每1000个令牌）
        total_price = (Decimal(total_tokens) * unit_price) / price_unit

        # 计算延迟
        latency = 0.1  # 示例延迟时间

        return EmbeddingUsage(
            tokens=total_tokens,  # 添加 tokens 字段
            total_tokens=total_tokens,
            unit_price=unit_price,
            price_unit=price_unit,
            total_price=total_price,
            currency="USD",
            latency=latency
        )

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._async_session and not self._async_session.closed:
            await self._async_session.close()

    def __del__(self):
        if self._async_session and not self._async_session.closed:
            try:
                import asyncio
                loop = asyncio.get_running_loop()
                if loop.is_running():
                    loop.create_task(self._async_session.close())
            except RuntimeError: # Can happen if loop is already closed
                pass
            except Exception as e:
                logger.error(f"Error creating task to close async_session in __del__: {e}")
