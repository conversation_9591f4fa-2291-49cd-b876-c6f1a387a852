2025-05-28 09:40:15.116 +08:00 | INFO     | modules.db.client_factory:register_rdb_client:23 - 已注册关系型数据库客户端: mysql
2025-05-28 09:40:15.116 +08:00 | INFO     | modules.db.client_factory:register_rdb_client:23 - 已注册关系型数据库客户端: mysql_memory
2025-05-28 09:40:15.116 +08:00 | INFO     | modules.db.client_factory:register_vs_client:29 - 已注册向量数据库客户端: pgvector
2025-05-28 09:40:15.116 +08:00 | INFO     | modules.db.client_factory:register_vs_client:29 - 已注册向量数据库客户端: pgvector_advanced
2025-05-28 09:40:15.116 +08:00 | INFO     | modules.db.registry:register_all_clients:23 - 所有数据库客户端注册完成
2025-05-28 09:40:15.318 +08:00 | INFO     | modules.pg_database.mysql.mysql_fun:__init__:9 - Connecting to MySQL database...
2025-06-04 10:57:35.859 +08:00 | INFO     | api.fastapi_app:sse_schema:146 - 收到schema请求：{'index': {'modelId': '1', 'indexType': 'atom'}, 'dialogue': {'rounds': [{'inMessage': '采矿业的长期贷款的人民币本金是多少？', 'hint': ''}]}}
2025-06-04 10:57:35.860 +08:00 | INFO     | api.service_factory:create_text2sql_service:8 - Creating new Text2SQLService instance
2025-06-04 10:57:35.861 +08:00 | INFO     | api.fastapi_app:sse_schema:158 - 使用schema生成工作流步骤: ['information_retriever', 'column_retriever', 'column_selector', 'schema_generator']
2025-06-04 10:57:35.861 +08:00 | INFO     | api.fastapi_app:sse_schema:163 - 用户提问：[{'inMessage': '采矿业的长期贷款的人民币本金是多少？', 'hint': ''}]
2025-06-04 10:57:35.861 +08:00 | INFO     | utils.llm.dialogue:process_dialog_from_frontend:122 - [用户问题]: 【历史对话】
【本轮用户问题】采矿业的长期贷款的人民币本金是多少？
2025-06-04 10:57:35.861 +08:00 | INFO     | api.fastapi_app:sse_schema:165 - 处理后的用户提问：【历史对话】
【本轮用户问题】采矿业的长期贷款的人民币本金是多少？
2025-06-04 10:57:35.864 +08:00 | INFO     | utils.common.time_utils:timer:21 - 处理查询任务时间：0.0000秒
2025-06-04 10:57:35.865 +08:00 | INFO     | workflow.workflow_manager:execute_workflow:55 - 【STEP-0：<information_retriever>】【START】
2025-06-04 10:57:35.866 +08:00 | SUCCESS  | utils.llm.prompts:_load_template:33 - Template information_extractor_user loaded successfully.
2025-06-04 10:57:35.867 +08:00 | SUCCESS  | utils.llm.prompts:_load_template:33 - Template information_extractor_system loaded successfully.
2025-06-04 10:57:35.868 +08:00 | INFO     | workflow.base.base_llm_step:call_llm_chain:400 - call_llm_chain.开始并行执行 1 次LLM调用
2025-06-04 10:57:35.868 +08:00 | DEBUG    | workflow.base.base_llm_step:_increment_llm_call_count:332 - LLM调用次数: 1/1
2025-06-04 10:57:35.869 +08:00 | INFO     | utils.llm.llm_util:async_call_opentrek:225 - Opentrek Request Payload: {'model': 'qwen_vllm', 'messages': [{'role': 'system', 'content': 'Human: Objective: 分析用户的查询请求和提示信息，提取关键信息并以结构化方式输出，包括时间信息、行业类别、贷款期限类型和计算金额类型。\n\nInstructions:\n\n1. 时间提取：\n   - 分析用户查询中提到的时间信息（如：上个月、本季度、今年以来等）\n   - 如果用户未提供时间信息，默认不考虑时间维度\n   - 提取时间粒度，以("day","month","quarter","year")为粒度，如果没有提供时间，默认时间粒度为"day"\n\n2. 关键词信息提取\n2.1 重点关注如下字段信息提取和整合：\n   - 行业类别：例如"农、林、畜牧业"、"采矿业"、"制造业"等\n   - 贷款期限类型：长期或短期, 英文为short_term, long_term\n   - 计算金额类型：人民币、美元的本金和放贷金额等, 英文包括loan_bal_cny, loan_bal_usd, loan_amt_cny, loan_amt_usd\n\n2.2 对于语序混乱的信息，如：\n问题：请帮我查找农林畜牧业上个月人民币的贷款的本金是多少，哦对了需要短期的\n提取信息整合思路：\n行业类别：农、林、畜牧业\n贷款期限类型：短期\n计算金额类型：人民币本金\n2.3 对于其他信息，以语义颗粒度适中的方式进行提取，需要各个关键词尽量独立。\n2.4 仔细阅读提示信息，补充用户问题中未提供，但潜在的或者必要的信息，如业务逻辑，提取到keywords里。\n\n\n3. 根据信息完整性判断状态：\n   - 完整信息：返回"success"状态\n   - 缺少计算金额类型：返回"warning"状态，默认为"人民币本金"\n   - 缺少行业、期限或时间：返回"error"状态，并提示用户补充信息\n\n4. 业务口径生成\n- 根据提取的关键词信息，日期信息，进行总结，使得语义通达完整，生成提示信息\n\n5. 结构化输出格式：\n  ```json\n  {\n    "status": "success|warning|error",\n    "time": "提取的时间信息",\n    "time_period": "提取的时间粒度",\n    "keywords": ["行业类别", "期限类型", "计算金额类型","业务逻辑",...],\n    "keywords_en": ["Business Type/Loan Purpose/IS_HIGH_TECH_INDUSTRY/DIGITAL_INDUSTRY_TYPE/IS_KNOWLEDGE_INDUSTRY", "Loan Term", "Currency Type","Business Logic",... ],\n    "caliberBiz": "业务口径：查询【日期】【行业】【期限】【金额】",\n    "message": "提示信息（如有）"\n  }\n  ```\n\n示例1:\n用户查询: "请帮我查找上个月农林畜牧业的短期贷款的人民币本金是多少"\n提示信息: "银保监会要求，贷款余额的计算必须基于银保监会要求的口径，即 `adm_lon_varoius_is_cbirc_loan = \'Y\'`"\n输出:\n```json\n{\n  "status": "success",\n  "time": "上个月",\n  "time_period": "month",\n  "keywords": ["农、林、畜牧业", "短期", "人民币本金","是银保监会要求"],\n  "keywords_en": ["Agriculture, Forestry and Farming", "short_term", "loan_bal_cny","is cbirc loan"],\n  "caliberBiz": "查询【日期】上个月【行业】农、林、畜牧业【期限】短期【金额】人民币本金",\n  "message": ""\n}\n```\n\n示例2:\n用户查询: "查一下制造业的短期贷款金额"\n提示信息: "银保监会要求，贷款余额的计算必须基于银保监会要求的口径，即 `adm_lon_varoius_is_cbirc_loan = \'Y\'`"\n输出:\n```json\n{\n  "status": "warning",\n  "time": "",\n  "time_period": "day",\n  "keywords": ["制造业", "短期", "人民币本金","是银保监会要求"],\n  "keywords_en": ["Manufacturing", "short_term", "loan_bal_cny","is cbirc loan"],\n  "caliberBiz": "查询【行业】制造业【期限】短期【金额】人民币本金",\n  "message": "用户未提供贷款期限类型和计算金额类型，默认为人民币本金.用户未提供时间信息，默认不考虑时间维度"\n}\n```\n\n示例3:\n用户查询: "贷款情况怎么样"\n提示信息: "银保监会要求，贷款余额的计算必须基于银保监会要求的口径，即 `adm_lon_varoius_is_cbirc_loan = \'Y\'`"\n输出:\n```json\n{\n  "status": "error",\n  "time": "",\n  "time_period": "day",\n  "keywords": ["", "", "","是银保监会要求"],\n  "keywords_en": ["", "", "",""],\n  "caliberBiz": "",\n  "message": "用户未提供行业类别、贷款期限类型、时间信息，请补充这些信息"\n}\n```\n\nTask:\n分析以下用户查询，提取关键信息并以规定的JSON格式输出。\n\n用户查询: 【历史对话】\n【本轮用户问题】采矿业的长期贷款的人民币本金是多少？\n\n请注意：\n1. 如果未提供计算金额类型，默认为"人民币本金"并使用"warning"状态；\n2. 如果未提供时间信息，默认不考虑时间维度，使用"warning"状态；\n3. 如果缺少行业类别、贷款期限，请使用"error"状态并在message中提示原因，优先级高于缺少计算金额类型'}, {'role': 'user', 'content': 'Human: 用户查询为：\n【历史对话】\n【本轮用户问题】采矿业的长期贷款的人民币本金是多少？\n提示信息为：\n**重要说明：数据模型结构**\n\n        *   **真实表名**: 实际操作的表名格式为 **\'table_模型ID\'** (例如: `table_1`, `table_12345`)，其中 `模型ID` 是具体的模型标识符。\n        *   **真实列名**: 列名是 **原始表名** 和 **原始字段名** 的组合，用下划线连接 (例如: `adm_lon_varoius_term_type`, `bdm_acc_creditcard_installment_acct_bal`)。列名中的原始字段名可能对应元数据中的字段ID (如 `term_type` 对应 `adm_lon_varoius_field87`)，实际生成SQL时需确认准确的列名。为便于理解，以下规则使用 `原始表名_字段名` 形式。\n        *   **操作基础**: 所有查询和计算都 **基于这张单一的模型宽表** (例如: `table_1`) 进行\n    **重要说明：业务逻辑**\n    虽然用户查询中不一定会明确提到，但以下业务逻辑是必须遵守的：\n    1. 贷款余额的计算必须基于银保监会要求的口径，即 `adm_lon_varoius_is_cbirc_loan = \'Y\'`\n    \n    \n    请根据以下指导解析用户查询，生成SQL或表达式：\n\n    1.  **查询模式**:\n        *   **原子指标**: "查询[日期] [维度组合] 贷款余额"。维度组合可能包括行业、业务类型、期限等。示例："查询2024年底 制造业 中长期 贷款余额"。\n        *   **计算指标**: "计算[指标A]和[指标B]的[和/差等]"。通常涉及多个原子指标的聚合。示例："计算 个人住房按揭贷款 和 个人其他贷款 的总和"。\n\n    2.  **原子指标核心规则**:\n        *   **默认口径**: 基于`adm_lon_varoius`相关列的查询基于银保监会要求，必须包含条件 `adm_lon_varoius_is_cbirc_loan = \'Y\'` (是银保监各项贷款口径, Y)。\n        *   **目标字段**: 主要查询目标为贷款余额，通常是 `adm_lon_varoius_loan_bal_cny`。\n        *   **期限**: 通过 `.adm_lon_varoius_term_type` 过滤。\n            *   "短期" 对应 `\'01\'` 。\n            *   "中长期" 对应 `\'02\'` 。\n            *   "各项贷款" 通常指所有期限（不按 `term_type` 过滤或包含所有类型）。\n\n    3.  **关键维度映射 (基于 模型表 中的组合列)**:\n        *   **行业 (adm_lon_varoius_loan_purpose)**: 通常通过 `like \'X%\'` 过滤。\n            *   \'A%\': 农、林、牧、渔业 (代码 \'A\')\n            *   \'B%\': 采矿业 (代码 \'B\')\n            *   \'C%\': 制造业 (代码 \'C\')\n            *   \'D%\': 电力、热力、燃气及水的生产和供应业 (代码 \'D\')\n            *   \'E%\': 建筑业 (代码 \'E\')\n            *   \'F%\': 批发和零售业 (代码 \'F\')\n            *   \'G%\': 交通运输、仓储和邮政业 (代码 \'G\')\n            *   \'H%\': 住宿和餐饮业 (代码 \'H\')\n            *   \'I%\': 信息传输、计算机服务和软件业 (代码 \'I\')\n            *   \'J%\': 金融业 (代码 \'J\')\n            *   \'K%\': 房地产业 (代码 \'K\')\n            *   \'L%\': 租赁和商务服务业 (代码 \'L\')\n            *   \'M%\': 科学研究和技术服务业 (代码 \'M\')\n            *   \'N%\': 水利、环境和公共设施管理业 (代码 \'N\')\n            *   \'O%\': 居民服务、修理和其他服务业 (代码 \'O\')\n            *   \'P%\': 教育 (代码 \'P\')\n            *   \'Q%\': 卫生、社会工作 (代码 \'Q\')\n            *   \'R%\': 文化、体育和娱乐业 (代码 \'R\')\n            *   \'S%\': 公共管理、社会保障和社会组织 (代码 \'S\')\n            *   \'T%\': 国际组织 (代码 \'T\')\n            *   \'Z%\': 对境外贷款 (代码 \'Z\')\n        *   **业务类型 (adm_lon_varoius_business_type)**:\n            *   \'19\': 个人贷款-信用卡\n            *   \'020201\': 个人贷款-住房按揭贷款\n            *   \'020202\': 个人贷款-汽车\n            *   \'020299\': 个人贷款-其他\n            *   \'17\': 买断式转贴现\n            *   \'18\': 买断其他票据类资产\n        *   **高技术产业 (adm_lon_varoius_is_high_tech_industry)**:\n            *   \'Y\': 是高技术产业\n            *   \'N\': 不是高技术产业\n        *   **知识产权密集型产业 (adm_lon_varoius_is_kownledge_industry)**:\n            *   \'Y\': 是知识产权密集型产业\n            *   \'N\': 不是知识产权密集型产业\n        *   **数字经济产业分类 (adm_lon_varoius_digital_industry_type)**:\n            *   \'01\': 数字产品制造业\n            *   \'02\': 数字产品服务业\n            *   \'03\': 数字技术应用业\n            *   \'04\': 数字要素驱动业\n\n    4.  **特殊逻辑处理**:\n        *   **信用卡中长期贷款**: 需要合并两部分计算（注意列名和代码,以table_1为例），伪代码如下：`SUM_IF(table_1 where adm_lon_varoius_term_type=\'02\' and table_1.adm_lon_varoius_business_type=\'19\', table_1.adm_lon_varoius_loan_bal_cny) + SUM_IF(table_1 where table_1.bdm_acc_creditcard_installment_stageing_term > 12, table_1.bdm_acc_creditcard_installment_acct_bal)`。\n        *   **养老产业贷款**: 过滤条件复杂，需同时考虑 `adm_lon_varoius_loan_purpose`  的特定值列表以及 `adm_lon_varoius_indu_type` (注意: indu_type元数据未提供) 与另一组 `loan_purpose` 列表的组合条件。\n\n    请运用这些详细规则和精确的元数据映射来理解用户意图并构建准确的查询逻辑。\n    \n请提供一个完整的结构化JSON输出，包含状态、时间信息、关键词列表、业务口径及报错的提示信息。\n请注意：\n1. 如果未提供计算金额类型，默认为"人民币本金"并使用"warning"状态；\n2. 如果未提供时间信息，默认不考虑时间维度，使用"warning"状态；\n3. 如果缺少行业类别、贷款期限，请使用"error"状态并在message中提示原因，优先级高于缺少计算金额类型\n输出格式：\n{\n  "status": "success|warning|error",\n  "time": "提取的时间信息",\n  "time_period": "提取的时间粒度",\n  "keywords": ["行业类别", "期限类型", "计算金额类型"],\n  "keywords_en": ["Business Type/Loan Purpose/IS_HIGH_TECH_INDUSTRY/DIGITAL_INDUSTRY_TYPE/IS_KNOWLEDGE_INDUSTRY", "Loan Term", "Currency Type"],\n  "caliberBiz": "业务口径：查询【日期】【行业】【期限】【金额】",\n  "message": "提示信息（如有）"\n}\n\n'}], 'stream': True, 'max_tokens': 10000}
2025-06-04 10:57:35.887 +08:00 | INFO     | utils.llm.llm_util:async_call_opentrek:241 - Opentrek Stream Request Initiated
2025-06-04 10:57:35.887 +08:00 | DEBUG    | utils.llm.llm_util:__init__:271 - OpentrekStream initialized.
2025-06-04 10:57:37.071 +08:00 | INFO     | utils.api.text2sql_event_generator:create_event_generator:183 - API处理被取消，已处理时间：1.2127秒
2025-06-04 10:57:37.072 +08:00 | INFO     | utils.common.time_utils:timer:21 - Text2SQL服务处理时间：1.2079秒
