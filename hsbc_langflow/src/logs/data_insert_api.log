nohup: ignoring input
INFO:     Started server process [474808]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:31318 (Press CTRL+C to quit)
['/data/ideal/code/gsh_code/text2SQL/hsbc/src/api', '/usr/lib/python312.zip', '/usr/lib/python3.12', '/usr/lib/python3.12/lib-dynload', '/usr/local/lib/python3.12/dist-packages', '/usr/lib/python3/dist-packages', '/data/ideal/code/gsh_code/text2SQL/hsbc/src']
['/data/ideal/code/gsh_code/text2SQL/hsbc/src/api', '/usr/lib/python312.zip', '/usr/lib/python3.12', '/usr/lib/python3.12/lib-dynload', '/usr/local/lib/python3.12/dist-packages', '/usr/lib/python3/dist-packages', '/data/ideal/code/gsh_code/text2SQL/hsbc/src', '/data/ideal/code/gsh_code/text2SQL/hsbc/src']
Database connection pool created with 10 to 30 connections.
PgVectorClient initialized
MySQLClient initialized
Deleted 0 entities from hsbc_embedding_data successfully.
Inserted 3 entities into hsbc_embedding_data with partition_key=p_7________________jxTkX87qFn successfully.
INFO:     **************:51432 - "POST /indicator_insert HTTP/1.1" 200 OK
