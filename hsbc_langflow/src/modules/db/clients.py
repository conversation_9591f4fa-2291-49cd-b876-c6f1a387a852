'''
File Created: Saturday, 7th June 2025 8:59:08 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 9:35:34 am
'''

"""
数据库客户端工厂与缓存 (The "Back Kitchen")

本模块的职责:
1.  **充当数据库客户端的工厂 (Factory)**。`get_rdb_client` 和 `get_vdb_client`
    函数根据全局 `config` 对象中的配置，使用 `hydra.utils.instantiate` 来创建
    具体的客户端实例（如 MySQLClient, PGVectorClient 等）。
2.  **实现客户端实例的缓存与单例管理**。通过内部的 `_client_registry` 字典，
    本模块确保了对于每种类型的数据库（RDB, VDB），在整个应用的生命周期中只创建
    一个客户端实例。后续的 `get_*_client` 调用将直接返回缓存的实例。

与 `utils.db.common.client_util` 的关系:
-   本模块是 **"后厨"**，负责客户端的实际制造和管理。
-   `utils.db.common.client_util` 是 **"服务员"**，是应用层代码与后厨之间的中间人。
-   服务员（`LazyClient`）会调用这里的 `get_*_client` 函数来获取客户端实例。

解决的问题:
-   **保证了数据库客户端的全局单例性**，避免了重复创建数据库连接，节约了系统资源。
-   **将客户端的创建逻辑与配置解耦**，所有创建细节都由 Hydra 和这里的工厂函数处理。
"""

# modules/db/clients.py (改进设计)

from loguru import logger
from typing import Dict, Any, Optional, Callable
import hydra
from omegaconf import OmegaConf

# 导入我们改造后的单例config
from utils.common.config_util import config 

# 这是一个简单的字典，用作我们单例的"注册表"
# 它将缓存由Hydra创建的客户端实例
_client_registry: Dict[str, Any] = {}

# 用于测试的客户端工厂函数
_rdb_factory: Optional[Callable[[], Any]] = None
_vdb_factory: Optional[Callable[[], Any]] = None

def set_client_factory(client_type: str, factory_func: Callable[[], Any]) -> None:
    """
    设置客户端工厂函数，主要用于测试。
    在测试中，可以使用此函数注入模拟的客户端，避免实际连接数据库。
    
    Args:
        client_type: 客户端类型，'rdb'或'vdb'
        factory_func: 创建客户端实例的工厂函数
    """
    global _rdb_factory, _vdb_factory
    if client_type.lower() == 'rdb':
        _rdb_factory = factory_func
        # 重置缓存，确保下次调用get_rdb_client时使用新工厂
        if "rdb_primary" in _client_registry:
            del _client_registry["rdb_primary"]
    elif client_type.lower() == 'vdb':
        _vdb_factory = factory_func
        if "vdb_primary" in _client_registry:
            del _client_registry["vdb_primary"]
    else:
        raise ValueError(f"Unknown client type: {client_type}")

def reset_client_registry() -> None:
    """
    重置客户端注册表，主要用于测试。
    这将清空所有缓存的客户端实例。
    """
    _client_registry.clear()
    logger.info("Client registry has been reset")

def get_rdb_client() -> Any:
    """
    获取单例的关系型数据库（RDB）客户端。
    客户端的具体实现（如MySQL, PostgreSQL）由Hydra配置决定。
    第一次调用时，它会使用全局配置来创建实例并缓存。
    后续调用将直接返回缓存的实例。
    
    如果设置了自定义工厂函数，将使用工厂函数创建客户端（测试用途）。
    """
    client_key = "rdb_primary"
    if client_key not in _client_registry:
        # 优先使用自定义工厂函数（如果有）
        if _rdb_factory is not None:
            logger.info("Using custom RDB factory function (for testing)")
            client = _rdb_factory()
            _client_registry[client_key] = client
            return client
            
        # 否则使用标准配置
        logger.info("RDB client not found in cache. Attempting to create and connect...")
        # 关键：从我们全局的、已初始化的config对象中获取RDB的配置
        rdb_cfg = config.primary_rdb
        if rdb_cfg is None or not hasattr(rdb_cfg, '_target_'):
            logger.error("RDB configuration is missing or invalid. It must contain a '_target_' key for instantiation.")
            raise ValueError("RDB configuration is missing or does not contain a '_target_' key for instantiation.")

        # 使用Hydra的instantiate方法，根据配置自动创建正确的客户端实例
        target_class = getattr(rdb_cfg, '_target_', 'N/A')
        logger.info(f"Instantiating RDB client using config: target={target_class}")
        client = hydra.utils.instantiate(rdb_cfg)
        client.connect() # 创建实例后立即连接
        
        _client_registry[client_key] = client
        logger.info(f"Successfully created, connected, and cached a new RDB client of type '{rdb_cfg.db_type}'.")
    
    return _client_registry[client_key]

def get_vdb_client() -> Any:
    """
    获取单例的向量数据库（VDB）客户端。
    客户端的具体实现（如PGVector, Milvus）由Hydra配置决定。
    逻辑同 get_rdb_client。
    
    如果设置了自定义工厂函数，将使用工厂函数创建客户端（测试用途）。
    """
    client_key = "vdb_primary"
    if client_key not in _client_registry:
        # 优先使用自定义工厂函数（如果有）
        if _vdb_factory is not None:
            logger.info("Using custom VDB factory function (for testing)")
            client = _vdb_factory()
            _client_registry[client_key] = client
            return client
            
        # 否则使用标准配置
        logger.info("VDB client not found in cache. Attempting to create and connect...")
        vdb_cfg = config.primary_vdb
        if vdb_cfg is None or not hasattr(vdb_cfg, '_target_'):
            logger.error("VDB configuration is missing or invalid. It must contain a '_target_' key for instantiation.")
            raise ValueError("VDB configuration is missing or does not contain a '_target_' key for instantiation.")

        target_class = getattr(vdb_cfg, '_target_', 'N/A')
        logger.info(f"Instantiating VDB client using config: target={target_class}")
        client = hydra.utils.instantiate(vdb_cfg)
        client.connect() # 创建实例后立即连接

        _client_registry[client_key] = client
        logger.info(f"Successfully created, connected, and cached a new VDB client of type '{vdb_cfg.db_type}'.")

    return _client_registry[client_key]

# ==================== 使用建议 ====================
# 不再推荐使用模块级变量，因为它们会在导入时执行，
# 而此时Hydra配置可能尚未加载，会导致错误。
#
# 正确的使用方式是在需要的地方直接调用函数:
# from modules.db.clients import get_rdb_client
#
# def some_function():
#     db_client = get_rdb_client()
#     db_client.select(...) 

# ==================== 测试示例 ====================
# 在测试中，可以使用set_client_factory注入模拟客户端:
# 
# from unittest.mock import MagicMock
# from modules.db.clients import set_client_factory, get_rdb_client, reset_client_registry
# 
# def setup_function():
#     reset_client_registry()  # 清空缓存
#     mock_client = MagicMock()
#     mock_client.query.return_value = ["test_data"]
#     set_client_factory('rdb', lambda: mock_client)
# 
# def test_db_query():
#     client = get_rdb_client()  # 将返回模拟客户端
#     result = client.query("SELECT * FROM test")
#     assert result == ["test_data"] 