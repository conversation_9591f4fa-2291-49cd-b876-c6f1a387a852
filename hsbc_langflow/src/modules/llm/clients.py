'''
File Created: Tuesday, 11th June 2025 3:05:10 pm
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
'''

"""
模型服务提供者工厂与缓存 (The "Model Kitchen")

本模块的职责:
1.  **充当模型提供者的工厂 (Factory)**。`get_llm_provider` 和 `get_embedding_provider`
    函数根据全局 `config` 对象中的配置，使用 `hydra.utils.instantiate` 来创建
    具体的模型服务实例（如 OpenTrekLLM, SampleEmbedding 等）。
2.  **实现模型提供者的缓存与单例管理**。通过内部的 `_provider_registry` 字典，
    本模块确保了对于每种类型的模型服务（LLM, Embedding），在整个应用的生命周期中只创建
    一个实例。后续的调用将直接返回缓存的实例。

解决的问题:
-   **保证了模型服务实例的全局单例性**，避免了重复加载模型或重复创建API客户端，节约了系统资源。
-   **将模型服务的实例化逻辑与配置解耦**，所有创建细节都由 Hydra 和这里的工厂函数处理。
"""

from loguru import logger
from typing import Dict, Any, Optional, Callable
import hydra

from utils.common.config_util import config

# 全局注册表，用于缓存模型提供者实例
_provider_registry: Dict[str, Any] = {}

# 用于测试的工厂函数
_llm_factory: Optional[Callable[[], Any]] = None
_embedding_factory: Optional[Callable[[], Any]] = None


def set_provider_factory(provider_type: str, factory_func: Callable[[], Any]) -> None:
    """
    设置模型提供者工厂函数，主要用于测试。
    
    Args:
        provider_type: 'llm' 或 'embedding'
        factory_func: 创建实例的工厂函数
    """
    global _llm_factory, _embedding_factory
    if provider_type.lower() == 'llm':
        _llm_factory = factory_func
        if "llm_primary" in _provider_registry:
            del _provider_registry["llm_primary"]
    elif provider_type.lower() == 'embedding':
        _embedding_factory = factory_func
        if "embedding_primary" in _provider_registry:
            del _provider_registry["embedding_primary"]
    else:
        raise ValueError(f"Unknown provider type: {provider_type}")

def reset_provider_registry() -> None:
    """重置提供者注册表，主要用于测试。"""
    _provider_registry.clear()
    logger.info("Model provider registry has been reset")

def get_llm_provider() -> Any:
    """
    获取单例的 LLM 提供者实例。
    """
    provider_key = "llm_primary"
    if provider_key not in _provider_registry:
        if _llm_factory:
            logger.info("Using custom LLM provider factory (for testing)")
            provider = _llm_factory()
        else:
            logger.info("LLM provider not in cache. Creating from config...")
            llm_cfg = config.llm
            if not hasattr(llm_cfg, '_target_'):
                raise ValueError("LLM configuration is missing '_target_' key.")
            
            # 使用 Hydra 实例化
            # **注意**: 我们只传递 'api_key' 和 'base_url'
            # provider 的 __init__ 方法应该只接收它需要的参数
            init_args = {
                'api_key': llm_cfg.api_key,
                'base_url': llm_cfg.base_url
            }
            provider = hydra.utils.instantiate(llm_cfg, **init_args)
            provider.config = llm_cfg
        
        _provider_registry[provider_key] = provider
        logger.info(f"Successfully created and cached LLM provider: {type(provider).__name__}")
    
    return _provider_registry[provider_key]

def get_embedding_provider() -> Any:
    """
    获取单例的 Embedding 提供者实例。
    """
    provider_key = "embedding_primary"
    if provider_key not in _provider_registry:
        if _embedding_factory:
            logger.info("Using custom Embedding provider factory (for testing)")
            provider = _embedding_factory()
        else:
            logger.info("Embedding provider not in cache. Creating from config...")
            embedding_cfg = config.embedding
            if not hasattr(embedding_cfg, '_target_'):
                raise ValueError("Embedding configuration is missing '_target_' key.")
            
            init_args = {
                'api_key': embedding_cfg.api_key,
                'base_url': embedding_cfg.base_url
            }
            provider = hydra.utils.instantiate(embedding_cfg, **init_args)
            provider.config = embedding_cfg
            
        _provider_registry[provider_key] = provider
        logger.info(f"Successfully created and cached Embedding provider: {type(provider).__name__}")
        
    return _provider_registry[provider_key]


__all__ = [
    'get_llm_provider',
    'get_embedding_provider',
    'set_provider_factory',
    'reset_provider_registry'
]
