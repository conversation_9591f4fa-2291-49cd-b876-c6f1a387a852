import pickle
from datasketch import MinHash, MinHashLSH
from pathlib import Path
import logging
import os
import yaml
from typing import Dict, Tuple, List, Union, Optional

from database_utils.retrieval.db_values.preprocess import _create_minhash
from database_utils.pg_database.data.pg_db import PgSqlClient

### Database value similarity ###

def load_config() -> Dict:
    """
    加载配置文件
    
    返回:
        Dict: 配置信息
    """
    config_path = Path(__file__).parent.parent.parent.parent / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def _jaccard_similarity(m1: MinHash, m2: MinHash) -> float:
    """
    计算两个MinHash对象之间的Jaccard相似度。

    参数:
        m1 (MinHash): 第一个MinHash对象。
        m2 (MinHash): 第二个MinHash对象。

    返回:
        float: 两个MinHash对象之间的Jaccard相似度。
    """
    return m1.jaccard(m2)

def load_db_lsh(db_directory_path: str, db_id: Optional[str] = None) -> Tuple[MinHashLSH, Dict[str, Tuple[MinHash, str, str, str]]]:
    """
    从指定目录中的预处理文件加载LSH和MinHashes。

    参数:
        db_directory_path (str): 数据库目录路径。
        db_id (Optional[str]): 数据库ID。如果为None，则从目录路径中提取。

    返回:
        Tuple[MinHashLSH, Dict[str, Tuple[MinHash, str, str, str]]]: LSH对象和MinHashes字典。

    抛出:
        Exception: 如果加载LSH或MinHashes出错。
    """
    if db_id is None:
        db_id = Path(db_directory_path).name
    
    try:
        # 检查是否存在预处理子目录
        if (Path(db_directory_path) / "preprocessed" / db_id).exists():
            # 新格式路径（PostgreSQL或其他非SQLite数据库）
            lsh_path = Path(db_directory_path) / "preprocessed" /  f"{db_id}_lsh.pkl"
            minhashes_path = Path(db_directory_path) / "preprocessed" / f"{db_id}_minhashes.pkl"
        else:
            # 旧格式路径（SQLite）
            lsh_path = Path(db_directory_path) / "preprocessed" / f"{db_id}_lsh.pkl"
            minhashes_path = Path(db_directory_path) / "preprocessed" / f"{db_id}_minhashes.pkl"
        
        # 读取LSH和MinHashes
        with open(lsh_path, "rb") as file:
            lsh = pickle.load(file)
        with open(minhashes_path, "rb") as file:
            minhashes = pickle.load(file)
        
        return lsh, minhashes
    except Exception as e:
        logging.error(f"加载 {db_id} 的LSH时出错: {e}")
        raise e

def query_lsh(lsh: MinHashLSH, minhashes: Dict[str, Tuple[MinHash, str, str, str]], keyword: str, 
              signature_size: int = 100, n_gram: int = 3, top_n: int = 10) -> Dict[str, Dict[str, List[str]]]:
    """
    查询LSH以查找与给定关键字相似的值，并返回最相似的结果。

    参数:
        lsh (MinHashLSH): LSH对象。
        minhashes (Dict[str, Tuple[MinHash, str, str, str]]): MinHashes字典。
        keyword (str): 要搜索的关键字。
        signature_size (int, optional): MinHash签名的大小。
        n_gram (int, optional): 用于MinHash的n-gram大小。
        top_n (int, optional): 返回的最相似结果数量。

    返回:
        Dict[str, Dict[str, List[str]]]: 包含最相似值的字典。
    """
    query_minhash = _create_minhash(signature_size, keyword, n_gram)
    results = lsh.query(query_minhash)
    similarities = [(result, _jaccard_similarity(query_minhash, minhashes[result][0])) for result in results]
    similarities = sorted(similarities, key=lambda x: x[1], reverse=True)[:top_n]

    similar_values_trimmed: Dict[str, Dict[str, List[str]]] = {}
    for result, similarity in similarities:
        table_name, column_name, value = minhashes[result][1:]
        if table_name not in similar_values_trimmed:
            similar_values_trimmed[table_name] = {}
        if column_name not in similar_values_trimmed[table_name]:
            similar_values_trimmed[table_name][column_name] = []
        similar_values_trimmed[table_name][column_name].append(value)

    return similar_values_trimmed

def find_db_values(db_directory_path: str, keyword: str, db_type: str = "postgresql", 
                  db_id: Optional[str] = None, signature_size: int = 100, 
                  n_gram: int = 3, top_n: int = 10) -> Dict[str, Dict[str, List[str]]]:
    """
    在数据库中查找与关键字相似的值。

    参数:
        db_directory_path (str): 数据库目录路径。
        keyword (str): 要搜索的关键字。
        db_type (str): 数据库类型 ("sqlite", "postgresql" 等)。
        db_id (Optional[str]): 数据库ID。如果为None，则从目录路径中提取。
        signature_size (int): MinHash签名的大小。
        n_gram (int): 用于MinHash的n-gram大小。
        top_n (int): 返回的最相似结果数量。

    返回:
        Dict[str, Dict[str, List[str]]]: 包含最相似值的字典。
    """
    # 确定数据库ID
    if db_id is None:
        db_id = Path(db_directory_path).name
    
    # 加载LSH和MinHashes
    lsh, minhashes = load_db_lsh(db_directory_path, db_id)
    
    # 查询LSH
    return query_lsh(lsh, minhashes, keyword, signature_size, n_gram, top_n)

def create_pg_client_from_config() -> PgSqlClient:
    """
    从配置文件创建PostgreSQL客户端。
    
    返回:
        PgSqlClient: PostgreSQL客户端实例。
    """
    config = load_config()
    db_config = config.get('database', {})
    
    return PgSqlClient(
        db_name=db_config.get('database', 'adm'),
        host=db_config.get('host', '**************'),
        port=int(db_config.get('port', 30146)),
        user=db_config.get('user', 'pgvector'),
        password=db_config.get('password', 'pgvector')
    )
