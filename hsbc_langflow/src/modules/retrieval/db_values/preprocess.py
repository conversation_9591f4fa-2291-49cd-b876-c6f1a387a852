import pickle  # 导入pickle模块，用于序列化和反序列化Python对象
from datasketch import MinHash, MinHashLSH  # 导入MinHash和MinHashLSH类，用于局部敏感哈希
from pathlib import Path  # 导入Path类，用于处理文件路径
from tqdm import tqdm  # 导入进度条工具
import logging  # 导入日志模块
from typing import Dict, List, Any, Tuple, Optional, Union  # 导入类型提示工具
import os
from urllib.parse import quote_plus
import yaml  # 导入yaml模块，用于读取配置文件

from database_utils.retrieval.async_sqlalchemy_execution import execute_sql, create_database_config  # 执行SQL查询
from database_utils.pg_database.pgvector.pgvector_class import PgVectorClass  # 导入PostgreSQL类
from database_utils.pg_database.data.pg_db import PgSqlClient  # 导入PostgreSQL客户端类

def load_config() -> Dict:
    """
    加载配置文件
    
    返回:
        Dict: 配置信息
    """
    config_path = Path(__file__).parent.parent.parent.parent / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def _get_unique_values_sqlite(db_path: str) -> Dict[str, Dict[str, List[str]]]:
    """
    从SQLite数据库中检索唯一的文本值，排除主键。
    
    参数:
        db_path (str): 数据库路径或连接字符串。
    
    返回:
        Dict[str, Dict[str, List[str]]]: 包含每个表和列的唯一值的字典。
    """
    # 创建数据库配置
    db_config = create_database_config(db_path, db_type="sqlite")
    
    # 获取数据库中所有表的名称
    table_names = [table[0] for table in execute_sql(db_config, "SELECT name FROM sqlite_master WHERE type='table';", fetch="all")]
    primary_keys = []  # 初始化主键列表

    # 遍历所有表，收集主键信息
    for table_name in table_names:
        # 获取表的列信息
        columns = execute_sql(db_config, f"PRAGMA table_info('{table_name}')", fetch="all")
        for column in columns:
            if column[5] > 0:  # 检查是否为主键（column[5]>0表示是主键）
                column_name = column[1]
                # 避免主键名称重复（不区分大小写）
                if column_name.lower() not in [c.lower() for c in primary_keys]:
                    primary_keys.append(column_name)
    
    # 初始化存储唯一值的字典
    unique_values: Dict[str, Dict[str, List[str]]] = {}
    
    # 遍历每个表，收集文本列的唯一值
    for table_name in table_names:
        if table_name == "sqlite_sequence":  # 跳过sqlite内部表
            continue
        logging.info(f"Processing {table_name}")  # 记录当前处理的表名
        
        # 获取表中所有是文本类型且不是主键的列
        columns = [col[1] for col in execute_sql(db_config, f"PRAGMA table_info('{table_name}')", fetch="all") 
                  if ("TEXT" in col[2] and col[1].lower() not in [c.lower() for c in primary_keys])]
        
        # 初始化当前表的列值字典
        table_values: Dict[str, List[str]] = {}
        
        # 处理每一列
        for column in columns:
            # 跳过ID、URL、Email等常见的非文本内容列
            if any(keyword in column.lower() for keyword in ["_id", " id", "url", "email", "web", "time", "phone", "date", "address"]) or column.endswith("Id"):
                continue

            # 计算列中唯一值的总长度和数量
            try:
                result = execute_sql(db_config, f"""
                    SELECT SUM(LENGTH(unique_values)), COUNT(unique_values)
                    FROM (
                        SELECT DISTINCT `{column}` AS unique_values
                        FROM `{table_name}`
                        WHERE `{column}` IS NOT NULL
                    ) AS subquery
                """, fetch="one", timeout = 480)
            except:
                result = 0, 0  # 查询出错时，设置默认值

            sum_of_lengths, count_distinct = result
            # 如果列没有值，则跳过
            if sum_of_lengths is None or count_distinct == 0:
                continue

            # 计算平均长度
            average_length = sum_of_lengths / count_distinct
            logging.info(f"Column: {column}, sum_of_lengths: {sum_of_lengths}, count_distinct: {count_distinct}, average_length: {average_length}")
            
            # 检查列是否满足我们要处理的条件:
            # 1. 是name列且总长度小于5MB
            # 2. 总长度小于2MB且平均长度小于25
            # 3. 不同值的数量少于100
            if ("name" in column.lower() and sum_of_lengths < 8000000) or (sum_of_lengths < 5000000 and average_length < 30) or count_distinct < 200:
                logging.info(f"Fetching distinct values for {column}")
                try:
                    # 获取列的所有唯一值
                    values = [str(value[0]) for value in execute_sql(db_config, f"SELECT DISTINCT `{column}` FROM `{table_name}` WHERE `{column}` IS NOT NULL", fetch="all", timeout = 480)]
                except:
                    values = []  # 查询出错时，设置为空列表
                logging.info(f"Number of different values: {len(values)}")
                table_values[column] = values
        
        # 将表的值添加到总字典中
        unique_values[table_name] = table_values

    return unique_values

def _get_unique_values_pgsql(pg_client: PgSqlClient, db_name: str) -> Dict[str, Dict[str, List[str]]]:
    """
    从PostgreSQL数据库中检索唯一的文本值，排除主键。
    
    参数:
        pg_client (PgSqlClient): PostgreSQL客户端实例。
        db_name (str): 数据库名称。
    
    返回:
        Dict[str, Dict[str, List[str]]]: 包含每个表和列的唯一值的字典。
    """
    # 初始化存储唯一值的字典
    unique_values: Dict[str, Dict[str, List[str]]] = {}
    
    try:
        # 获取所有表名
        table_query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
        """
        with pg_client.conn.cursor() as cur:
            cur.execute(table_query)
            table_names = [table[0] for table in cur.fetchall()]
        
        # 获取所有主键
        primary_keys = []
        for table_name in table_names:
            pk_query = """
            SELECT kcu.column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
            WHERE tc.constraint_type = 'PRIMARY KEY'
            AND tc.table_name = %s
            """
            with pg_client.conn.cursor() as cur:
                cur.execute(pk_query, (table_name,))
                for row in cur.fetchall():
                    column_name = row[0]
                    if column_name.lower() not in [c.lower() for c in primary_keys]:
                        primary_keys.append(column_name)
        
        # 处理每个表
        for table_name in table_names:
            logging.info(f"Processing {table_name}")
            
            # 获取表中所有文本类型且不是主键的列
            columns_query = """
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_name = %s
            AND table_schema = 'public'
            """
            with pg_client.conn.cursor() as cur:
                cur.execute(columns_query, (table_name,))
                columns_info = cur.fetchall()
            
            text_columns = [col[0] for col in columns_info 
                           if (col[1] in ('text', 'character varying', 'varchar', 'char') and 
                               col[0].lower() not in [c.lower() for c in primary_keys])]
            
            # 初始化当前表的列值字典
            table_values: Dict[str, List[str]] = {}
            
            # 处理每一列
            for column in text_columns:
                # 跳过ID、URL、Email等常见的非文本内容列
                if any(keyword in column.lower() for keyword in ["_id", " id", "url", "email", "web", "time", "phone", "date", "address"]) or column.endswith("Id"):
                    continue

                # 计算列中唯一值的总长度和数量
                try:
                    length_query = f"""
                    SELECT SUM(LENGTH(unique_values)), COUNT(unique_values)
                    FROM (
                        SELECT DISTINCT "{column}" AS unique_values
                        FROM "{table_name}"
                        WHERE "{column}" IS NOT NULL
                    ) AS subquery
                    """
                    with pg_client.conn.cursor() as cur:
                        cur.execute(length_query)
                        result = cur.fetchone()
                except Exception as e:
                    logging.error(f"Error calculating length for {table_name}.{column}: {e}")
                    result = (0, 0)  # 查询出错时，设置默认值

                sum_of_lengths, count_distinct = result if result else (0, 0)
                # 如果列没有值，则跳过
                if sum_of_lengths is None or count_distinct == 0:
                    continue

                # 计算平均长度
                average_length = sum_of_lengths / count_distinct
                logging.info(f"Column: {column}, sum_of_lengths: {sum_of_lengths}, count_distinct: {count_distinct}, average_length: {average_length}")
                
                # 检查列是否满足我们要处理的条件:
                # 1. 是name列且总长度小于8MB
                # 2. 总长度小于5MB且平均长度小于30
                # 3. 不同值的数量少于200
                if ("name" in column.lower() and sum_of_lengths < 8000000) or (sum_of_lengths < 5000000 and average_length < 30) or count_distinct < 200:
                    logging.info(f"Fetching distinct values for {column}")
                    try:
                        # 获取列的所有唯一值
                        values_query = f"""
                        SELECT DISTINCT "{column}"
                        FROM "{table_name}"
                        WHERE "{column}" IS NOT NULL
                        """
                        with pg_client.conn.cursor() as cur:
                            cur.execute(values_query)
                            values = [str(value[0]) for value in cur.fetchall()]
                    except Exception as e:
                        logging.error(f"Error fetching values for {table_name}.{column}: {e}")
                        values = []  # 查询出错时，设置为空列表
                    
                    logging.info(f"Number of different values: {len(values)}")
                    table_values[column] = values
            
            # 将表的值添加到总字典中
            unique_values[table_name] = table_values
    
    except Exception as e:
        logging.error(f"Error processing database {db_name}: {e}")
    
    return unique_values

def create_pg_client_from_config() -> PgSqlClient:
    """
    从配置文件创建PostgreSQL客户端
    
    返回:
        PgSqlClient: PostgreSQL客户端实例
    """
    config = load_config()
    db_config = config.get('database', {})
    
    return PgSqlClient(
        db_name=db_config.get('database', 'adm'),
        host=db_config.get('host', '**************'),
        port=int(db_config.get('port', 30146)),
        user=db_config.get('user', 'pgvector'),
        password=db_config.get('password', 'pgvector')
    )

def _get_unique_values(db_path: str, db_type: str = "postgresql", pg_client: Optional[PgSqlClient] = None) -> Dict[str, Dict[str, List[str]]]:
    """
    从数据库中检索唯一的文本值，排除主键。
    
    参数:
        db_path (str): 数据库路径或连接字符串。
        db_type (str): 数据库类型 ("sqlite" 或 "postgresql")。
        pg_client (Optional[PgSqlClient]): PostgreSQL客户端实例，仅当db_type为"postgresql"时有效。
    
    返回:
        Dict[str, Dict[str, List[str]]]: 包含每个表和列的唯一值的字典。
    """
    if db_type.lower() == "sqlite":
        return _get_unique_values_sqlite(db_path)
    elif db_type.lower() in ["postgresql", "pgsql", "postgres"]:
        if pg_client is None:
            # 如果没有提供pg_client，使用配置文件创建一个
            pg_client = create_pg_client_from_config()
        
        return _get_unique_values_pgsql(pg_client, db_path.split('/')[-1] if '/' in db_path else db_path)
    else:
        raise ValueError(f"不支持的数据库类型: {db_type}")

def _create_minhash(signature_size: int, string: str, n_gram: int) -> MinHash:
    """
    为给定字符串创建MinHash对象。
    
    参数:
        signature_size (int): MinHash签名的大小。
        string (str): 要创建MinHash的输入字符串。
        n_gram (int): 用于MinHash的n-gram大小。
    
    返回:
        MinHash: 输入字符串的MinHash对象。
    """
    # 创建一个具有指定排列数的MinHash对象
    m = MinHash(num_perm=signature_size)
    
    # 将字符串分解为n-gram并更新MinHash
    for d in [string[i:i + n_gram] for i in range(len(string) - n_gram + 1)]:
        m.update(d.encode('utf8'))  # 将每个n-gram添加到MinHash中
    return m

def skip_column(column_name: str, column_values: List[str]) -> bool:
    """
    根据列的值确定是否跳过处理该列。
    
    参数:
        column_name (str): 列的名称。
        column_values (List[str]): 列中的值列表。
    
    返回:
        bool: 如果应该跳过该列，则为True，否则为False。
    """
    # 如果列名包含"name"，则不跳过
    if "name" in column_name.lower():
        return False
    
    # 计算值的总长度和平均长度
    sum_of_lengths = sum(len(value) for value in column_values)
    average_length = sum_of_lengths / len(column_values)
    
    # 如果总长度大于50KB且平均长度大于20，则跳过该列
    return (sum_of_lengths > 50000) and (average_length > 20)

def make_lsh(unique_values: Dict[str, Dict[str, List[str]]], signature_size: int, n_gram: int, threshold: float, verbose: bool = True) -> Tuple[MinHashLSH, Dict[str, Tuple[MinHash, str, str, str]]]:
    """
    从唯一值创建MinHash LSH。
    
    参数:
        unique_values (Dict[str, Dict[str, List[str]]]): 唯一值的字典。
        signature_size (int): MinHash签名的大小。
        n_gram (int): 用于MinHash的n-gram大小。
        threshold (float): MinHash LSH的阈值。
        verbose (bool): 是否显示进度信息。
    
    返回:
        Tuple[MinHashLSH, Dict[str, Tuple[MinHash, str, str, str]]]: MinHash LSH对象和MinHashes字典。
    """
    # 创建LSH索引，threshold是相似度阈值
    lsh = MinHashLSH(threshold=threshold, num_perm=signature_size)
    
    # 存储所有MinHash的字典
    minhashes: Dict[str, Tuple[MinHash, str, str, str]] = {}
    
    try:
        # 计算所有唯一值的总数，用于进度条
        total_unique_values = sum(len(column_values) for table_values in unique_values.values() for column_values in table_values.values())
        logging.info(f"Total unique values: {total_unique_values}")
        
        # 如果verbose为True，则创建进度条
        progress_bar = tqdm(total=total_unique_values, desc="Creating LSH") if verbose else None
        
        # 遍历所有表
        for table_name, table_values in unique_values.items():
            # 遍历表中的所有列
            for column_name, column_values in table_values.items():
                # doctype列特别处理，打印提示信息
                if column_name.lower() == "doctype":
                    print("="*20)
                    print("Doctype found")
                    print("="*20)
                logging.info(f"Processing {table_name} - {column_name} - {len(column_values)}")
                
                # 遍历列中的每个唯一值
                for id, value in enumerate(column_values):
                    # 为每个值创建MinHash
                    minhash = _create_minhash(signature_size, value, n_gram)
                    # 生成唯一键
                    minhash_key = f"{table_name}_{column_name}_{id}"
                    # 存储MinHash和相关信息
                    minhashes[minhash_key] = (minhash, table_name, column_name, value)
                    # 将MinHash添加到LSH索引
                    lsh.insert(minhash_key, minhash)
                    
                    # 更新进度条
                    if verbose:
                        progress_bar.update(1)
        
        # 关闭进度条
        if verbose:
            progress_bar.close()
    except Exception as e:
        logging.error(f"Error creating LSH: {e}")
    
    return lsh, minhashes

def make_db_lsh(db_directory_path: str, db_type: str = "postgresql", **kwargs: Any) -> None:
    """
    为数据库创建MinHash LSH并保存结果。
    
    参数:
        db_directory_path (str): 对于sqlite是数据库目录路径；对于其他数据库是预处理文件存储的目录路径。
        db_type (str): 数据库类型 ("sqlite", "postgresql" 等)。
        **kwargs (Any): LSH创建的附加参数。
                       如果包含db_id，则会将其作为数据库名称拼接到连接字符串中。
    """
    db_type = db_type.lower()
    config = load_config()
    
    # 处理数据库路径和创建目录
    if db_type == "sqlite":
        # sqlite特有的处理：从路径中提取数据库ID和创建目录
        db_id = Path(db_directory_path).name
        preprocessed_path = Path(db_directory_path) / "preprocessed"
        preprocessed_path.mkdir(exist_ok=True)
        db_path = str(Path(db_directory_path) / f"{db_id}.sqlite")
        pg_client = None
    elif db_type in ["postgresql", "pgsql", "postgres"]:
        # 对于postgresql
        if "db_id" in kwargs:
            # 使用db_id构建数据库连接字符串的一部分
            db_id = kwargs["db_id"]
            
            # 从配置文件获取连接信息
            db_config = config.get('database', {})
            db_user = db_config.get('user', 'pgvector')
            db_password = db_config.get('password', 'pgvector')
            db_host = db_config.get('host', '**************')
            db_port = db_config.get('port', 30146)
            
            # 创建PostgreSQL客户端
            pg_client = PgSqlClient(
                db_name=db_id,
                host=db_host,
                port=int(db_port),
                user=db_user,
                password=db_password
            )
            
            # 构建数据库连接字符串用于存储
            db_path = f"{db_user}:{quote_plus(db_password)}@{db_host}:{db_port}/{db_id}"
        else:
            # 使用完整连接字符串作为db_path
            db_path = db_directory_path
            db_id = db_directory_path.split('/')[-1]
            pg_client = None  # 在_get_unique_values中会根据连接字符串创建pg_client
        
        # 在指定目录下创建preprocessed目录
        preprocessed_path = Path(db_directory_path) / "preprocessed"
        preprocessed_path.mkdir(parents=True, exist_ok=True)
    else:
        # 对于mysql等其他数据库
        if "db_id" in kwargs:
            # 使用db_id构建数据库连接字符串
            db_id = kwargs["db_id"]
            db_config = config.get('database', {})
            db_path = f"{db_config.get('user')}:{quote_plus(db_config.get('password'))}@{db_config.get('host')}:{db_config.get('port')}/{db_id}"
        else:
            # 使用完整连接字符串作为db_path
            db_path = db_directory_path
            db_id = db_directory_path.split('/')[-1]
        
        # 在指定目录下创建preprocessed目录
        preprocessed_path = Path(db_directory_path) / "preprocessed" / db_id
        preprocessed_path.mkdir(parents=True, exist_ok=True)
        pg_client = None
    
    # 获取数据库中的唯一值
    unique_values = _get_unique_values(db_path, db_type, pg_client)
    logging.info("Unique values obtained")
    
    # 将唯一值保存到pickle文件
    with open(preprocessed_path / f"{db_id}_unique_values.pkl", "wb") as file:
        pickle.dump(unique_values, file)
    logging.info("Saved unique values")
    
    # 创建LSH索引和MinHash字典
    # 从kwargs中提取LSH参数
    lsh_params = {
        "signature_size": kwargs.get("signature_size", 128),
        "n_gram": kwargs.get("n_gram", 3),
        "threshold": kwargs.get("threshold", 0.5),
        "verbose": kwargs.get("verbose", True)
    }
    lsh, minhashes = make_lsh(unique_values, **lsh_params)
    
    # 将LSH和MinHash保存到pickle文件
    with open(preprocessed_path / f"{db_id}_lsh.pkl", "wb") as file:
        pickle.dump(lsh, file)
    with open(preprocessed_path / f"{db_id}_minhashes.pkl", "wb") as file:
        pickle.dump(minhashes, file)
    
    # 关闭PostgreSQL连接（如果适用）
    if pg_client is not None:
        pg_client.disconnect()
