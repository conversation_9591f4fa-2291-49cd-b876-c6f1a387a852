import os
import random
import logging
import asyncio
import threading
from enum import Enum
from queue import Empty
from functools import wraps
from typing import Any, Union, List, Dict, Optional, Tuple, Callable
from multiprocessing import Process, Queue

from sqlalchemy import create_engine, text, MetaData, Table, select, inspect
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, AsyncConnection
from sqlalchemy.engine import Engine, Connection
from sqlalchemy.exc import SQLAlchemyError
import sqlglot
from sqlglot import parse_one, exp

class TimeoutException(Exception):
    """在执行超时时抛出的异常"""
    pass

class ExecutionStatus(Enum):
    """SQL执行状态枚举"""
    SYNTACTICALLY_CORRECT = "SYNTACTICALLY_CORRECT"
    EMPTY_RESULT = "EMPTY_RESULT"
    NONE_RESULT = "NONE_RESULT"
    ZERO_COUNT_RESULT = "ZERO_COUNT_RESULT"
    ALL_NONE_RESULT = "ALL_NONE_RESULT"
    SYNTACTICALLY_INCORRECT = "SYNTACTICALLY_INCORRECT"

class SQLDatabaseConfig:
    """数据库配置类"""
    def __init__(self, url: str, is_async: bool = True, connect_args: Dict = None):
        """
        初始化数据库配置
        
        Args:
            url (str): 数据库连接URL
            is_async (bool): 是否使用异步引擎
            connect_args (Dict): 连接参数
        """
        self.url = url
        self.is_async = is_async
        self.connect_args = connect_args or {}
        
    def create_engine(self) -> Union[Engine, AsyncEngine]:
        """创建数据库引擎"""
        if self.is_async:
            return create_async_engine(self.url, **self.connect_args)
        else:
            return create_engine(self.url, **self.connect_args)

def create_database_config(db_path: str, db_type: str = "mysql", is_async: bool = True) -> SQLDatabaseConfig:
    """
    创建数据库配置
    
    Args:
        db_path (str): 数据库路径或名称
        db_type (str): 数据库类型 (sqlite, mysql, postgresql等)
        is_async (bool): 是否使用异步引擎
        
    Returns:
        SQLDatabaseConfig: 数据库配置对象
    """
    connect_args = {}
    if db_type == "sqlite":
        if is_async:
            url = f"sqlite+aiosqlite:///{db_path}"
        else:
            url = f"sqlite:///{db_path}"
        connect_args = {"timeout": 60}
    elif db_type == "mysql":
        if is_async:
            url = f"mysql+aiomysql://{db_path}"
        else:
            url = f"mysql+pymysql://{db_path}"
    elif db_type == "postgresql":
        if is_async:
            url = f"postgresql+asyncpg://{db_path}"
        else:
            url = f"postgresql+psycopg2://{db_path}"
    else:
        raise ValueError(f"不支持的数据库类型: {db_type}")
    
    return SQLDatabaseConfig(url, is_async, connect_args)

def _clean_sql(sql: str) -> str:
    """
    清理SQL查询，移除不需要的字符和空格
    
    Args:
        sql (str): SQL查询字符串
        
    Returns:
        str: 清理后的SQL查询字符串
    """
    return sql.replace('\n', ' ').replace('"', "'").strip("`.")

async def execute_sql_async(db_config: SQLDatabaseConfig, sql: str, fetch: Union[str, int] = "all", 
                      timeout: int = 60) -> Any:
    """
    异步执行SQL查询并获取结果
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置
        sql (str): 要执行的SQL查询
        fetch (Union[str, int]): 获取结果的方式 ("all", "one", "random"或整数)
        timeout (int): 执行超时时间(秒)
        
    Returns:
        Any: 根据fetch参数获取的结果
        
    Raises:
        TimeoutException: 如果执行超时
        Exception: 如果执行出错
    """
    if not db_config.is_async:
        raise ValueError("此函数仅支持异步数据库配置")
    
    sql = _clean_sql(sql)
    
    try:
        async def _execute():
            engine = db_config.create_engine()
            async with engine.connect() as conn:
                result = await conn.execute(text(sql))
                
                if fetch == "all":
                    rows = result.fetchall()
                elif fetch == "one":
                    rows = result.fetchone()
                elif fetch == "random":
                    sample_rows = result.fetchmany(10)
                    rows = random.choice(sample_rows) if sample_rows else []
                elif isinstance(fetch, int):
                    rows = result.fetchmany(fetch)
                else:
                    raise ValueError("无效的fetch参数，必须是'all', 'one', 'random'或整数")
                
                return rows
        
        # 设置超时
        return await asyncio.wait_for(_execute(), timeout)
    except asyncio.TimeoutError:
        raise TimeoutException(f"SQL查询执行超过了{timeout}秒的超时时间")
    except Exception as e:
        logging.error(f"执行SQL时出错: {e}\nSQL: {sql}")
        raise e

def execute_sql_sync(db_config: SQLDatabaseConfig, sql: str, fetch: Union[str, int] = "all", 
                    timeout: int = 60) -> Any:
    """
    同步执行SQL查询并获取结果
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置
        sql (str): 要执行的SQL查询
        fetch (Union[str, int]): 获取结果的方式 ("all", "one", "random"或整数)
        timeout (int): 执行超时时间(秒)
        
    Returns:
        Any: 根据fetch参数获取的结果
        
    Raises:
        TimeoutException: 如果执行超时
        Exception: 如果执行出错
    """
    if db_config.is_async:
        raise ValueError("此函数仅支持同步数据库配置")
    
    sql = _clean_sql(sql)
    
    class QueryThread(threading.Thread):
        def __init__(self):
            threading.Thread.__init__(self)
            self.result = None
            self.exception = None

        def run(self):
            try:
                engine = db_config.create_engine()
                with engine.connect() as conn:
                    result = conn.execute(text(sql))
                    
                    if fetch == "all":
                        self.result = result.fetchall()
                    elif fetch == "one":
                        self.result = result.fetchone()
                    elif fetch == "random":
                        samples = result.fetchmany(10)
                        self.result = random.choice(samples) if samples else []
                    elif isinstance(fetch, int):
                        self.result = result.fetchmany(fetch)
                    else:
                        raise ValueError("无效的fetch参数，必须是'all', 'one', 'random'或整数")
            except Exception as e:
                self.exception = e
    
    query_thread = QueryThread()
    query_thread.start()
    query_thread.join(timeout)
    
    if query_thread.is_alive():
        raise TimeoutException(f"SQL查询执行超过了{timeout}秒的超时时间")
    if query_thread.exception:
        raise query_thread.exception
    
    return query_thread.result

def execute_sql(db_config: Union[SQLDatabaseConfig, str], sql: str, fetch: Union[str, int] = "all", 
               timeout: int = 60, db_type: str = "mysql") -> Any:
    """
    执行SQL查询的统一接口
    
    Args:
        db_config (Union[SQLDatabaseConfig, str]): 数据库配置或路径
        sql (str): 要执行的SQL查询
        fetch (Union[str, int]): 获取结果的方式
        timeout (int): 超时时间(秒)
        db_type (str): 如果db_config是字符串，则需要指定数据库类型
        
    Returns:
        Any: 查询结果
    """
    if isinstance(db_config, str):
        db_config = create_database_config(db_config, db_type, False)  # 默认使用同步版本
    
    if db_config.is_async:
        return asyncio.run(execute_sql_async(db_config, sql, fetch, timeout))
    else:
        return execute_sql_sync(db_config, sql, fetch, timeout)

async def create_smaller_db_async(db_config: SQLDatabaseConfig, max_rows: int = 100000) -> str:
    """
    异步创建较小的数据库副本
    
    Args:
        db_config (SQLDatabaseConfig): 原数据库配置
        max_rows (int): 每个表的最大行数
        
    Returns:
        str: 新数据库路径
    """
    # 提取原数据库路径
    if 'sqlite' in db_config.url:
        # 对于sqlite，提取文件路径
        original_db_path = db_config.url.split('///')[1]
        base, ext = os.path.splitext(original_db_path)
        new_db_path = f"{base}_small{ext}"
        new_db_url = db_config.url.replace(original_db_path, new_db_path)
    else:
        # 对于其他数据库，创建一个带_small后缀的新数据库
        parts = db_config.url.split('/')
        original_db_name = parts[-1]
        new_db_name = f"{original_db_name}_small"
        new_db_url = db_config.url.replace(original_db_name, new_db_name)
    
    # 创建新数据库配置
    new_db_config = SQLDatabaseConfig(new_db_url, db_config.is_async, db_config.connect_args)
    
    # 获取原数据库的引擎和连接
    orig_engine = db_config.create_engine()
    new_engine = new_db_config.create_engine()
    
    async with orig_engine.connect() as orig_conn:
        # 获取所有表
        inspector = inspect(orig_engine)
        table_names = await orig_conn.run_sync(lambda sync_conn: inspector.get_table_names())
        
        async with new_engine.connect() as new_conn:
            # 为每个表创建模式并复制数据
            for table_name in table_names:
                if table_name == "sqlite_sequence":
                    continue
                
                # 获取表的定义
                table_metadata = MetaData()
                table = await orig_conn.run_sync(
                    lambda sync_conn: Table(table_name, table_metadata, autoload_with=sync_conn)
                )
                
                # 在新数据库中创建表
                await new_conn.run_sync(lambda sync_conn: table_metadata.create_all(new_engine))
                
                # 从原表获取随机行
                stmt = select(table).order_by(text("RANDOM()")).limit(max_rows)
                rows = (await orig_conn.execute(stmt)).fetchall()
                
                # 将行插入新表
                if rows:
                    for row in rows:
                        await new_conn.execute(table.insert().values(row))
                    
                    await new_conn.commit()
    
    return new_db_url

def create_smaller_db(db_config: Union[SQLDatabaseConfig, str], max_rows: int = 100000, 
                     db_type: str = "mysql") -> str:
    """
    创建较小的数据库副本的统一接口
    
    Args:
        db_config (Union[SQLDatabaseConfig, str]): 数据库配置或路径
        max_rows (int): 每个表的最大行数
        db_type (str): 如果db_config是字符串，则需要指定数据库类型
        
    Returns:
        str: 新数据库路径或URL
    """
    if isinstance(db_config, str):
        db_config = create_database_config(db_config, db_type, False)
    
    if db_config.is_async:
        return asyncio.run(create_smaller_db_async(db_config, max_rows))
    else:
        # 实现同步版本的create_smaller_db
        # 此处略，逻辑与异步版本类似
        raise NotImplementedError("尚未实现同步版本的create_smaller_db")

async def subprocess_sql_executor_async(db_config: SQLDatabaseConfig, sql: str, timeout: int = 60) -> Any:
    """
    在子进程中异步执行SQL查询
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置
        sql (str): 要执行的SQL查询
        timeout (int): 超时时间(秒)
        
    Returns:
        Any: 查询结果
        
    Raises:
        TimeoutException: 如果执行超时
        Exception: 如果执行出错
    """
    def task(queue, db_config_dict, sql):
        try:
            # 重建数据库配置
            db_config = SQLDatabaseConfig(
                db_config_dict['url'], 
                db_config_dict['is_async'],
                db_config_dict['connect_args']
            )
            # 使用同步版本执行查询
            result = execute_sql_sync(db_config, sql, "all", timeout)
            queue.put(result)
        except Exception as e:
            queue.put(e)
    
    # 转换数据库配置为字典
    db_config_dict = {
        'url': db_config.url,
        'is_async': False,  # 在子进程中使用同步版本
        'connect_args': db_config.connect_args
    }
    
    queue = Queue()
    process = Process(target=task, args=(queue, db_config_dict, sql))
    process.start()
    
    # 使用异步等待子进程完成
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, process.join, timeout)
    
    if process.is_alive():
        process.terminate()
        await loop.run_in_executor(None, process.join)
        logging.error("子进程SQL执行器超时")
        raise TimeoutException("执行超时")
    else:
        try:
            result = queue.get_nowait()
        except Empty:
            raise Exception("进程未返回数据")
        
        if isinstance(result, Exception):
            raise result
        return result

def subprocess_sql_executor(db_config: Union[SQLDatabaseConfig, str], sql: str, timeout: int = 60, 
                           db_type: str = "mysql") -> Any:
    """
    在子进程中执行SQL查询的统一接口
    
    Args:
        db_config (Union[SQLDatabaseConfig, str]): 数据库配置或路径
        sql (str): 要执行的SQL查询
        timeout (int): 超时时间(秒)
        db_type (str): 如果db_config是字符串，则需要指定数据库类型
        
    Returns:
        Any: 查询结果
    """
    if isinstance(db_config, str):
        db_config = create_database_config(db_config, db_type, False)
    
    if db_config.is_async:
        return asyncio.run(subprocess_sql_executor_async(db_config, sql, timeout))
    else:
        # 同步版本的实现（类似于原始代码）
        def task(queue, db_config, sql):
            try:
                result = execute_sql_sync(db_config, sql, "all", timeout)
                queue.put(result)
            except Exception as e:
                queue.put(e)
        
        queue = Queue()
        process = Process(target=task, args=(queue, db_config, sql))
        process.start()
        process.join(timeout)
        
        if process.is_alive():
            process.terminate()
            process.join()
            logging.error("子进程SQL执行器超时")
            raise TimeoutException("执行超时")
        else:
            try:
                result = queue.get_nowait()
            except Empty:
                raise Exception("进程未返回数据")
            
            if isinstance(result, Exception):
                raise result
            return result

async def compare_sqls_async(db_config: SQLDatabaseConfig, predicted_sql: str, ground_truth_sql: str, 
                           meta_timeout: int = 30) -> Dict[str, Union[int, str]]:
    """
    异步比较两个SQL查询的结果
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置
        predicted_sql (str): 预测的SQL查询
        ground_truth_sql (str): 真实的SQL查询
        meta_timeout (int): 比较的超时时间(秒)
        
    Returns:
        Dict[str, Union[int, str]]: 包含比较结果和错误信息的字典
    """
    predicted_sql = _clean_sql(predicted_sql)
    
    async def _compare_sqls_outcomes() -> int:
        try:
            predicted_res = await execute_sql_async(db_config, predicted_sql, "all")
            ground_truth_res = await execute_sql_async(db_config, ground_truth_sql, "all")
            return int(set(map(tuple, predicted_res)) == set(map(tuple, ground_truth_res)))
        except Exception as e:
            logging.critical(f"比较SQL结果出错: {e}")
            raise e
    
    try:
        # 设置超时
        res = await asyncio.wait_for(_compare_sqls_outcomes(), meta_timeout)
        error = "incorrect answer" if res == 0 else "--"
    except asyncio.TimeoutError:
        logging.warning("比较超时")
        error = "timeout"
        res = 0
    except Exception as e:
        logging.error(f"比较SQL出错: {e}")
        error = str(e)
        res = 0
    
    return {'exec_res': res, 'exec_err': error}

def compare_sqls(db_config: Union[SQLDatabaseConfig, str], predicted_sql: str, ground_truth_sql: str, 
                meta_timeout: int = 30, db_type: str = "mysql") -> Dict[str, Union[int, str]]:
    """
    比较两个SQL查询结果的统一接口
    
    Args:
        db_config (Union[SQLDatabaseConfig, str]): 数据库配置或路径
        predicted_sql (str): 预测的SQL查询
        ground_truth_sql (str): 真实的SQL查询
        meta_timeout (int): 比较的超时时间(秒)
        db_type (str): 如果db_config是字符串，则需要指定数据库类型
        
    Returns:
        Dict[str, Union[int, str]]: 包含比较结果和错误信息的字典
    """
    if isinstance(db_config, str):
        db_config = create_database_config(db_config, db_type, False)
    
    if db_config.is_async:
        return asyncio.run(compare_sqls_async(db_config, predicted_sql, ground_truth_sql, meta_timeout))
    else:
        # 同步版本的实现
        predicted_sql = _clean_sql(predicted_sql)
        try:
            def _compare_sqls_outcomes():
                try:
                    predicted_res = execute_sql_sync(db_config, predicted_sql, "all")
                    ground_truth_res = execute_sql_sync(db_config, ground_truth_sql, "all")
                    return int(set(map(tuple, predicted_res)) == set(map(tuple, ground_truth_res)))
                except Exception as e:
                    logging.critical(f"比较SQL结果出错: {e}")
                    raise e
            
            # 使用函数超时机制
            class TimeoutResult:
                def __init__(self):
                    self.result = None
                    self.exception = None
            
            timeout_result = TimeoutResult()
            
            def target():
                try:
                    timeout_result.result = _compare_sqls_outcomes()
                except Exception as e:
                    timeout_result.exception = e
            
            thread = threading.Thread(target=target)
            thread.start()
            thread.join(meta_timeout)
            
            if thread.is_alive():
                error = "timeout"
                res = 0
            elif timeout_result.exception:
                error = str(timeout_result.exception)
                res = 0
            else:
                res = timeout_result.result
                error = "incorrect answer" if res == 0 else "--"
            
            return {'exec_res': res, 'exec_err': error}
        except Exception as e:
            logging.error(f"比较SQL出错: {e}")
            return {'exec_res': 0, 'exec_err': str(e)}

async def validate_sql_query_async(db_config: SQLDatabaseConfig, sql: str, 
                                max_returned_rows: int = 30) -> Dict[str, Union[str, Any]]:
    """
    异步验证SQL查询并返回结果
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置
        sql (str): 要验证的SQL查询
        max_returned_rows (int): 最大返回行数
        
    Returns:
        Dict[str, Union[str, Any]]: 包含SQL查询、结果和状态的字典
    """
    try:
        result = await execute_sql_async(db_config, sql, fetch=max_returned_rows)
        return {"SQL": sql, "RESULT": result, "STATUS": "OK"}
    except Exception as e:
        logging.error(f"验证SQL查询出错: {e}")
        return {"SQL": sql, "RESULT": str(e), "STATUS": "ERROR"}

def validate_sql_query(db_config: Union[SQLDatabaseConfig, str], sql: str, max_returned_rows: int = 30, 
                     db_type: str = "mysql") -> Dict[str, Union[str, Any]]:
    """
    验证SQL查询的统一接口
    
    Args:
        db_config (Union[SQLDatabaseConfig, str]): 数据库配置或路径
        sql (str): 要验证的SQL查询
        max_returned_rows (int): 最大返回行数
        db_type (str): 如果db_config是字符串，则需要指定数据库类型
        
    Returns:
        Dict[str, Union[str, Any]]: 包含SQL查询、结果和状态的字典
    """
    if isinstance(db_config, str):
        db_config = create_database_config(db_config, db_type, False)
    
    if db_config.is_async:
        return asyncio.run(validate_sql_query_async(db_config, sql, max_returned_rows))
    else:
        # 同步版本的实现
        try:
            result = execute_sql_sync(db_config, sql, fetch=max_returned_rows)
            return {"SQL": sql, "RESULT": result, "STATUS": "OK"}
        except Exception as e:
            logging.error(f"验证SQL查询出错: {e}")
            return {"SQL": sql, "RESULT": str(e), "STATUS": "ERROR"}

async def aggregate_sqls_async(db_config: SQLDatabaseConfig, sqls: List[str]) -> str:
    """
    异步聚合多个SQL查询
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置
        sqls (List[str]): SQL查询列表
        
    Returns:
        str: 从最大簇中选择的最短SQL查询
    """
    results = []
    for sql in sqls:
        results.append(await validate_sql_query_async(db_config, sql))
    
    clusters = {}
    
    # 根据唯一结果集分组查询
    for result in results:
        if result['STATUS'] == 'OK':
            # 使用frozenset作为键来处理不可哈希类型如列表
            key = frozenset(tuple(row) for row in result['RESULT'])
            if key in clusters:
                clusters[key].append(result['SQL'])
            else:
                clusters[key] = [result['SQL']]
    
    if clusters:
        # 找到最大的簇
        largest_cluster = max(clusters.values(), key=len, default=[])
        # 从最大簇中选择最短的SQL查询
        if largest_cluster:
            return min(largest_cluster, key=len)
    
    logging.warning("未找到有效的SQL簇，返回第一个SQL查询")
    return sqls[0]

def aggregate_sqls(db_config: Union[SQLDatabaseConfig, str], sqls: List[str], 
                  db_type: str = "mysql") -> str:
    """
    聚合多个SQL查询的统一接口
    
    Args:
        db_config (Union[SQLDatabaseConfig, str]): 数据库配置或路径
        sqls (List[str]): SQL查询列表
        db_type (str): 如果db_config是字符串，则需要指定数据库类型
        
    Returns:
        str: 聚合后的SQL查询
    """
    if isinstance(db_config, str):
        db_config = create_database_config(db_config, db_type, False)
    
    if db_config.is_async:
        return asyncio.run(aggregate_sqls_async(db_config, sqls))
    else:
        # 同步版本的实现
        results = [validate_sql_query(db_config, sql) for sql in sqls]
        clusters = {}
        
        # 根据唯一结果集分组查询
        for result in results:
            if result['STATUS'] == 'OK':
                # 使用frozenset作为键来处理不可哈希类型如列表
                key = frozenset(tuple(row) for row in result['RESULT'])
                if key in clusters:
                    clusters[key].append(result['SQL'])
                else:
                    clusters[key] = [result['SQL']]
        
        if clusters:
            # 找到最大的簇
            largest_cluster = max(clusters.values(), key=len, default=[])
            # 从最大簇中选择最短的SQL查询
            if largest_cluster:
                return min(largest_cluster, key=len)
        
        logging.warning("未找到有效的SQL簇，返回第一个SQL查询")
        return sqls[0]

async def get_execution_status_async(db_config: SQLDatabaseConfig, sql: str, 
                                  execution_result: List = None) -> ExecutionStatus:
    """
    异步获取SQL执行状态
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置
        sql (str): 要执行的SQL查询
        execution_result (List): 执行结果，如果为None则执行SQL
        
    Returns:
        ExecutionStatus: 执行状态
    """
    if not execution_result:
        try:
            execution_result = await execute_sql_async(db_config, sql, fetch="all")
        except asyncio.TimeoutError:
            logging.error("在get_execution_status中超时")
            return ExecutionStatus.SYNTACTICALLY_INCORRECT
        except Exception:
            return ExecutionStatus.SYNTACTICALLY_INCORRECT
    
    if (execution_result is None) or (execution_result == []):
        return ExecutionStatus.EMPTY_RESULT
    
    # 这里可以添加更详细的检查，类似于原始代码中的注释部分
    
    return ExecutionStatus.SYNTACTICALLY_CORRECT

def get_execution_status(db_config: Union[SQLDatabaseConfig, str], sql: str, 
                       execution_result: List = None, db_type: str = "mysql") -> ExecutionStatus:
    """
    获取SQL执行状态的统一接口
    
    Args:
        db_config (Union[SQLDatabaseConfig, str]): 数据库配置或路径
        sql (str): 要执行的SQL查询
        execution_result (List): 执行结果，如果为None则执行SQL
        db_type (str): 如果db_config是字符串，则需要指定数据库类型
        
    Returns:
        ExecutionStatus: 执行状态
    """
    if isinstance(db_config, str):
        db_config = create_database_config(db_config, db_type, False)
    
    if db_config.is_async:
        return asyncio.run(get_execution_status_async(db_config, sql, execution_result))
    else:
        # 同步版本的实现
        if not execution_result:
            try:
                execution_result = execute_sql_sync(db_config, sql, fetch="all")
            except TimeoutException:
                logging.error("在get_execution_status中超时")
                return ExecutionStatus.SYNTACTICALLY_INCORRECT
            except Exception:
                return ExecutionStatus.SYNTACTICALLY_INCORRECT
        
        if (execution_result is None) or (execution_result == []):
            return ExecutionStatus.EMPTY_RESULT
        
        # 这里可以添加更详细的检查，类似于原始代码中的注释部分
        
        return ExecutionStatus.SYNTACTICALLY_CORRECT

async def run_with_timeout_async(func: Callable, *args, timeouts: List[int] = [3, 5]) -> Any:
    """
    在超时内异步运行函数
    
    Args:
        func (Callable): 要运行的函数
        *args: 函数参数
        timeouts (List[int]): 尝试的超时时间列表
        
    Returns:
        Any: 函数结果
        
    Raises:
        TimeoutException: 如果所有尝试都超时
    """
    for attempt, timeout in enumerate(timeouts):
        try:
            # 如果func是协程函数或已经是协程对象
            if asyncio.iscoroutinefunction(func):
                return await asyncio.wait_for(func(*args), timeout)
            else:
                # 如果是普通函数，在线程池中运行
                loop = asyncio.get_event_loop()
                return await asyncio.wait_for(
                    loop.run_in_executor(None, func, *args),
                    timeout
                )
        except asyncio.TimeoutError:
            logging.error(f"函数 {func.__name__} 在尝试 {attempt + 1}/{len(timeouts)} 中超过了 {timeout} 秒超时")
            if attempt == len(timeouts) - 1:
                raise TimeoutException(
                    f"函数 {func.__name__} 在尝试 {attempt + 1}/{len(timeouts)} 中超过了 {timeout} 秒超时"
                )
    
    raise TimeoutException(f"函数 {func.__name__} 在 {len(timeouts)} 次尝试后失败")

def run_with_timeout(func: Callable, *args, timeouts: List[int] = [3, 5], is_async: bool = False) -> Any:
    """
    在超时内运行函数的统一接口
    
    Args:
        func (Callable): 要运行的函数
        *args: 函数参数
        timeouts (List[int]): 尝试的超时时间列表
        is_async (bool): 函数是否是异步的
        
    Returns:
        Any: 函数结果
    """
    if is_async or asyncio.iscoroutinefunction(func):
        return asyncio.run(run_with_timeout_async(func, *args, timeouts=timeouts))
    else:
        # 同步版本的实现
        for attempt, timeout in enumerate(timeouts):
            result = [None, None]
            stop_event = threading.Event()
            
            def wrapper(stop_event, *args):
                try:
                    if not stop_event.is_set():
                        result[0] = func(*args)
                except Exception as e:
                    result[1] = e
            
            thread = threading.Thread(target=wrapper, args=(stop_event, *args))
            thread.start()
            
            # 等待线程完成或超时
            thread.join(timeout)
            
            if thread.is_alive():
                logging.error(f"函数 {func.__name__} 在尝试 {attempt + 1}/{len(timeouts)} 中超过了 {timeout} 秒超时")
                stop_event.set()  # 发送停止信号
                thread.join()  # 等待线程识别停止事件
                if attempt == len(timeouts) - 1:
                    raise TimeoutException(
                        f"函数 {func.__name__} 在尝试 {attempt + 1}/{len(timeouts)} 中超过了 {timeout} 秒超时"
                    )
            else:
                if result[1] is not None:
                    raise result[1]
                return result[0]
        
        raise TimeoutException(f"函数 {func.__name__} 在 {len(timeouts)} 次尝试后失败") 