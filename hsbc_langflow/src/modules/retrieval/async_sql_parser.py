import logging
import sqlvalidator
import asyncio
from typing import Dict, List, Optional, Union
from func_timeout import func_timeout, FunctionTimedOut

from sqlglot import parse_one, exp
from sqlglot.optimizer.qualify import qualify
if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database_utils.retrieval.async_sqlalchemy_execution import (
    SQLDatabaseConfig, create_database_config, execute_sql
)
from database_utils.retrieval.async_db_info import get_table_all_columns, get_db_all_tables
import logging

logger = logging.getLogger(__name__)
# logger.setLevel(logging.INFO)
# # log 输出到文件
# handler = logging.FileHandler('/data/ideal/code/gsh_code/text2SQL/xiyan/logs/sql_parser.log')
# handler.setLevel(logging.INFO)
# formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# handler.setFormatter(formatter)
# logger.addHandler(handler)

def format_sql_query(query, meta_time_out = 10):
    try:
        return func_timeout(meta_time_out, sqlvalidator.format_sql, args=(query))
    except FunctionTimedOut:
        print(f"Timeout in format_sql_query: {query}")
        return query
    except Exception:
        return query

async def get_sql_tables(db_path: str, sql: str, db_type: str = "mysql") -> List[str]:
    """
    异步检索SQL查询中涉及的表名。
    
    Args:
        db_path (str): 数据库连接字符串或文件路径。
        sql (str): SQL查询字符串。
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        List[str]: SQL查询中涉及的表名列表（已去重）。
    """
    # 获取数据库中实际存在的表
    db_tables = await get_db_all_tables(db_path, db_type)
    
    try:
        # 根据数据库类型选择解析方言
        dialect = db_type.lower()
        if dialect not in ['mysql', 'postgresql', 'bigquery', 'snowflake', 'spark', 'duckdb']:
            dialect = 'sqlite'  # 默认回退到sqlite解析器
            
        # 解析SQL语句
        parsed_sql = parse_one(sql, read=dialect)
        parsed_tables = list(parsed_sql.find_all(exp.Table))
        
        # 提取并清理表名
        clean_tables = set()  # 使用set来自动去重
        for table in parsed_tables:
            # 处理可能的模式限定表名 (schema.table)
            table_parts = str(table.name).split('.')
            table_name = table_parts[-1].strip().replace('\"', '').replace('`', '').replace('[', '').replace(']', '')
            
            # 对于MySQL，考虑可能的数据库.表名格式
            if dialect == 'mysql' and len(table_parts) > 1:
                # 检查是否是database.table格式
                if table_parts[0].lower() not in ['information_schema', 'mysql', 'performance_schema', 'sys']:
                    # 将数据库名与表名组合为完整表识别符
                    full_name = f"{table_parts[0]}.{table_name}"
                    if full_name.lower() in [db_table.lower() for db_table in db_tables]:
                        clean_tables.add(full_name)
                        continue
                    
            # 标准单表名匹配
            if table_name.lower() in [db_table.lower() for db_table in db_tables]:
                clean_tables.add(table_name)
                
        return list(clean_tables)  # 转换回列表返回
    except Exception as e:
        logging.critical(f"Error in get_sql_tables: {e}\nSQL: {sql}\nDB type: {db_type}")
        raise e

def _get_main_parent(expression: exp.Expression) -> Optional[exp.Expression]:
    """
    检索给定SQL表达式的主要父表达式。
    
    Args:
        expression (exp.Expression): SQL表达式。
        
    Returns:
        Optional[exp.Expression]: 主要父表达式，如果未找到则为None。
    """
    parent = expression.parent
    while parent and not isinstance(parent, exp.Subquery):
        parent = parent.parent
    return parent

def _get_table_with_alias(parsed_sql: exp.Expression, alias: str) -> Optional[exp.Table]:
    """
    检索与给定别名关联的表。
    
    Args:
        parsed_sql (exp.Expression): 解析的SQL表达式。
        alias (str): 表别名。
        
    Returns:
        Optional[exp.Table]: 与别名关联的表，如果未找到则为None。
    """
    return next((table for table in parsed_sql.find_all(exp.Table) if table.alias == alias), None)

async def _process_subquery_columns(db_path: str, subquery: exp.Expression, parent_context: Dict = None, db_type: str = "mysql") -> Dict[str, List[str]]:
    """
    处理子查询中的列引用，特别是处理相关子查询。
    
    Args:
        db_path: 数据库路径
        subquery: 子查询表达式
        parent_context: 父查询的上下文（表和列的映射）
        db_type: 数据库类型
        
    Returns:
        包含子查询中表和列的字典
    """
    sub_columns_dict = {}
    
    # 检查子查询中的表引用
    sub_tables = [t for t in subquery.find_all(exp.Table)]
    
    # 获取子查询中的列引用
    for column in subquery.find_all(exp.Column):
        column_name = column.name
        table_alias = column.table
        
        # 在处理表别名之前添加日志
        logging.info(f"子查询处理 - 当前子查询: {subquery.sql()[:100]}...")
        logging.info(f"子查询处理 - 父上下文: {parent_context}")
        
        # 如果列引用有表别名
        if table_alias:
            logging.info(f"子查询处理 - 发现表别名引用: {table_alias}.{column_name}")
            # 在子查询中查找表
            sub_table = _get_table_with_alias(subquery, table_alias)
            if sub_table:
                table_name = sub_table.name
                if table_name not in sub_columns_dict:
                    sub_columns_dict[table_name] = []
                if column_name not in sub_columns_dict[table_name]:
                    sub_columns_dict[table_name].append(column_name)
            # 检查是否是对外部查询的引用（相关子查询）
            elif parent_context:
                logging.info(f"子查询处理 - 检查是否为外部查询引用: {table_alias}")
                for parent_table, parent_aliases in parent_context.items():
                    if table_alias in parent_aliases:
                        logging.info(f"子查询处理 - 找到外部查询引用: {table_alias} 匹配 {parent_table} 的别名")
                        if parent_table not in sub_columns_dict:
                            sub_columns_dict[parent_table] = []
                        if column_name not in sub_columns_dict[parent_table]:
                            sub_columns_dict[parent_table].append(column_name)
        # 没有表别名的列，尝试确定它所属的表
        else:
            for sub_table in sub_tables:
                try:
                    table_columns = await get_table_all_columns(db_path, sub_table.name, db_type)
                    if column_name.lower() in [col.lower() for col in table_columns]:
                        if sub_table.name not in sub_columns_dict:
                            sub_columns_dict[sub_table.name] = []
                        if column_name not in sub_columns_dict[sub_table.name]:
                            sub_columns_dict[sub_table.name].append(column_name)
                except Exception as e:
                    logging.debug(f"获取子查询表列时出错: {e}")
    
    return sub_columns_dict

async def get_sql_columns_dict(db_path: str, sql: str, db_type: str = "mysql") -> Dict[str, List[str]]:
    """
    异步检索SQL查询中涉及的表及其各自列的字典。
    
    Args:
        db_path (str): 数据库连接字符串。
        sql (str): SQL查询字符串。
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        Dict[str, List[str]]: 表及其列的字典。
    """
    # 选择适当的SQL方言进行解析
    dialect = db_type.lower()
    if dialect not in ['mysql', 'postgresql', 'bigquery', 'snowflake', 'spark', 'duckdb']:
        dialect = 'sqlite'  # 默认回退
    
    # 解析并限定列名
    sql_expr = parse_one(sql, read=dialect) if isinstance(sql, str) else sql
    # MySQL方言中正确处理列限定
    try:
        sql_expr = qualify(sql_expr, qualify_columns=True, validate_qualify_columns=False)
    except Exception as e:
        logging.warning(f"列限定失败，使用原始SQL: {e}")
    
    columns_dict = {}
    # 添加: 跟踪表别名
    table_aliases = {}

    # 获取所有数据库表，用于验证和解析
    all_db_tables = await get_db_all_tables(db_path, db_type)
    
    # 添加：收集表及其别名
    for table in sql_expr.find_all(exp.Table):
        table_name = str(table.name).replace('`', '').replace('"', '')
        if '.' in table_name:
            table_name = table_name.split('.')[-1]
            
        if table_name not in table_aliases:
            table_aliases[table_name] = []
            
        if table.alias:
            alias = str(table.alias)
            if alias not in table_aliases[table_name]:
                table_aliases[table_name].append(alias)

    # 处理子查询 - MySQL子查询可能更复杂
    sub_queries = [subq for subq in sql_expr.find_all(exp.Subquery) if subq != sql_expr]
    for sub_query in sub_queries:
        # 添加：传递表别名信息给子查询处理
        subq_columns_dict = await _process_subquery_columns(db_path, sub_query, table_aliases, db_type)
        for table, columns in subq_columns_dict.items():
            if table not in columns_dict:
                columns_dict[table] = columns
            else:
                # 避免重复列
                columns_dict[table].extend([col for col in columns 
                                           if col.lower() not in [c.lower() for c in columns_dict[table]]])

    # 处理所有列引用
    for column in sql_expr.find_all(exp.Column):
        column_name = column.name
        table_alias = column.table
        
        # 处理表别名 - MySQL更强调使用别名
        table = _get_table_with_alias(sql_expr, table_alias) if table_alias else None
        
        # 处理可能包含数据库名的表引用 (db.table)
        if table and '.' in str(table.name):
            parts = str(table.name).split('.')
            # 检查是否是有效的数据库表名
            if len(parts) == 2 and any(t.lower() == parts[1].lower() for t in all_db_tables):
                table_name = parts[1]  # 使用表名部分
            else:
                table_name = table.name  # 使用完整名称
        else:
            table_name = table.name if table else None

        # 如果列没有明确的表限定，尝试找出它属于哪个表
        if not table_name:
            # 查找与列在同一查询上下文的表
            candidate_tables = [t for t in sql_expr.find_all(exp.Table) 
                              if _get_main_parent(t) == _get_main_parent(column)]
            
            # MySQL中特别处理JOIN和子查询
            join_tables = []
            for join in sql_expr.find_all(exp.Join):
                join_tables.extend([t for t in join.find_all(exp.Table)])
            candidate_tables.extend(join_tables)
            
            # 检查每个候选表是否包含该列
            for candidate_table in candidate_tables:
                # 处理可能包含数据库名的表
                if '.' in str(candidate_table.name):
                    parts = str(candidate_table.name).split('.')
                    cand_name = parts[-1]  # 使用最后一部分作为表名
                else:
                    cand_name = candidate_table.name
                    
                # 获取表的所有列并检查
                try:
                    table_columns = await get_table_all_columns(db_path, cand_name, db_type)
                    if column_name.lower() in [col.lower() for col in table_columns]:
                        table_name = cand_name
                        break
                except Exception as e:
                    logging.debug(f"获取表 {cand_name} 的列时出错: {e}")
                    continue

        # 将列添加到其表的映射中
        if table_name:
            # 清理表名 - MySQL使用反引号
            clean_table_name = str(table_name).replace('`', '').replace('"', '')
            
            if clean_table_name not in columns_dict:
                columns_dict[clean_table_name] = []
                
            # 清理列名 - MySQL使用反引号
            clean_column_name = str(column_name).replace('`', '').replace('"', '')
            
            # 添加：过滤掉别名被误认为列的情况
            if clean_column_name in table_aliases.get(clean_table_name, []):
                continue
                
            # 避免重复
            if clean_column_name.lower() not in [c.lower() for c in columns_dict[clean_table_name]]:
                columns_dict[clean_table_name].append(clean_column_name)

    return columns_dict

async def _check_value_exists(db_path: str, table_name: str, column_name: str, value: str, db_type: str = "mysql") -> Optional[str]:
    """
    异步检查数据库表的列中是否存在某个值。
    
    Args:
        db_path (str): 数据库文件路径。
        table_name (str): 表名。
        column_name (str): 列名。
        value (str): 要检查的值。
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        Optional[str]: 如果值存在，则返回该值，否则返回None。
    """
    db_config = create_database_config(db_path, db_type)
    query = f"SELECT `{column_name}` FROM `{table_name}` WHERE `{column_name}` LIKE '%{value}%' LIMIT 1"
    result = await execute_sql(db_config, query, "one")
    return result[0] if result else None

async def _extract_subquery_literals(db_path: str, subquery: exp.Expression, 
                                    parent_tables: Dict[str, List[str]] = None, 
                                    db_type: str = "mysql") -> Dict[str, Dict[str, List[str]]]:
    """
    从子查询中提取条件字面量
    
    Args:
        db_path: 数据库路径
        subquery: 子查询表达式
        parent_tables: 父查询的表和列映射
        db_type: 数据库类型
        
    Returns:
        包含子查询中条件字面量的字典
    """
    # 获取子查询中的表和列
    sub_columns = await _process_subquery_columns(db_path, subquery, parent_tables, db_type)
    used_entities = {}
    
    # 处理子查询中的字面量
    for literal in subquery.find_all(exp.Literal):
        if literal == literal.parent.expression:
            for column_exp in literal.parent.find_all(exp.Column):
                column_name = column_exp.name
                clean_column_name = str(column_name).replace('`', '')
                
                # 检查列属于哪个表
                for table_name, columns in sub_columns.items():
                    if clean_column_name.lower() in [col.lower() for col in columns]:
                        # 添加字面量
                        if table_name not in used_entities:
                            used_entities[table_name] = {}
                        if clean_column_name not in used_entities[table_name]:
                            used_entities[table_name][clean_column_name] = []
                        
                        example = literal.this
                        if example not in used_entities[table_name][clean_column_name]:
                            used_entities[table_name][clean_column_name].append(example)
                
                # 检查是否是对父查询表的引用
                if parent_tables and column_exp.table:
                    for parent_table, parent_columns in parent_tables.items():
                        if column_exp.table in parent_tables.get(parent_table, []):
                            if parent_table not in used_entities:
                                used_entities[parent_table] = {}
                            if clean_column_name not in used_entities[parent_table]:
                                used_entities[parent_table][clean_column_name] = []
                            
                            example = literal.this
                            if example not in used_entities[parent_table][clean_column_name]:
                                used_entities[parent_table][clean_column_name].append(example)
    
    return used_entities

async def _analyze_subquery_relationships(db_path: str, parsed_sql: exp.Expression, 
                                         table_aliases: Dict[str, List[str]], 
                                         columns_dict: Dict[str, List[str]],
                                         used_entities: Dict[str, Dict[str, List[str]]],
                                         db_type: str = "mysql") -> Dict[str, Dict[str, List[str]]]:
    """
    分析子查询与外部查询之间的关系条件，提取相关字面量。
    
    Args:
        db_path: 数据库路径
        parsed_sql: 解析后的SQL表达式
        table_aliases: 表别名映射
        columns_dict: 表和列的映射
        used_entities: 已提取的字面量字典
        db_type: 数据库类型
        
    Returns:
        更新后的used_entities字典
    """
    logging.info("开始分析子查询关系条件")
    # 处理子查询与外部查询的比较操作
    for binary in parsed_sql.find_all(exp.Binary):
        
        # 处理 column = (SELECT ...) 或 (SELECT ...) = column 模式
        if (isinstance(binary.left, exp.Column) and isinstance(binary.right, exp.Subquery)) or \
           (isinstance(binary.left, exp.Subquery) and isinstance(binary.right, exp.Column)):
            logger.info(f"处理二元操作符: {binary}")
            # 添加日志
            logger.info(f"二元操作符类型: {type(binary).__name__}, 操作符: {binary.key}")
            
            column = binary.left if isinstance(binary.left, exp.Column) else binary.right
            subquery = binary.right if isinstance(binary.right, exp.Subquery) else binary.left
            
            # 添加日志
            logger.info(f"列信息: 名称={column.name}, 表别名={column.table}")
            logger.info(f"子查询内容: {subquery.sql()[:100]}...")
            
            # 提取列信息
            col_name = str(column.name).replace('`', '')
            
            # 确定列所属的表
            tables_for_column = []
            if column.table:
                # 有表别名的情况
                for table_name, aliases in table_aliases.items():
                    if str(column.table) in aliases:
                        tables_for_column.append(table_name)
            else:
                # 没有表别名，尝试从columns_dict确定表
                for table_name, cols in columns_dict.items():
                    if col_name.lower() in [c.lower() for c in cols]:
                        tables_for_column.append(table_name)
            
            # 从子查询中提取第一个结果列的值(对于单列子查询)
            subquery_literals = subquery.find_all(exp.Literal)
            # 检查子查询的结构
            if isinstance(subquery.args.get('this'), exp.Select):
                select_expr = subquery.args['this']
                expressions = select_expr.args.get('expressions', [])
                
                # 添加日志
                logger.info(f"子查询SELECT部分: {select_expr}")
                
                # 特别检查WHERE条件
                where_clause = select_expr.args.get('where')
                if where_clause:
                    # 添加日志
                    logger.info(f"子查询WHERE条件: {where_clause}")
                    
                    # 检查WHERE条件中的相关引用
                    for binary_cond in where_clause.find_all(exp.Binary):
                        # 添加日志
                        logger.info(f"WHERE条件中的二元操作: {binary_cond}, 操作符: {binary_cond.key}")
                        
                        # 检查是否有相关引用
                        if isinstance(binary_cond.left, exp.Column) and binary_cond.left.table:
                            # 添加日志
                            logger.info(f"左侧列引用: {binary_cond.left.table}.{binary_cond.left.name}")
                        
                        if isinstance(binary_cond.right, exp.Column) and binary_cond.right.table:
                            # 添加日志
                            logger.info(f"右侧列引用: {binary_cond.right.table}.{binary_cond.right.name}")
                
                # 处理聚合函数和普通表达式
                for expr in expressions:
                    if isinstance(expr, exp.AggFunc):
                        logger.info(f"处理聚合函数: {type(expr).__name__}, 函数名: {expr.key}")
                        logger.info(f"聚合函数参数: {expr.args}")
                        # 处理聚合函数的情况
                        agg_column = expr.args.get('this')
                        if isinstance(agg_column, exp.Column):
                            col_name = str(agg_column.name).replace('`', '')
                            # 将聚合函数的列添加到相关表中
                            for table_name in tables_for_column:
                                if table_name not in used_entities:
                                    used_entities[table_name] = {}
                                if col_name not in used_entities[table_name]:
                                    used_entities[table_name][col_name] = []
                    elif isinstance(expr, exp.Literal):
                        # 处理字面量
                        value = expr.this
                        for table_name in tables_for_column:
                            if table_name not in used_entities:
                                used_entities[table_name] = {}
                            if col_name not in used_entities[table_name]:
                                used_entities[table_name][col_name] = []
                            if value not in used_entities[table_name][col_name]:
                                used_entities[table_name][col_name].append(value)
                
                # 在处理子查询WHERE条件后添加
                where_clause = select_expr.args.get('where')
                if where_clause:
                    # 添加处理结果的日志
                    logger.info(f"处理子查询WHERE条件后的used_entities: {used_entities}")
    
    # 处理IN子查询
    for in_expr in parsed_sql.find_all(exp.In):
        if isinstance(in_expr.args['this'], exp.Column) and isinstance(in_expr.args['expressions'], exp.Subquery):
            col = in_expr.args['this']
            subquery = in_expr.args['expressions']
            
            # 提取列信息
            col_name = str(col.name).replace('`', '')
            
            # 确定列所属的表
            for table_name, cols in columns_dict.items():
                if col_name.lower() in [c.lower() for c in cols]:
                    # 检查子查询的结构
                    if isinstance(subquery.args.get('this'), exp.Select):
                        select_expr = subquery.args['this']
                        expressions = select_expr.args.get('expressions', [])
                        
                        # 处理子查询中的表达式
                        for expr in expressions:
                            if isinstance(expr, exp.Literal):
                                # 直接的字面量
                                value = expr.this
                                if table_name not in used_entities:
                                    used_entities[table_name] = {}
                                if col_name not in used_entities[table_name]:
                                    used_entities[table_name][col_name] = []
                                if value not in used_entities[table_name][col_name]:
                                    used_entities[table_name][col_name].append(value)
                            elif isinstance(expr, exp.Column):
                                # 处理列引用
                                referenced_col = expr.name
                                # 查找该列的实际值
                                subquery_literals = subquery.find_all(exp.Literal)
                                for lit in subquery_literals:
                                    if lit.parent and isinstance(lit.parent, exp.Binary):
                                        binary = lit.parent
                                        if (isinstance(binary.left, exp.Column) and 
                                            str(binary.left.name) == referenced_col):
                                            value = lit.this
                                            if table_name not in used_entities:
                                                used_entities[table_name] = {}
                                            if col_name not in used_entities[table_name]:
                                                used_entities[table_name][col_name] = []
                                            if value not in used_entities[table_name][col_name]:
                                                used_entities[table_name][col_name].append(value)
                            elif isinstance(expr, exp.AggFunc):
                                # 处理聚合函数
                                agg_column = expr.args.get('this')
                                if isinstance(agg_column, exp.Column):
                                    agg_col_name = str(agg_column.name).replace('`', '')
                                    # 记录聚合函数使用的列
                                    if table_name not in used_entities:
                                        used_entities[table_name] = {}
                                    if agg_col_name not in used_entities[table_name]:
                                        used_entities[table_name][agg_col_name] = []
    
    # 处理EXISTS子查询中的相关条件
    for exists in parsed_sql.find_all(exp.Exists):
        if isinstance(exists.this, exp.Subquery):
            subquery = exists.this
            
            # 查找相关子查询条件(子查询中引用外部查询列的条件)
            for binary in subquery.find_all(exp.Binary):
                # 只处理相等条件
                if binary.operator not in ['=', '==']:
                    continue
                
                # 检查是否有一边是内部列，一边是外部列的引用
                if isinstance(binary.left, exp.Column) and isinstance(binary.right, exp.Column):
                    left_col, right_col = binary.left, binary.right
                    
                    # 判断哪个是外部列引用
                    outer_col = None
                    if left_col.table and any(left_col.table in aliases for aliases in table_aliases.values()):
                        outer_col = left_col
                    elif right_col.table and any(right_col.table in aliases for aliases in table_aliases.values()):
                        outer_col = right_col
                    
                    if outer_col:
                        outer_col_name = str(outer_col.name).replace('`', '')
                        
                        # 找出外部列所属的表
                        for table_name, aliases in table_aliases.items():
                            if outer_col.table in aliases:
                                # 检查子查询的结构
                                if isinstance(subquery.args.get('this'), exp.Select):
                                    select_expr = subquery.args['this']
                                    where_clause = select_expr.args.get('where')
                                    
                                    if where_clause:
                                        # 处理WHERE子句中的条件
                                        where_literals = where_clause.find_all(exp.Literal)
                                        for lit in where_literals:
                                            if lit.parent and isinstance(lit.parent, exp.Binary):
                                                binary_cond = lit.parent
                                                # 检查是否是与外部列相关的条件
                                                if isinstance(binary_cond.left, exp.Column):
                                                    value = lit.this
                                                    if table_name not in used_entities:
                                                        used_entities[table_name] = {}
                                                    if outer_col_name not in used_entities[table_name]:
                                                        used_entities[table_name][outer_col_name] = []
                                                    if value not in used_entities[table_name][outer_col_name]:
                                                        used_entities[table_name][outer_col_name].append(value)
    
    # 增加：处理相关子查询中的条件引用
    sub_queries = list(parsed_sql.find_all(exp.Subquery))
    logging.info(f"发现子查询数量: {len(sub_queries)}")
    for subquery in sub_queries:
        # 记录子查询内容
        logging.info(f"分析子查询: {subquery.sql()[:100]}...")
        
        # 查找子查询中的二元条件表达式
        binary_conditions = list(subquery.find_all(exp.Binary))
        
        for binary in binary_conditions:
            # 检查二元操作符类型
            is_comparison = isinstance(binary, (exp.EQ, exp.GT, exp.LT, exp.GTE, exp.LTE, exp.NEQ))
            
            if is_comparison:
                # 检查是否一边是列另一边是字面量
                col_side, lit_side = None, None
                outer_ref = False
                
                if isinstance(binary.left, exp.Column):
                    col_side = binary.left
                    # 检查是否引用了外部查询的列
                    if col_side.table and '.' in str(col_side.table):
                        outer_ref = True
                        logger.info(f"发现外部引用: {col_side.table}.{col_side.name}")
                elif isinstance(binary.right, exp.Column):
                    col_side = binary.right
                    # 检查是否引用了外部查询的列
                    if col_side.table and '.' in str(col_side.table):
                        outer_ref = True
                        logger.info(f"发现外部引用: {col_side.table}.{col_side.name}")
                
                if isinstance(binary.right, exp.Literal):
                    lit_side = binary.right
                elif isinstance(binary.left, exp.Literal):
                    lit_side = binary.left
                
                # 如果找到了外部引用和字面量
                if outer_ref and lit_side and col_side:
                    col_name = str(col_side.name).replace('`', '')
                    outer_table_ref = str(col_side.table).split('.')[0]
                    
                    # 尝试匹配外部表引用
                    for table_name, aliases in table_aliases.items():
                        if outer_table_ref in aliases:
                            value = lit_side.this
                            
                            # 添加到对应的表-列组合
                            if table_name not in used_entities:
                                used_entities[table_name] = {}
                            if col_name not in used_entities[table_name]:
                                used_entities[table_name][col_name] = []
                            if value not in used_entities[table_name][col_name]:
                                used_entities[table_name][col_name].append(value)
    
    # 处理与聚合函数子查询的比较
    for binary in parsed_sql.find_all(exp.Binary):
        # 检查是否是比较操作符
        if not isinstance(binary, (exp.EQ, exp.GT, exp.LT, exp.GTE, exp.LTE, exp.NEQ)):
            continue
            
        # 查找形如 column > (SELECT AVG(...)) 的模式
        if (isinstance(binary.left, exp.Column) and isinstance(binary.right, exp.Subquery)) or \
           (isinstance(binary.left, exp.Subquery) and isinstance(binary.right, exp.Column)):
            
            column = binary.left if isinstance(binary.left, exp.Column) else binary.right
            subquery = binary.right if isinstance(binary.right, exp.Subquery) else binary.left
            
            logging.info(f"聚合函数处理 - 操作符: {binary.key}, 列: {column.name}, 子查询: {subquery.sql()[:100]}...")
            
            # 检查子查询的结构
            if isinstance(subquery.args.get('this'), exp.Select):
                select_expr = subquery.args['this']
                expressions = select_expr.args.get('expressions', [])
                
                # 提取列信息
                col_name = str(column.name).replace('`', '')
                
                # 确定列所属的表
                tables_for_column = []
                if column.table:
                    # 有表别名的情况
                    for table_name, aliases in table_aliases.items():
                        if str(column.table) in aliases:
                            tables_for_column.append(table_name)
                else:
                    # 从columns_dict确定表
                    for table_name, cols in columns_dict.items():
                        if col_name.lower() in [c.lower() for c in cols]:
                            tables_for_column.append(table_name)
                logging.info(f"聚合函数处理 - 子查询表达式: {[type(expr).__name__ for expr in expressions]}")
                
                # 处理子查询中的表达式
                for expr in expressions:
                    if isinstance(expr, exp.AggFunc):
                        logging.info(f"聚合函数处理 - 发现聚合函数: {type(expr).__name__}, 参数: {expr.args}")
                        # 处理聚合函数
                        agg_column = expr.args.get('this')
                        if isinstance(agg_column, exp.Column):
                            agg_col_name = str(agg_column.name).replace('`', '')
                            # 记录聚合函数使用的列
                            for table_name in tables_for_column:
                                if table_name not in used_entities:
                                    used_entities[table_name] = {}
                                if agg_col_name not in used_entities[table_name]:
                                    used_entities[table_name][agg_col_name] = []
                                
                                # 检查聚合函数的WHERE条件中的字面量
                                where_clause = select_expr.args.get('where')
                                if where_clause:
                                    for binary_cond in where_clause.find_all(exp.Binary):
                                        if isinstance(binary_cond.left, exp.Column) and isinstance(binary_cond.right, exp.Literal):
                                            # 获取正确的列名和值
                                            where_col_name = str(binary_cond.left.name).replace('`', '')
                                            value = binary_cond.right.this
                                            
                                            # 将值添加到正确的列中
                                            if where_col_name not in used_entities[table_name]:
                                                used_entities[table_name][where_col_name] = []
                                            if value not in used_entities[table_name][where_col_name]:
                                                used_entities[table_name][where_col_name].append(value)
                                        # 处理值在左侧的情况
                                        elif isinstance(binary_cond.right, exp.Column) and isinstance(binary_cond.left, exp.Literal):
                                            where_col_name = str(binary_cond.right.name).replace('`', '')
                                            value = binary_cond.left.this
                                            
                                            if where_col_name not in used_entities[table_name]:
                                                used_entities[table_name][where_col_name] = []
                                            if value not in used_entities[table_name][where_col_name]:
                                                used_entities[table_name][where_col_name].append(value)
                    
                    elif isinstance(expr, exp.Literal):
                        # 处理直接的字面量
                        value = expr.this
                        for table_name in tables_for_column:
                            if table_name not in used_entities:
                                used_entities[table_name] = {}
                            if col_name not in used_entities[table_name]:
                                used_entities[table_name][col_name] = []
                            if value not in used_entities[table_name][col_name]:
                                used_entities[table_name][col_name].append(value)
                    
                    elif isinstance(expr, exp.Column):
                        # 处理列引用
                        referenced_col = str(expr.name).replace('`', '')
                        # 检查WHERE子句中的条件
                        where_clause = select_expr.args.get('where')
                        if where_clause:
                            for binary_cond in where_clause.find_all(exp.Binary):
                                if isinstance(binary_cond.right, exp.Literal) and \
                                   isinstance(binary_cond.left, exp.Column) and \
                                   str(binary_cond.left.name) == referenced_col:
                                    value = binary_cond.right.this
                                    for table_name in tables_for_column:
                                        if table_name not in used_entities:
                                            used_entities[table_name] = {}
                                        if col_name not in used_entities[table_name]:
                                            used_entities[table_name][col_name] = []
                                        if value not in used_entities[table_name][col_name]:
                                            used_entities[table_name][col_name].append(value)
    
    logging.info("完成子查询关系分析，提取到的字面量: {used_entities}")
    return used_entities

async def get_sql_condition_literals(db_path: str, sql: str, db_type: str = "mysql") -> Dict[str, Dict[str, List[str]]]:
    """
    异步检索SQL查询条件中使用的字面量并检查它们在数据库中是否存在。
    针对MySQL优化，增强子查询处理。
    
    Args:
        db_path (str): 数据库连接字符串。
        sql (str): SQL查询字符串。
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        Dict[str, Dict[str, List[str]]]: 表及其列与条件字面量的字典。
    """
    try:
        # 获取SQL中涉及的表和列
        columns_dict = await get_sql_columns_dict(db_path=db_path, sql=sql, db_type=db_type)
        used_entities = {}
        
        # 添加：收集表别名
        table_aliases = {}
        
        # 使用正确的方言
        dialect = db_type.lower()
        if dialect not in ['mysql', 'postgresql', 'bigquery', 'snowflake', 'spark', 'duckdb']:
            dialect = 'sqlite'  # 默认回退
        
        # 解析SQL
        parsed_sql = parse_one(sql, read=dialect)
        
        # 添加：收集表别名信息
        for table in parsed_sql.find_all(exp.Table):
            table_name = str(table.name).replace('`', '').replace('"', '')
            if '.' in table_name:
                table_name = table_name.split('.')[-1]
                
            if table_name not in table_aliases:
                table_aliases[table_name] = []
                
            if table.alias:
                alias = str(table.alias)
                if alias not in table_aliases[table_name]:
                    table_aliases[table_name].append(alias)
        
        # 添加：专门处理子查询
        sub_queries = [subq for subq in parsed_sql.find_all(exp.Subquery) if subq != parsed_sql]
        for sub_query in sub_queries:
            logging.info(f"处理子查询: {sub_query}")
            sub_literals = await _extract_subquery_literals(db_path, sub_query, table_aliases, db_type)
            for table, cols in sub_literals.items():
                if table not in used_entities:
                    used_entities[table] = {}
                for col, values in cols.items():
                    if col not in used_entities[table]:
                        used_entities[table][col] = []
                    used_entities[table][col].extend([v for v in values if v not in used_entities[table][col]])
        
        # 添加: 处理BETWEEN操作符
        for between_expr in parsed_sql.find_all(exp.Between):
            # 通过args字典访问Between的组件
            if isinstance(between_expr.args['this'], exp.Column):
                col = between_expr.args['this']
                col_name = str(col.name).replace('`', '')
                
                # 获取字面量对象
                low_value_obj = between_expr.args['low']
                high_value_obj = between_expr.args['high']
                
                # 提取字面量的实际值
                low_value = str(low_value_obj.this) if hasattr(low_value_obj, 'this') else str(low_value_obj)
                high_value = str(high_value_obj.this) if hasattr(high_value_obj, 'this') else str(high_value_obj)
                
                # 查找列所属的表
                if col.table:
                    # 有表别名的情况
                    for table_name, aliases in table_aliases.items():
                        if str(col.table) in aliases:
                            # 添加下限值
                            if low_value:
                                if table_name not in used_entities:
                                    used_entities[table_name] = {}
                                if col_name not in used_entities[table_name]:
                                    used_entities[table_name][col_name] = []
                                if low_value not in used_entities[table_name][col_name]:
                                    used_entities[table_name][col_name].append(low_value)
                            
                            # 添加上限值
                            if high_value:
                                if table_name not in used_entities:
                                    used_entities[table_name] = {}
                                if col_name not in used_entities[table_name]:
                                    used_entities[table_name][col_name] = []
                                if high_value not in used_entities[table_name][col_name]:
                                    used_entities[table_name][col_name].append(high_value)
                else:
                    # 没有表别名，尝试从columns_dict确定表
                    for table_name, cols in columns_dict.items():
                        if col_name.lower() in [c.lower() for c in cols]:
                            # 添加下限值
                            if low_value:
                                if table_name not in used_entities:
                                    used_entities[table_name] = {}
                                if col_name not in used_entities[table_name]:
                                    used_entities[table_name][col_name] = []
                                if low_value not in used_entities[table_name][col_name]:
                                    used_entities[table_name][col_name].append(low_value)
                            
                            # 添加上限值
                            if high_value:
                                if table_name not in used_entities:
                                    used_entities[table_name] = {}
                                if col_name not in used_entities[table_name]:
                                    used_entities[table_name][col_name] = []
                                if high_value not in used_entities[table_name][col_name]:
                                    used_entities[table_name][col_name].append(high_value)
        
        # 原有的字面量处理逻辑保持不变
        for sql_exp in parsed_sql.flatten():
            for literal in sql_exp.find_all(exp.Literal):
                if literal == literal.parent.expression:
                    for column_exp in literal.parent.find_all(exp.Column):
                        column_name = column_exp.name
                        clean_column_name = str(column_name).replace('`', '')
                        
                        for table_name, column_names in columns_dict.items():
                            if clean_column_name.lower() in [col.lower() for col in column_names]:
                                example_exist = False
                                example = literal.this
                                
                                parent_str = str(literal.parent)
                                
                                # 处理特定情况的逻辑保持不变
                                if "(" in parent_str and "SELECT" not in parent_str.upper():  # 添加：避免将子查询视为函数
                                    value_check = await _check_value_exists(db_path, table_name, clean_column_name, literal.this, db_type)
                                    if value_check:
                                        example_exist = True
                                        example = value_check
                                
                                elif "LIKE" in parent_str:
                                    example_to_search = literal.this.replace("%", "").replace("_", "")
                                    value_check = await _check_value_exists(db_path, table_name, clean_column_name, example_to_search, db_type)
                                    if value_check:
                                        example_exist = True
                                        example = example_to_search
                                
                                elif "REGEXP" in parent_str or "RLIKE" in parent_str:
                                    pattern = literal.this
                                    if pattern.startswith('^'):
                                        pattern = pattern[1:]
                                    if pattern.endswith('$'):
                                        pattern = pattern[:-1]
                                    value_check = await _check_value_exists(db_path, table_name, clean_column_name, pattern, db_type)
                                    if value_check:
                                        example_exist = True
                                        example = value_check
                                
                                # 简单条件处理保持不变
                                elif "IN (" in parent_str:
                                    example_exist = True
                                
                                elif any(op in parent_str for op in ["=", "<", ">", "<=", ">=", "<>"]):
                                    example_exist = True
                                    
                                else:
                                    example_exist = True
                                
                                if example_exist:
                                    if table_name not in used_entities:
                                        used_entities[table_name] = {}
                                    
                                    if clean_column_name not in used_entities[table_name]:
                                        used_entities[table_name][clean_column_name] = []
                                    
                                    if example not in used_entities[table_name][clean_column_name]:
                                        used_entities[table_name][clean_column_name].append(example)
        
        # 添加：特别处理WHERE子句中的条件
        where_clauses = parsed_sql.find_all(exp.Where)
        logging.info(f"比较操作符处理 - 找到 {where_clauses.gi_frame.f_locals}")
        for where in where_clauses:
            # 处理子查询外层的条件
            binary_ops = where.find_all(exp.Binary)
            logging.info(f"比较操作符处理 - WHERE子句中发现 {binary_ops.gi_frame.f_locals}")
            
            for binary in binary_ops:
                logging.info(f"比较操作符处理 - 操作符: {binary}, 类型: {type(binary).__name__}")
                logging.info(f"比较操作符处理 - 左操作数: {type(binary.left).__name__}, 右操作数: {type(binary.right).__name__}")
                
                # 处理AND/OR连接的条件
                if isinstance(binary, (exp.And, exp.Or)):
                    # 分别处理左右两边的条件
                    for side in [binary.left, binary.right]:
                        if isinstance(side, exp.Binary):
                            if isinstance(side.left, exp.Column) and isinstance(side.right, exp.Literal):
                                col = side.left
                                lit = side.right
                                col_name = str(col.name).replace('`', '')
                                
                                # 确定列属于哪个表
                                if col.table:
                                    for table_name, aliases in table_aliases.items():
                                        if str(col.table) in aliases:
                                            if table_name not in used_entities:
                                                used_entities[table_name] = {}
                                            if col_name not in used_entities[table_name]:
                                                used_entities[table_name][col_name] = []
                                            if lit.this not in used_entities[table_name][col_name]:
                                                used_entities[table_name][col_name].append(lit.this)
                                else:
                                    # 对于没有表别名的列，尝试从columns_dict确定表
                                    for table_name, cols in columns_dict.items():
                                        if col_name.lower() in [c.lower() for c in cols]:
                                            if table_name not in used_entities:
                                                used_entities[table_name] = {}
                                            if col_name not in used_entities[table_name]:
                                                used_entities[table_name][col_name] = []
                                            if lit.this not in used_entities[table_name][col_name]:
                                                used_entities[table_name][col_name].append(lit.this)
                # 处理单个比较条件
                elif isinstance(binary.left, exp.Column) and isinstance(binary.right, exp.Literal):
                    col = binary.left
                    lit = binary.right
                    col_name = str(col.name).replace('`', '')
                    
                    # 确定列属于哪个表
                    if col.table:
                        for table_name, aliases in table_aliases.items():
                            if str(col.table) in aliases:
                                if table_name not in used_entities:
                                    used_entities[table_name] = {}
                                if col_name not in used_entities[table_name]:
                                    used_entities[table_name][col_name] = []
                                if lit.this not in used_entities[table_name][col_name]:
                                    used_entities[table_name][col_name].append(lit.this)
                    else:
                        # 对于没有表别名的列，尝试从columns_dict确定表
                        for table_name, cols in columns_dict.items():
                            if col_name.lower() in [c.lower() for c in cols]:
                                if table_name not in used_entities:
                                    used_entities[table_name] = {}
                                if col_name not in used_entities[table_name]:
                                    used_entities[table_name][col_name] = []
                                if lit.this not in used_entities[table_name][col_name]:
                                    used_entities[table_name][col_name].append(lit.this)
        
        # 在调用_analyze_subquery_relationships前添加日志
        logging.info(f"处理前的字面量收集结果: {used_entities}")
        
        # 添加：分析子查询关系条件
        used_entities = await _analyze_subquery_relationships(
            db_path, parsed_sql, table_aliases, columns_dict, used_entities, db_type
        )
        
        # 添加日志
        logging.info(f"最终的字面量收集结果: {used_entities}")
        
        return used_entities
    except Exception as e:
        logging.critical(f"Error in get_sql_condition_literals: {e}\nSQL {sql}\nDB type: {db_type}")
        raise e

async def _check_value_exists_mysql(db_path: str, table_name: str, column_name: str, value: str, db_type: str = "mysql") -> Optional[str]:
    """
    检查MySQL数据库表的列中是否存在某个值。
    针对MySQL的LIKE查询优化。
    
    Args:
        db_path (str): 数据库连接字符串。
        table_name (str): 表名。
        column_name (str): 列名。
        value (str): 要检查的值。
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        Optional[str]: 如果值存在，则返回该值，否则返回None。
    
    示例:
        1. 基本查询:
           输入: 
             db_path = "user:password@localhost:3306/mydb"
             table_name = "customers"
             column_name = "name"
             value = "John"
           SQL生成: SELECT `name` FROM `customers` WHERE `name` LIKE '%John%' LIMIT 1
           输出: 如果找到匹配值，返回 "John Smith"；如果未找到，返回 None
        
        2. 带有数据库名的表:
           输入:
             db_path = "user:password@localhost:3306/mydb"
             table_name = "sales.orders"
             column_name = "order_id"
             value = "1001"
           SQL生成: SELECT `order_id` FROM `orders` WHERE `order_id` LIKE '%1001%' LIMIT 1
           输出: 如果找到匹配值，返回 "1001"；如果未找到，返回 None
           
        3. 处理特殊字符:
           输入:
             db_path = "user:password@localhost:3306/mydb"
             table_name = "products"
             column_name = "description"
             value = "50% off"
           SQL生成: SELECT `description` FROM `products` WHERE `description` LIKE '%50% off%' LIMIT 1
           输出: 如果找到匹配值，返回 "Summer sale: 50% off all items"；如果未找到，返回 None
           
        4. 处理SQL注入尝试:
           输入:
             db_path = "user:password@localhost:3306/mydb"
             table_name = "users"
             column_name = "username"
             value = "admin'; DROP TABLE users; --"
           SQL生成: SELECT `username` FROM `users` WHERE `username` LIKE '%admin''; DROP TABLE users; --%' LIMIT 1
           输出: 安全处理后，可能返回 None，防止SQL注入
    """
    # 创建数据库配置
    db_config = create_database_config(db_path, db_type)
    
    # 处理可能的表名格式（database.table）
    table_parts = table_name.split('.')
    clean_table = table_parts[-1].replace('`', '')
    
    # 对于MySQL，使用适合的引号风格(反引号)和转义
    # 使用参数化查询防止SQL注入
    try:
        # 安全移除任何可能导致SQL注入的字符
        safe_value = value.replace("'", "''")
        
        # 构建安全的查询
        query = f"SELECT `{column_name}` FROM `{clean_table}` WHERE `{column_name}` LIKE '%{safe_value}%' LIMIT 1"
        
        # 执行查询
        result = await execute_sql(db_config, query, "one")
        return result[0] if result else None
    except Exception as e:
        logging.debug(f"检查值存在时出错: {e}, 表: {clean_table}, 列: {column_name}, 值: {value}")
        return None

if __name__ == "__main__":
    import os
    import dotenv
    from urllib.parse import quote_plus
    
    async def main():
        # 加载环境变量
        dotenv.load_dotenv()
        
        # 构建数据库连接字符串
        db_path = f"{os.getenv('DATABASE_USER')}:{quote_plus(os.getenv('DATABASE_PASSWORD'))}@{os.getenv('DATABASE_HOST')}:{os.getenv('DATABASE_PORT')}/{os.getenv('DATABASE_NAME')}"
        
        # 示例SQL查询
        test_queries = [
            # 基本SELECT查询 - 查询客户资产信息
            """
            SELECT cust_id, crm_biz_id, curr_bal_rmb 
            FROM cust_asset_info 
            WHERE curr_bal_rmb > 100000 
            AND crm_biz_id = '01'
            """,
            
            # 带Where 复杂条件
            """
            SELECT SUM(loan_bal_cny) 
            FROM adm_lon_varoius 
            WHERE substring(loan_purpose FROM 1 FOR 1) = 'Z' AND term_type = '01' AND loan_type IS NOT NULL
            GROUP BY loan_type, branch_id;
            """,
            
            # 复杂JOIN查询 - 查询客户、资产和客户经理信息
            """
            SELECT m.cust_name, m.mainten_object_id,
                   b.age, b.gender_cd,
                   a.crm_biz_id, a.curr_bal_rmb
            FROM cust_manager_rela_h m
            JOIN cust_basic_info b ON m.cust_id = b.cust_id
            JOIN cust_asset_info a ON b.cust_id = a.cust_id
            WHERE b.age BETWEEN 25 AND 50
            AND a.curr_bal_rmb > 100000
            """,
            
            # 带子查询的复杂查询 - 查询高于平均余额的客户
            """
            SELECT a.cust_id, a.crm_biz_id, a.curr_bal_rmb,
                   b.age, m.cust_name
            FROM cust_asset_info a
            JOIN cust_basic_info b ON a.cust_id = b.cust_id
            JOIN cust_manager_rela_h m ON a.cust_id = m.cust_id
            WHERE a.curr_bal_rmb > (
                SELECT AVG(curr_bal_rmb)
                FROM cust_asset_info
                WHERE crm_biz_id = 1000
            )
            """
        ]
        
        try:
            print("=== SQL解析测试 ===")
            
            for i, sql in enumerate(test_queries, 1):
                print(f"\n测试查询 {i}:")
                print("SQL:", sql.strip())
                
                # 1. 格式化SQL
                formatted_sql = format_sql_query((' '.join(sql.strip().split()),))
                print("\n格式化后的SQL:")
                print(formatted_sql)
                
                # 2. 获取涉及的表
                tables = await get_sql_tables(db_path, formatted_sql)
                print("\n涉及的表:")
                print(tables)
                
                # 3. 获取表和列的映射
                columns_dict = await get_sql_columns_dict(db_path, formatted_sql)
                print("\n表和列的映射:")
                for table, columns in columns_dict.items():
                    print(f"{table}: {columns}")
                
                # 4. 获取条件中的字面量
                literals = await get_sql_condition_literals(db_path, formatted_sql)
                print("\n条件字面量:")
                for table, cols in literals.items():
                    print(f"\n{table}:")
                    for col, values in cols.items():
                        print(f"  {col}: {values}")
                
                print("\n" + "="*50)
                
        except Exception as e:
            print(f"测试过程中出错: {e}")
            raise e

    # 运行异步主函数
    asyncio.run(main())
