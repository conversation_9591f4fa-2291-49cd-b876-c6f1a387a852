import logging
from typing import List, Dict, Union, Optional
from sqlalchemy import inspect, MetaData, Table, text
import sqlglot
import asyncio
if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from database_utils.retrieval.async_sqlalchemy_execution import (
    SQLDatabaseConfig, create_database_config, execute_sql
)

async def get_db_all_tables_async(db_config: SQLDatabaseConfig) -> List[str]:
    """
    异步获取数据库中所有表名
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置对象
        
    Returns:
        List[str]: 表名列表
    """
    try:
        engine = db_config.create_engine()
        async with engine.connect() as conn:
            if 'sqlite' in db_config.url.lower():
                # SQLite 特定方法
                result = await conn.execute(text("SELECT name FROM sqlite_master WHERE type='table';"))
                tables = [table[0].replace('\"', '').replace('`', '') for table in await result.fetchall() 
                         if table[0] != "sqlite_sequence"]
            else:
                # 使用SQLAlchemy反射功能获取所有表
                tables = await conn.run_sync(lambda sync_conn: inspect(sync_conn).get_table_names())
            
            return tables
    except Exception as e:
        logging.error(f"获取所有表出错: {e}")
        raise e
    finally:
        await engine.dispose()

async def get_db_all_tables(db_path: str, db_type: str = "mysql") -> List[str]:
    """
    异步获取数据库中所有表名
    
    Args:
        db_path (str): 数据库路径或连接字符串
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        List[str]: 表名列表
    """
    
    db_config = create_database_config(db_path, db_type)
    
    if db_config.is_async:
        return await get_db_all_tables_async(db_config)
    else:
        try:
            # 同步方式
            engine = db_config.create_engine()
            if 'sqlite' in db_config.url.lower():
                # 对于SQLite使用原始查询
                raw_table_names = execute_sql(db_config, "SELECT name FROM sqlite_master WHERE type='table';")
                return [table[0].replace('\"', '').replace('`', '') for table in raw_table_names 
                       if table[0] != "sqlite_sequence"]
            else:
                # 使用SQLAlchemy元数据
                inspector = inspect(engine)
                return inspector.get_table_names()
        except Exception as e:
            logging.error(f"获取所有表出错: {e}")
            raise e

async def get_table_all_columns_async(db_config: SQLDatabaseConfig, table_name: str) -> List[str]:
    """
    异步获取指定表的所有列名
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置对象
        table_name (str): 表名
        
    Returns:
        List[str]: 列名列表
    """
    try:
        engine = db_config.create_engine()
        async with engine.connect() as conn:
            if 'sqlite' in db_config.url.lower():
                # SQLite 特定方法
                result = await conn.execute(text(f"PRAGMA table_info(`{table_name}`);"))
                columns = [row[1].replace('\"', '').replace('`', '') for row in await result.fetchall()]
            else:
                # 使用SQLAlchemy反射功能
                columns = await conn.run_sync(
                    lambda sync_conn: [col['name'] for col in inspect(sync_conn).get_columns(table_name)]
                )
            
            return columns
    except Exception as e:
        logging.error(f"获取表 {table_name} 的列名出错: {e}")
        raise e
    finally:
        await engine.dispose()

async def get_table_all_columns(db_path: str, table_name: str, db_type: str = "mysql") -> List[str]:
    """
    获取指定表的所有列名的统一接口
    
    Args:
        db_path (str): 数据库路径或连接字符串
        table_name (str): 表名
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        List[str]: 列名列表
    """
    db_config = create_database_config(db_path, db_type)
    
    if db_config.is_async:
        return await get_table_all_columns_async(db_config, table_name)
    else:
        try:
            # 同步方式
            engine = db_config.create_engine()
            if 'sqlite' in db_config.url.lower():
                # 对于SQLite使用原始查询
                table_info_rows = execute_sql(db_config, f"PRAGMA table_info(`{table_name}`);")
                return [row[1].replace('\"', '').replace('`', '') for row in table_info_rows]
            else:
                # 使用SQLAlchemy元数据
                inspector = inspect(engine)
                return [col['name'] for col in inspector.get_columns(table_name)]
        except Exception as e:
            logging.error(f"获取表 {table_name} 的列名出错: {e}")
            raise e

async def get_db_schema_async(db_config: SQLDatabaseConfig) -> Dict[str, List[str]]:
    """
    异步获取数据库模式
    
    Args:
        db_config (SQLDatabaseConfig): 数据库配置对象
        
    Returns:
        Dict[str, List[str]]: 表名到列名列表的映射字典
    """
    try:
        table_names = await get_db_all_tables_async(db_config)
        schema = {}
        
        for table_name in table_names:
            schema[table_name] = await get_table_all_columns_async(db_config, table_name)
            
        return schema
    except Exception as e:
        logging.error(f"获取数据库模式出错: {e}")
        raise e

async def get_db_schema(db_path: str, db_type: str = "mysql") -> Dict[str, List[str]]:
    """
    获取数据库模式的统一接口
    
    Args:
        db_path (str): 数据库路径或连接字符串
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        Dict[str, List[str]]: 表名到列名列表的映射字典
    """
    try:
        # 获取所有表
        table_names = await get_db_all_tables(db_path, db_type)
        # 为每个表获取列
        return {table_name: await get_table_all_columns(db_path, table_name, db_type) for table_name in table_names}
    except Exception as e:
        logging.error(f"获取数据库模式出错: {e}")
        raise e

def get_table_primary_keys(db_path: str, table_name: str, db_type: str = "mysql") -> List[str]:
    """
    获取表的主键列
    
    Args:
        db_path (str): 数据库路径或连接字符串
        table_name (str): 表名
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        List[str]: 主键列名列表
    """
    db_config = create_database_config(db_path, db_type, is_async=False)
    engine = db_config.create_engine()
    
    try:
        inspector = inspect(engine)
        return inspector.get_pk_constraint(table_name)['constrained_columns']
    except Exception as e:
        logging.error(f"获取表 {table_name} 的主键出错: {e}")
        raise e

def get_table_foreign_keys(db_path: str, table_name: str, db_type: str = "mysql") -> List[Dict]:
    """
    获取表的外键约束
    
    Args:
        db_path (str): 数据库路径或连接字符串
        table_name (str): 表名
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        List[Dict]: 外键约束信息列表
    """
    db_config = create_database_config(db_path, db_type, is_async=False)
    engine = db_config.create_engine()
    
    try:
        inspector = inspect(engine)
        return inspector.get_foreign_keys(table_name)
    except Exception as e:
        logging.error(f"获取表 {table_name} 的外键出错: {e}")
        raise e

def get_table_indexes(db_path: str, table_name: str, db_type: str = "mysql") -> List[Dict]:
    """
    获取表的索引
    
    Args:
        db_path (str): 数据库路径或连接字符串
        table_name (str): 表名
        db_type (str): 数据库类型 ("sqlite", "mysql" 等)
        
    Returns:
        List[Dict]: 索引信息列表
    """
    db_config = create_database_config(db_path, db_type, is_async=False)
    engine = db_config.create_engine()
    
    try:
        inspector = inspect(engine)
        return inspector.get_indexes(table_name)
    except Exception as e:
        logging.error(f"获取表 {table_name} 的索引出错: {e}")
        raise e

def analyze_query_tables(sql: str) -> List[str]:
    """
    使用SQLGlot分析SQL查询中引用的表
    
    Args:
        sql (str): SQL查询字符串
        
    Returns:
        List[str]: 查询中引用的表名列表
    """
    try:
        parsed = sqlglot.parse_one(sql)
        tables = set()
        
        # 收集所有表引用
        for table in parsed.find_all(sqlglot.exp.Table):
            tables.add(table.name)
            
        return list(tables)
    except Exception as e:
        logging.error(f"分析SQL查询的表出错: {e}")
        return []

def analyze_query_columns(sql: str) -> Dict[str, List[str]]:
    """
    使用SQLGlot分析SQL查询中引用的列
    
    Args:
        sql (str): SQL查询字符串
        
    Returns:
        Dict[str, List[str]]: 表名到列名列表的映射
    """
    try:
        parsed = sqlglot.parse_one(sql)
        columns_by_table = {}
        
        # 收集带有表限定符的列
        for column in parsed.find_all(sqlglot.exp.Column):
            if column.table:
                table_name = column.table.name
                if table_name not in columns_by_table:
                    columns_by_table[table_name] = []
                columns_by_table[table_name].append(column.name)
        
        # 收集不带表限定符的列
        for column in parsed.find_all(sqlglot.exp.Column):
            if not column.table:
                if 'unknown' not in columns_by_table:
                    columns_by_table['unknown'] = []
                columns_by_table['unknown'].append(column.name)
                
        return columns_by_table
    except Exception as e:
        logging.error(f"分析SQL查询的列出错: {e}")
        return {} 
    
if __name__ == "__main__":
    import os
    import dotenv
    from urllib.parse import quote_plus
    
    async def main():
        dotenv.load_dotenv()
        engine_url = f"{os.getenv('DB_USER')}:{quote_plus(os.getenv('DB_PASSWORD'))}@{os.getenv('DB_HOST')}:{os.getenv('DB_PORT')}/{os.getenv('DB_NAME')}"
        
        # 获取所有表名
        tables = await get_db_all_tables(engine_url)
        print(f"数据库中的表: {tables}")

        # 获取特定表的所有列
        columns = await get_table_all_columns(engine_url, "cust_asset_info")
        print(f"cust_asset_info表的列: {columns}")

        # 获取整个数据库模式
        schema = await get_db_schema(engine_url)
        print("数据库模式:")
        for table, cols in schema.items():
            print(f"  {table}: {cols}")

        # 使用MySQL
        mysql_tables = await get_db_all_tables(engine_url, db_type="mysql")
        print(f"MySQL数据库中的表: {mysql_tables}")

        # 分析SQL查询 (这两个函数是同步的，不需要await)
        sql = "SELECT cust_id, crm_biz_id, biz_lev_cd FROM cust_asset_info WHERE cust_id = '1021111034'"
        tables = analyze_query_tables(sql)
        print(f"查询中的表: {tables}")

        columns = analyze_query_columns(sql)
        print(f"查询中的列: {columns}")

    # 在最顶层运行异步主函数
    asyncio.run(main())