import logging
from typing import Dict, List, Optional, Set, Tuple
from sqlglot import parse_one, exp

logger = logging.getLogger(__name__)

class TempTableManager:
    """
    临时表管理器，用于跟踪和解析SQL查询中的临时表。
    支持派生表（子查询）和CTE（WITH子句）。
    """
    
    def __init__(self):
        self.temp_tables = {}  # {table_name: {columns: [], source_tables: []}}
        self.cte_definitions = {}  # 存储CTE定义 {cte_name: parsed_expression}
        
    def clear(self):
        """清除所有临时表信息"""
        self.temp_tables = {}
        self.cte_definitions = {}
    
    def extract_temp_tables(self, sql_expr: exp.Expression) -> None:
        """
        从SQL表达式中提取所有临时表定义
        
        Args:
            sql_expr: 解析后的SQL表达式
        """
        # 处理WITH子句（CTE）
        self._extract_cte_definitions(sql_expr)
        
        # 处理派生表（子查询作为表）
        self._extract_derived_tables(sql_expr)
        
        # 日志记录发现的临时表
        if self.temp_tables:
            logger.info(f"发现临时表: {list(self.temp_tables.keys())}")
            for name, info in self.temp_tables.items():
                logger.info(f"临时表 {name} - 列: {info['columns']}, 源表: {info['source_tables']}")
    
    def _extract_cte_definitions(self, sql_expr: exp.Expression) -> None:
        """提取WITH子句中的CTE定义"""
        # 查找WITH表达式
        with_expressions = sql_expr.find_all(exp.With)
        
        for with_expr in with_expressions:
            # 处理WITH子句中的每个CTE
            for cte in with_expr.expressions:
                if isinstance(cte, exp.CTE):
                    cte_name = str(cte.alias).replace('`', '').replace('"', '')
                    
                    # 存储CTE定义
                    self.cte_definitions[cte_name] = cte
                    
                    # 解析CTE中的列和源表
                    columns = self._extract_columns_from_subquery(cte.this)
                    source_tables = self._extract_source_tables_from_subquery(cte.this)
                    
                    # 存储临时表信息
                    self.temp_tables[cte_name] = {
                        'columns': columns,
                        'source_tables': source_tables,
                        'type': 'cte'
                    }
                    
    def _extract_derived_tables(self, sql_expr: exp.Expression) -> None:
        """提取查询中的派生表（子查询作为表）"""
        # 查找SELECT语句中的FROM子句
        for select_expr in sql_expr.find_all(exp.Select):
            if hasattr(select_expr, 'args') and 'from' in select_expr.args:
                from_clause = select_expr.args['from']
                
                # 处理FROM子句中的每个表引用
                if from_clause:
                    self._process_from_item(from_clause)
        
        # 处理JOIN子句
        for join in sql_expr.find_all(exp.Join):
            if isinstance(join.this, exp.Subquery) and join.this.alias:
                self._process_derived_table(join.this)
    
    def _process_from_item(self, from_item):
        """递归处理FROM子句项"""
        if isinstance(from_item, exp.Subquery) and from_item.alias:
            # 处理子查询形式的派生表
            self._process_derived_table(from_item)
        elif isinstance(from_item, exp.From):
            # 处理FROM子句中的表达式
            for expr in from_item.expressions:
                if isinstance(expr, exp.Subquery) and expr.alias:
                    self._process_derived_table(expr)
                
    def _process_derived_table(self, subquery: exp.Subquery) -> None:
        """处理单个派生表"""
        if not subquery.alias:
            return
            
        derived_table_name = str(subquery.alias).replace('`', '').replace('"', '')
        
        # 解析派生表中的列和源表
        columns = self._extract_columns_from_subquery(subquery)
        source_tables = self._extract_source_tables_from_subquery(subquery)
        
        # 存储临时表信息
        self.temp_tables[derived_table_name] = {
            'columns': columns,
            'source_tables': source_tables,
            'type': 'derived_table',
            'definition': subquery
        }
    
    def _extract_columns_from_subquery(self, subquery: exp.Expression) -> List[str]:
        """从子查询中提取列名"""
        columns = []
        
        # 获取SELECT子句的表达式
        if isinstance(subquery, exp.Subquery) and 'this' in subquery.args:
            select_stmt = subquery.args['this']
            if isinstance(select_stmt, exp.Select) and 'expressions' in select_stmt.args:
                select_expressions = select_stmt.args['expressions']
                
                # 处理每个SELECT表达式
                for expr in select_expressions:
                    # 处理列别名
                    if expr.alias:
                        columns.append(str(expr.alias).replace('`', '').replace('"', ''))
                    # 处理直接列引用
                    elif isinstance(expr, exp.Column):
                        columns.append(str(expr.name).replace('`', '').replace('"', ''))
                    # 处理表达式，如聚合函数等
                    else:
                        # 尝试找到表达式名称
                        if hasattr(expr, 'name'):
                            columns.append(str(expr.name).replace('`', '').replace('"', ''))
                        elif hasattr(expr, 'this') and hasattr(expr.this, 'name'):
                            columns.append(str(expr.this.name).replace('`', '').replace('"', ''))
                        # 没有明确名称，使用占位符
                        else:
                            columns.append(f"column_{len(columns)}")
        
        return columns
    
    def _extract_source_tables_from_subquery(self, subquery: exp.Expression) -> List[str]:
        """从子查询中提取源表名"""
        source_tables = []
        
        # 查找子查询中引用的表
        if isinstance(subquery, exp.Subquery) and 'this' in subquery.args:
            select_stmt = subquery.args['this']
            if isinstance(select_stmt, exp.Select):
                # 处理FROM子句
                if 'from' in select_stmt.args and select_stmt.args['from']:
                    from_tables = select_stmt.find_all(exp.Table)
                    for table in from_tables:
                        table_name = str(table.name).replace('`', '').replace('"', '')
                        if table_name not in source_tables:
                            source_tables.append(table_name)
                
                # 处理子查询中引用的临时表
                from_subqueries = select_stmt.find_all(exp.Subquery)
                for sub in from_subqueries:
                    if sub.alias:
                        sub_name = str(sub.alias).replace('`', '').replace('"', '')
                        if sub_name in self.temp_tables and sub_name not in source_tables:
                            source_tables.append(sub_name)
                
                # 处理CTE引用
                if 'from' in select_stmt.args:
                    from_clause = select_stmt.args['from']
                    if isinstance(from_clause, exp.From):
                        for expr in from_clause.expressions:
                            if isinstance(expr, exp.Table):
                                table_name = str(expr.name).replace('`', '').replace('"', '')
                                if table_name in self.cte_definitions and table_name not in source_tables:
                                    source_tables.append(table_name)
        
        return source_tables
    
    def get_temp_table_columns(self, table_name: str) -> List[str]:
        """
        获取临时表的列名列表
        
        Args:
            table_name: 临时表名
            
        Returns:
            列名列表，如果表不存在则返回空列表
        """
        if table_name in self.temp_tables:
            return self.temp_tables[table_name]['columns']
        return []
    
    def get_all_temp_tables(self) -> List[str]:
        """
        获取所有临时表名
        
        Returns:
            临时表名列表
        """
        return list(self.temp_tables.keys())
    
    def get_source_tables(self, table_name: str) -> List[str]:
        """
        获取临时表的源表列表
        
        Args:
            table_name: 临时表名
            
        Returns:
            源表名列表，如果表不存在则返回空列表
        """
        if table_name in self.temp_tables:
            return self.temp_tables[table_name]['source_tables']
        return []
    
    def is_temp_table(self, table_name: str) -> bool:
        """
        检查表是否是临时表
        
        Args:
            table_name: 表名
            
        Returns:
            如果是临时表则返回True，否则返回False
        """
        return table_name in self.temp_tables 