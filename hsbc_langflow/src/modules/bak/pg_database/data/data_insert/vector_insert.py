import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))
from database_utils.pg_database.data.insert_data import excel_to_vector_db
from database_utils.pg_database.pgvector.pgvector_class import PgVectorClass

if __name__ == "__main__":
    pg_client = PgVectorClass(db_name='postgres', host='**************',
                              port=30146, user='pgvector', password='pgvector')

    excel_file_path = r'/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/sql_data_demo.csv'  # 替换为您的 Excel 文件路径

    # 定义表结构（如果需要创建表）
    table_create_fields = [
        {"name": "id", "dtype": "varchar(10)", "is_primary_key": True, "comment": "主键"},
        {"name": "user_question", "dtype": "varchar(2000)", "comment": "用户问题"},
        {"name": "knowledge_evidence", "dtype": "text", "comment": "知识"},
        {"name": "sql", "dtype": "varchar(2000)", "comment": "sql答案"},
        {"name": "embedding_user_question", "dtype": "vector(768)", "comment": "用户问题embedding"}
    ]

    # 定义插入映射
    insert_dict = [
        {'source_id': 'user_question', 'type': 'mapping', 'value': '', 'table_id': 'user_question'},
        {'source_id': 'knowledge_evidence', 'type': 'mapping', 'value': '', 'table_id': 'knowledge_evidence'},
        {'source_id': 'sql', 'type': 'mapping', 'value': '', 'table_id': 'sql'},
        {'source_id': 'user_question', 'type': 'embedding', 'value': '', 'table_id': 'embedding_user_question'},
        {'source_id': '', 'type': 'auto', 'value': '', 'table_id': 'id'}
    ]

    # 调用函数，创建新表
    excel_to_vector_db(
        pg_client,
        excel_file_path,
        table_name="sql_demo",
        table_desc="用户问题表",
        insert_dict=insert_dict,
        create_table_fields=table_create_fields,
        batch_size=50
    )

    # catalog
    excel_file_path = r'/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/测试表结构.csv'  # 替换为您的 Excel 文件路径

    # 定义表结构（如果需要创建表）
    cata_log_fields = [
        {"name": "id", "dtype": "varchar(10)", "is_primary_key": True, "comment": "主键"},
        {"name": "table_name", "dtype": "varchar(255)", "comment": "表名"},
        {"name": "column_name", "dtype": "varchar(255)", "comment": "字段名"},
        {"name": "original_column_name", "dtype": "varchar(255)", "comment": "原始/修正字段名"},
        {"name": "column_description", "dtype": "varchar(255)", "comment": "字段解释"},
        {"name": "value_description", "dtype": "text", "comment": "值解释"},
        {"name": "embedding_column_name", "dtype": "vector(768)", "comment": "字段名embedding"},
        {"name": "embedding_column_description", "dtype": "vector(768)", "comment": "字段解释embedding"},
        {"name": "embedding_value_description", "dtype": "vector(768)", "comment": "值解释embedding"},
    ]

    # 定义插入映射
    insert_dict = [
        {'source_id': 'table_name', 'type': 'mapping', 'value': '', 'table_id': 'table_name'},
        {'source_id': 'column_name', 'type': 'mapping', 'value': '', 'table_id': 'column_name'},
        {'source_id': 'original_column_name', 'type': 'mapping', 'value': '', 'table_id': 'original_column_name'},
        {'source_id': 'column_description', 'type': 'mapping', 'value': '', 'table_id': 'column_description'},
        {'source_id': 'value_description', 'type': 'mapping', 'value': '', 'table_id': 'value_description'},
        {'source_id': 'column_name', 'type': 'embedding', 'value': '', 'table_id': 'embedding_column_name'},
        {'source_id': 'column_description', 'type': 'embedding', 'value': '',
         'table_id': 'embedding_column_description'},
        {'source_id': 'value_description', 'type': 'embedding', 'value': '', 'table_id': 'embedding_value_description'},
        {'source_id': '', 'type': 'auto', 'value': '', 'table_id': 'id'}
    ]

    # 调用函数，创建新表
    excel_to_vector_db(
        pg_client,
        excel_file_path,
        table_name="cata_log",
        table_desc="数据结构表",
        insert_dict=insert_dict,
        create_table_fields=cata_log_fields,
        batch_size=50
    )

    # condition
    excel_file_path = r'/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/condition_data.csv'  # 替换为您的 Excel 文件路径

    # 定义表结构（如果需要创建表）
    table_create_fields = [
        {"name": "id", "dtype": "varchar(10)", "is_primary_key": True, "comment": "主键"},
        {"name": "value_name", "dtype": "varchar(2000)", "comment": "value条件值"},
        {"name": "value_description", "dtype": "text", "comment": "value条件解释"},
        {"name": "column_name", "dtype": "varchar(200)", "comment": "所属字段"},
        {"name": "table_name", "dtype": "varchar(200)", "comment": "所属表"},
        {"name": "embedding_value_name", "dtype": "vector(768)", "comment": "value条件值embedding"},
        {"name": "embedding_value_description", "dtype": "vector(768)", "comment": "value条件解释embedding"}
    ]

    # 定义插入映射
    insert_dict = [
        {'source_id': 'description', 'type': 'mapping', 'value': '', 'table_id': 'value_description'},
        {'source_id': 'table', 'type': 'mapping', 'value': '', 'table_id': 'table_name'},
        {'source_id': 'column', 'type': 'mapping', 'value': '', 'table_id': 'column_name'},
        {'source_id': 'description', 'type': 'embedding', 'value': '', 'table_id': 'embedding_value_description'},
        {'source_id': 'value', 'type': 'mapping', 'value': '', 'table_id': 'value_name'},
        {'source_id': 'value', 'type': 'embedding', 'value': '', 'table_id': 'embedding_value_name'},
        {'source_id': '', 'type': 'auto', 'value': '', 'table_id': 'id'}
    ]

    # 调用函数，创建新表
    excel_to_vector_db(
        pg_client,
        excel_file_path,
        table_name="condition",
        table_desc="条件表",
        insert_dict=insert_dict,
        create_table_fields=table_create_fields,
        batch_size=50
    )
