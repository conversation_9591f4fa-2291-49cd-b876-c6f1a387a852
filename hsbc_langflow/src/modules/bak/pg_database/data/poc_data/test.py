import pandas as pd
from typing import List, Dict


def dict_list_to_excel(data: List[Dict],
                       output_file: str,
                       sheet_name: str = 'Sheet1',
                       index: bool = False):
    """
    将字典列表写入 Excel 文件。

    参数:
        data (List[Dict]): 要写入的字典列表
        output_file (str): 输出的 Excel 文件路径（支持 .xlsx, .xls, .csv）
        sheet_name (str): Excel 工作表名称，默认为 'Sheet1'
        index (bool): 是否写入行索引，默认为 False
    """
    # 检查输入数据是否为空
    if not data:
        print("错误：输入的字典列表为空")
        return

    try:
        # 将字典列表转换为 DataFrame
        df = pd.DataFrame(data)

        # 根据文件扩展名选择写入方式
        if output_file.endswith('.csv'):
            df.to_csv(output_file, encoding='utf-8', index=index)
            print(f"成功将数据写入 CSV 文件：{output_file}")
        elif output_file.endswith('.xlsx'):
            df.to_excel(output_file, sheet_name=sheet_name, engine='openpyxl', index=index)
            print(f"成功将数据写入 Excel 文件：{output_file}，工作表：{sheet_name}")
        elif output_file.endswith('.xls'):
            df.to_excel(output_file, sheet_name=sheet_name, engine='xlwt', index=index)
            print(f"成功将数据写入 Excel 文件：{output_file}，工作表：{sheet_name}")
        else:
            print(f"错误：不支持的文件格式 {output_file}")
            return

    except Exception as e:
        print(f"写入文件时出错：{str(e)}")
if __name__ == "__main__":
    from condition_kb import *

    # 输出文件路径
    output_path = r'C:\Users\<USER>\Desktop\项目\汇丰\condition_data.xlsx'

    # 调用函数写入 Excel
    dict_list_to_excel(
        data=condition_knowledge_base,
        output_file=output_path,
        sheet_name='condition',
        index=False
    )