'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 6:10:36 am
'''

'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 6:09:34 am
'''

import pandas as pd
from typing import List, Dict
from database_utils.pg_database.llm.llm_chat import *
from database_utils.pg_database.pgvector.pgvector_class import *


def excel_to_vector_db(vector_db,
                       file_path: str,
                       table_name: str,
                       insert_dict: List[Dict],
                       table_desc=None,
                       sheet_name=0,
                       fields=None,
                       create_table_fields=None,
                       batch_size=50):
    """
    从Excel文件中读取数据，根据insert_dict映射生成实体并分批插入到向量数据库。

    参数:
        file_path (str): Excel文件路径（支持 .xlsx, .xls, .csv 等）
        table_name (str): 数据库表名（必填）
        insert_dict (List[Dict]): 字段映射规则列表
        sheet_name (str or int): 要读取的工作表名称或索引，默认为 0
        fields (list): 要提取的字段列表，如果为 None，则提取所有列
        create_table_fields (list): 建表字段定义，如果提供则创建新表，否则仅设置现有表
        batch_size (int): 每批次插入的记录数，默认为 50
    """

    # 根据 create_table_fields 判断使用哪个方法
    if create_table_fields:
        vector_db.create_and_set_table(table_name, create_table_fields, description=table_desc)
    else:
        vector_db.set_table(table_name)

    # 读取文件
    try:
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path, encoding='utf-8')
        elif file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
        elif file_path.endswith('.xls'):
            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
        else:
            print(f"错误：不支持的文件格式 {file_path}")
            return
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 未找到")
        return
    except Exception as e:
        print(f"读取文件时出错：{str(e)}")
        return

    # 处理字段选择
    if fields is None:
        fields = df.columns.tolist()
    else:
        missing_fields = [f for f in fields if f not in df.columns]
        if missing_fields:
            print(f"警告：以下字段在文件中不存在，将被忽略：{missing_fields}")
            fields = [f for f in fields if f in df.columns]

    # 处理数据框
    df = df[fields].fillna('')
    data = df.to_dict(orient='records')

    # 生成实体并分批插入
    entities = []
    total_inserted = 0

    for i, item in enumerate(data, start=1):
        entity = {}

        # 根据 insert_dict 映射字段
        for mapping in insert_dict:
            source_id = mapping.get('source_id', '')
            mapping_type = mapping.get('type', '')
            value = mapping.get('value', '')
            table_id = mapping.get('table_id', '')

            if mapping_type == 'mapping':  # 直接映射
                entity[table_id] = item.get(source_id, '')
            elif mapping_type == 'fixed':  # 固定值
                entity[table_id] = value
            elif mapping_type == 'embedding':  # 向量化
                try:
                    source_value = item.get(source_id, '') if source_id else ''
                    entity[table_id] = get_embeddings(source_value)["data"][0]['embedding']
                except Exception as e:
                    print(f"生成嵌入向量时出错（记录 {i}，字段 {table_id}）：{str(e)}")
                    entity[table_id] = None  # 如果出错，设为 None
            elif mapping_type == 'auto':  # 自增类型
                entity[table_id] = str(i)  # 使用记录编号作为自增值

        # 添加到实体列表
        entities.append(entity)

        # 当达到批次大小时执行插入
        if len(entities) >= batch_size:
            try:
                insert_flag=vector_db.insert(entities)
                total_inserted += len(entities)
                # 如果需要将进度写入到内存数据，在这里添加代码
                #TODO
                if insert_flag:
                    print(f"已插入 {total_inserted} 条记录到数据库表 {table_name}")
                else:
                    print(f"插入数据库时出错（批次 {total_inserted + 1} - {total_inserted + len(entities)}）：{str(e)}")
                entities = []  # 清空当前批次
            except Exception as e:
                print(f"插入数据库时出错（批次 {total_inserted + 1} - {total_inserted + len(entities)}）：{str(e)}")
                return

    # 处理剩余的记录（如果有）
    if entities:
        try:
            vector_db.insert(entities)
            total_inserted += len(entities)
            print(f"已插入剩余 {len(entities)} 条记录，总计 {total_inserted} 条记录到数据库表 {table_name}")
        except Exception as e:
            print(f"插入剩余记录时出错：{str(e)}")
            return

    print(f"完成！总共插入 {total_inserted} 条记录到数据库表 {table_name}")


