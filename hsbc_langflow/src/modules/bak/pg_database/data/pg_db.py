import pandas as pd
import os
import psycopg2
from psycopg2.extras import execute_values
from typing import List, Dict, Optional


def create_database(host: str = 'localhost', port: int = 5432,
                   user: str = 'postgres', password: str = 'password',
                   db_name: str = 'adm') -> bool:
    """创建PostgreSQL数据库"""
    try:
        # 连接到默认的postgres数据库
        conn = psycopg2.connect(dbname='postgres', host=host, port=port, user=user, password=password)
        conn.autocommit = True  # 创建数据库需要自动提交模式
        
        with conn.cursor() as cur:
            # 检查数据库是否已存在
            cur.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
            if cur.fetchone():
                print(f"数据库 {db_name} 已存在")
                conn.close()
                return True
            
            # 创建新数据库
            cur.execute(f"CREATE DATABASE {db_name}")
            print(f"数据库 {db_name} 创建成功")
            conn.close()
            return True
    except Exception as e:
        print(f"创建数据库时出错: {str(e)}")
        return False


def connect_postgres(db_name: str = 'adm', host: str = 'localhost', port: int = 5432,
                     user: str = 'postgres', password: str = 'password') -> psycopg2.extensions.connection:
    """连接到PostgreSQL数据库"""
    print(f"正在连接到PostgreSQL数据库 {db_name}...")
    conn = psycopg2.connect(dbname=db_name, host=host, port=port, user=user, password=password)
    print(f"成功连接到PostgreSQL数据库 {db_name}")
    return conn


def has_table(conn: psycopg2.extensions.connection, table_name: str) -> bool:
    """检查表是否存在"""
    with conn.cursor() as cur:
        cur.execute("SELECT EXISTS (SELECT FROM pg_tables WHERE tablename = %s)", (table_name,))
        return cur.fetchone()[0]


def create_table(conn: psycopg2.extensions.connection, table_name: str, fields: List[Dict], description: str = '') -> bool:
    """创建普通PostgreSQL表"""
    if has_table(conn, table_name):
        print(f"创建表失败。表 <{table_name}> 已存在。")
        return False
    
    # 构造列定义并收集主键字段
    columns = []
    column_comments = []
    primary_key_fields = []
    
    for field in fields:
        name = field["name"]
        dtype = field["dtype"]
        col_def = f"{name} {dtype}"
        
        if field.get("is_primary_key", False):
            primary_key_fields.append(name)
        
        if field.get("label") == "not null":
            col_def += " NOT NULL"
            
        columns.append(col_def)
        
        if "comment" in field:
            column_comments.append((table_name, name, field["comment"]))
    
    # 如果有主键字段，添加主键约束
    if primary_key_fields:
        columns.append(f"PRIMARY KEY ({', '.join(primary_key_fields)})")
    
    # 构造CREATE TABLE SQL
    sql = f"CREATE TABLE {table_name} ({', '.join(columns)})"
    print(sql)
    
    with conn.cursor() as cur:
        cur.execute(sql)
        conn.commit()
        
        # 添加列注释
        for table, col, comment in column_comments:
            cur.execute(f"COMMENT ON COLUMN {table}.{col} IS %s", (comment,))
        conn.commit()
        
        # 添加表注释
        if description:
            cur.execute(f"COMMENT ON TABLE {table_name} IS %s", (description,))
            conn.commit()
    
    print(f"表 <{table_name}> 创建成功。")
    return True


def insert_data(conn: psycopg2.extensions.connection, table_name: str, entities: List[Dict]) -> bool:
    """向表中插入数据"""
    if not entities:
        print("插入失败。实体列表为空。")
        return False
    
    # 插入数据
    columns = list(entities[0].keys())
    values = []
    
    for entity in entities:
        row = [entity.get(col) for col in columns]
        values.append(row)
    
    sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES %s"
    
    try:
        with conn.cursor() as cur:
            execute_values(cur, sql, values)
            conn.commit()
        print(f"成功插入 {len(entities)} 条记录到表 {table_name}")
        return True
    except Exception as e:
        print(f"插入失败: {str(e)}")
        conn.rollback()
        return False


class PgSqlClient:
    """管理PostgreSQL数据库操作的通用客户端类"""
    
    def __init__(self, db_name: str = "adm", host: str = "**************",
                 port: int = 30146, user: str = "pgvector", password: str = "pgvector",
                 auto_create_db: bool = False):
        """初始化数据库连接参数"""
        self.db_name = db_name
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.table_name = None
        self.conn = None
        
        # 如果需要自动创建数据库
        if auto_create_db:
            created = create_database(host, port, user, password, db_name)
            if not created:
                print(f"警告：无法创建数据库 {db_name}，但仍将尝试连接")
        
        self._connect()
    
    def _connect(self) -> None:
        """建立PostgreSQL连接并处理错误"""
        try:
            self.conn = connect_postgres(self.db_name, self.host, self.port, self.user, self.password)
        except Exception as e:
            raise ConnectionError(f"无法连接到PostgreSQL: {str(e)}")
    
    def disconnect(self) -> bool:
        """断开PostgreSQL连接"""
        if self.conn:
            try:
                self.conn.close()
                self.conn = None
                print("已断开与PostgreSQL的连接")
                return True
            except Exception as e:
                print(f"断开连接时出错: {str(e)}")
                return False
        return True
    
    def set_table(self, table_name: str) -> bool:
        """设置当前表名"""
        if not table_name or not isinstance(table_name, str):
            raise ValueError("表名必须是非空字符串")
        self.table_name = table_name
        return True
    
    def create_and_set_table(self, table_name: str, fields_list: List[Dict], description: str = "") -> bool:
        """创建表并将其设置为当前表"""
        if not self.conn:
            self._connect()
        self.set_table(table_name)
        return create_table(self.conn, table_name, fields_list, description)
    
    def insert(self, entities: List[Dict]) -> bool:
        """向表中插入数据"""
        if not self._ensure_connection_and_table():
            return False
        return insert_data(self.conn, self.table_name, entities)
    
    def _ensure_connection_and_table(self) -> bool:
        """确保连接有效且表已设置"""
        if not self.conn:
            self._connect()
        if not self.table_name:
            print("未设置表，请先设置或创建表")
            return False
        return True
    
    def __del__(self):
        """析构函数，确保断开连接"""
        self.disconnect()


def excel_to_postgres(pg_client,
                     file_path: str,
                     table_name: str = None,
                     mapping_dict: List[Dict] = None,
                     table_desc: str = None,
                     sheet_name=0,
                     fields: List[str] = None,
                     create_table_fields: List[Dict] = None,
                     batch_size: int = 50,
                     use_filename_as_tablename: bool = True,
                     auto_create_table: bool = True,
                     auto_detect_leading_zero: bool = True):
    """
    从Excel或CSV文件读取数据，根据mapping_dict映射生成实体并分批插入到PostgreSQL数据库。
    
    参数:
        pg_client: PostgreSQL客户端实例
        file_path (str): Excel/CSV文件路径
        table_name (str): 数据库表名，如果为None且use_filename_as_tablename为True，则使用文件名作为表名
        mapping_dict (List[Dict]): 字段映射规则列表
        table_desc (str): 表描述
        sheet_name (str or int): 要读取的工作表名称或索引，默认为0
        fields (list): 要提取的字段列表，如果为None，则提取所有列
        create_table_fields (list): 建表字段定义，如果提供则创建新表，否则仅设置现有表
        batch_size (int): 每批次插入的记录数，默认为50
        use_filename_as_tablename (bool): 是否使用文件名作为表名，默认为True
        auto_create_table (bool): 是否根据数据自动创建表，默认为True
        auto_detect_leading_zero (bool): 是否自动检测具有前导零的列，默认为True
    """
    # 如果表名为None且use_filename_as_tablename为True，则使用文件名作为表名
    if table_name is None and use_filename_as_tablename:
        file_name = os.path.basename(file_path)
        table_name = os.path.splitext(file_name)[0].lower()
        print(f"使用文件名 {table_name} 作为表名")
    
    # 先尝试以所有列都为字符串的方式读取文件，以便检测前导零
    try:
        if file_path.endswith('.csv'):
            # 先将所有列都作为字符串读取，便于检测前导零
            df_str = pd.read_csv(file_path, encoding='utf-8', dtype=str)
        elif file_path.endswith('.xlsx'):
            df_str = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl', dtype=str)
        elif file_path.endswith('.xls'):
            df_str = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd', dtype=str)
        else:
            print(f"错误：不支持的文件格式 {file_path}")
            return
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 未找到")
        return
    except Exception as e:
        print(f"读取文件时出错：{str(e)}")
        return
    
    # 自动检测具有前导零的列
    leading_zero_columns = []
    if auto_detect_leading_zero:
        for col in df_str.columns:
            # 检查该列是否含有以0开头且长度大于1的数字字符串
            # 先过滤掉非数字和空值
            numeric_values = df_str[col].dropna().str.match(r'^\d+$')
            if numeric_values.any():
                # 再检查是否有以0开头的数字
                has_leading_zero = df_str[col].dropna().str.match(r'^0\d+$').any()
                if has_leading_zero:
                    leading_zero_columns.append(col)
                    print(f"自动检测到列 '{col}' 包含前导零数字，将作为字符串处理")
    
    # 现在重新读取文件，对检测到的前导零列使用字符串类型
    try:
        if file_path.endswith('.csv'):
            # 对检测到的前导零列使用字符串类型
            if leading_zero_columns:
                dtype_dict = {col: str for col in leading_zero_columns}
                df = pd.read_csv(file_path, encoding='utf-8', dtype=dtype_dict)
            else:
                df = pd.read_csv(file_path, encoding='utf-8')
        elif file_path.endswith('.xlsx'):
            # Excel读取时需要使用converters来指定列的转换函数
            if leading_zero_columns:
                converters = {col: str for col in leading_zero_columns}
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl', converters=converters)
            else:
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
        elif file_path.endswith('.xls'):
            if leading_zero_columns:
                converters = {col: str for col in leading_zero_columns}
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd', converters=converters)
            else:
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
    except Exception as e:
        print(f"重新读取文件时出错：{str(e)}")
        return
    
    # 处理字段选择
    if fields is None:
        fields = df.columns.tolist()
    else:
        missing_fields = [f for f in fields if f not in df.columns]
        if missing_fields:
            print(f"警告：以下字段在文件中不存在，将被忽略：{missing_fields}")
            fields = [f for f in fields if f in df.columns]
    
    # 预处理数据：将数值列(不包括前导零列)中的空字符串和其他非数值字符串转换为None
    for col in df.columns:
        if col not in leading_zero_columns and pd.api.types.is_numeric_dtype(df[col].dtype):
            # 对于数值型列，将空字符串转换为NaN
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 如果需要自动创建表且没有提供表结构
    if auto_create_table and not create_table_fields:
        # 根据DataFrame自动生成表结构
        create_table_fields = []
        dtypes = df.dtypes
        
        for column in fields:
            field = {"name": column, "comment": f"自动生成的{column}字段"}
            
            # 根据pandas的数据类型确定SQL类型，对于前导零列使用varchar
            if column in leading_zero_columns:
                max_len = df[column].astype(str).str.len().max()
                safe_len = max(50, int(max_len * 1.5))  # 添加50%的余量，至少为50
                field["dtype"] = f"varchar({safe_len})"
            else:
                pd_type = dtypes[column]
                if pd.api.types.is_integer_dtype(pd_type):
                    field["dtype"] = "integer"
                elif pd.api.types.is_float_dtype(pd_type):
                    field["dtype"] = "float"
                elif pd.api.types.is_datetime64_any_dtype(pd_type):
                    field["dtype"] = "timestamp"
                elif pd.api.types.is_bool_dtype(pd_type):
                    field["dtype"] = "boolean"
                else:
                    # 如果是字符串类型，找出最长的字符串长度再加上一些余量
                    max_len = df[column].astype(str).str.len().max()
                    safe_len = max(255, int(max_len * 1.5))  # 添加50%的余量，至少为255
                    field["dtype"] = f"varchar({safe_len})"
            
            create_table_fields.append(field)
        
        print(f"根据数据自动生成表结构：")
        for field in create_table_fields:
            print(f"  {field['name']}: {field['dtype']}")
    
    # 根据create_table_fields判断使用哪个方法
    if create_table_fields:
        try:
            pg_client.create_and_set_table(table_name, create_table_fields, description=table_desc)
        except Exception as e:
            print(f"创建表失败: {str(e)}")
            if "already exists" not in str(e):  # 如果错误不是表已存在，则退出
                return
            # 如果表已存在，继续执行
            pg_client.set_table(table_name)
    else:
        pg_client.set_table(table_name)
    
    # 处理数据框 - 用None替代空值而不是空字符串
    df = df[fields].replace('', None)
    data = df.where(pd.notna(df), None).to_dict(orient='records')
    
    # 生成实体并分批插入
    entities = []
    total_inserted = 0
    
    for i, item in enumerate(data, start=1):
        entity = {}
        
        # 根据mapping_dict映射字段
        if mapping_dict:
            for mapping in mapping_dict:
                source_id = mapping.get('source_id', '')
                mapping_type = mapping.get('type', '')
                value = mapping.get('value', '')
                table_id = mapping.get('table_id', '')
                
                if mapping_type == 'mapping':  # 直接映射
                    entity[table_id] = item.get(source_id, None)
                elif mapping_type == 'fixed':  # 固定值
                    entity[table_id] = value
                elif mapping_type == 'auto':  # 自增类型
                    entity[table_id] = str(i)  # 使用记录编号作为自增值
        else:
            # 如果没有提供映射，直接使用原始数据
            entity = item
        
        # 添加到实体列表
        entities.append(entity)
        
        # 当达到批次大小时执行插入
        if len(entities) >= batch_size:
            try:
                insert_flag = pg_client.insert(entities)
                total_inserted += len(entities)
                
                if insert_flag:
                    print(f"已插入 {total_inserted} 条记录到数据库表 {table_name}")
                else:
                    print(f"插入数据库时出错（批次 {total_inserted + 1} - {total_inserted + len(entities)}）")
                entities = []  # 清空当前批次
            except Exception as e:
                print(f"插入数据库时出错（批次 {total_inserted + 1} - {total_inserted + len(entities)}）：{str(e)}")
                return
    
    # 处理剩余的记录（如果有）
    if entities:
        try:
            pg_client.insert(entities)
            total_inserted += len(entities)
            print(f"已插入剩余 {len(entities)} 条记录，总计 {total_inserted} 条记录到数据库表 {table_name}")
        except Exception as e:
            print(f"插入剩余记录时出错：{str(e)}")
            return
    
    print(f"完成！总共插入 {total_inserted} 条记录到数据库表 {table_name}")


# 使用示例
if __name__ == "__main__":
    # 创建PostgreSQL客户端，设置auto_create_db=True自动创建数据库
    pg_client = PgSqlClient(db_name='adm', host='**************',
                           port=30146, user='pgvector', password='pgvector',
                           auto_create_db=True)
    
    # 示例：从一个目录中读取所有CSV文件并导入
    import glob
    csv_files = glob.glob("/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/ADM_LON_VAROIUS.csv")
    
    for csv_file in csv_files:
        # 自动使用文件名作为表名，并自动创建表，自动检测前导零
        excel_to_postgres(
            pg_client,
            file_path=csv_file,
            table_desc="ADM层数据表",
            use_filename_as_tablename=True,
            batch_size=100,
            auto_create_table=True,
            auto_detect_leading_zero=True  # 启用自动检测前导零
        )