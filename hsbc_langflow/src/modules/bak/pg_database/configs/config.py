pgvector_config = {
    "db_name": 'postgres',
    "host": '**************',
    "port": 30146,
    "user": 'pgvector',
    "password": 'pgvector',
    "search_schema": {
        "vector_field": "embedding_column_name",
        "limit": 3,
        "metric_type": "cosine",
        "output_fields": ["id", "partition_key"],
        "expr": "",
        "partition_name": ""
    },
    "query_schema":{
        "limit": 3,
        "expr": "",
        "partition_name":"",
        "output_fields": ["id", "partition_key"]
    }
}

mysql_config = {
    'host': '**************',
    'port': 37615,
    'user': 'root',
    'password': 'idea@1008',
    'database': 'hsbc_data'
}