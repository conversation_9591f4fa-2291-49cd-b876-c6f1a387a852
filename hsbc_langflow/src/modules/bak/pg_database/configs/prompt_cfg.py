"""
@FileName: prompt_cfg.py
@Description:
@Version: 0.1
@Time: 2023/8/30 14:35 
@Copyright: 2022-2023
"""
import os
# prompt_templete_sales='我们来做一个角色扮演游戏，你现在扮演一个资深的金融行业营销专家，我们需要你来协助输出一些营销方案，具体需求如下:' \
#                       '{content}'

query_template = '''
你是一名专业的问题总结专家，请你根据下面的对话内容与新的问题，总结出一个问题，并确保这个问题是基于上下文的,同时可以将上下文对话中有用的信息放到这个新问题上。
这是历史上下文对话：
{content}
这是新的问题：
{question}

输出只要新问题

格式要求: 《新问题》：
'''

query_template_forword = '''
请你根据相关的文档与问题中的详细信息，给出最专业的回答，回答一定要保证自己有依据，如果不懂，可以直接回答“我无法回答这个问题”。
这是相关的参考文档：
{docs}
这是用户的提问:
{question}

如果你认为这个问题不需要参考文档的内容，可以根据自己的能力来回答该问题。
'''

#comment code prompt
prompt_template_comment_code = '你是一名精通{language}的资深程序员,我给你一份代码,我需要你给这份代码的所有内容做注释.\n代码如下:'\
                                '{code}'

#describe code prompt
prompt_template_describe_code = '你是一名精通{language}的资深程序员,我给你一份代码,我需要你告诉我这份代码实现了多少个类和多少个函数,并且解释这些类和函数的作用,总结代码内容.\n代码如下:\n'\
                                '{code}'

#get questions for kb text
prompt_template_q="""现有文本:
{content}
请针对上述文本给出近似的内容，要求用中文，必须与文本相关，不需要回答任何问题。同时不要使用此文本、该措施等描述，请用完整的称呼，
"""
prompt_template_q_dy="""你是一个资深的客服专家，现有文本:
{content}
请针对上述文本提出{num}个问题，要求用中文，必须与文本相关，不需要回答任何问题。同时不要使用此文本、该措施等描述，请用完整的称呼， 。 
"""
prompt_template_Summarize="""现有文本:
{content}
用简洁的方式总结上述文本，要求保留所有关键信息，保留数字信息。
"""

prompt_template_conten_summarize = """现在有一篇文章，请帮我总结这篇文章的主要内容。尽量保存完整的文章内容，
特别是其中的上下文引用部分，生成一篇1000字的总结，并且保留原文的关键信息。
这是要进行摘要的文章{content}"""

prompt_template_kb = """已知信息：
{context} 

根据上述已知信息，简洁和专业的来回答用户的问题。如果无法从中得到答案，请说 “根据已知信息无法回答该问题” 或 “没有提供足够的相关信息”，不允许在答案中添加编造成分，答案请使用中文。 问题是：{question}"""

# language dict
LANGUAGE_TAG = {
    "c"            : "// language: C",
    "c++"          : "// language: C++",
    "cpp"          : "// language: C++",
    "c#"           : "// language: C#",
    "csharp"       : "// language: C#",
    "c-sharp"      : "// language: C#",
    "css"          : "/* language: CSS */",
    "cuda"         : "// language: Cuda",
    "dart"         : "// language: Dart",
    "lua"          : "// language: Lua",
    "objectivec"   : "// language: Objective-C",
    "objective-c"  : "// language: Objective-C",
    "objective-c++": "// language: Objective-C++",
    "python"       : "# language: Python",
    "perl"         : "# language: Perl",
    "prolog"       : f"% language: Prolog",
    "swift"        : "// language: swift",
    "lisp"         : "; language: Lisp",
    "java"         : "// language: Java",
    "scala"        : "// language: Scala",
    "tex"          : f"% language: TeX",
    "vue"          : "<!--language: Vue-->",
    "markdown"     : "<!--language: Markdown-->",
    "html"         : "<!--language: HTML-->",
    "php"          : "// language: PHP",
    "js"           : "// language: JavaScript",
    "javascript"   : "// language: JavaScript",
    "typescript"   : "// language: TypeScript",
    "go"           : "// language: Go",
    "shell"        : "# language: Shell",
    "rust"         : "// language: Rust",
    "sql"          : "-- language: SQL",
    "kotlin"       : "// language: Kotlin",
    "vb"           : "' language: Visual Basic",
    "ruby"         : "# language: Ruby",
    "pascal"       : "// language: Pascal",
    "r"            : "# language: R",
    "fortran"      : "!language: Fortran",
    "lean"         : "-- language: Lean",
    "matlab"       : f"% language: Matlab",
    "delphi"       : "{language: Delphi}",
    "scheme"       : "; language: Scheme",
    "basic"        : "' language: Basic",
    "assembly"     : "; language: Assembly",
    "groovy"       : "// language: Groovy",
    "abap"         : "* language: Abap",
    "gdscript"     : "# language: GDScript",
    "haskell"      : "-- language: Haskell",
    "julia"        : "# language: Julia",
    "elixir"       : "# language: Elixir",
    "excel"        : "' language: Excel",
    "clojure"      : "; language: Clojure",
    "actionscript" : "// language: ActionScript",
    "solidity"     : "// language: Solidity",
    "powershell"   : "# language: PowerShell",
    "erlang"       : f"% language: Erlang",
    "cobol"        : "// language: Cobol",
    "alloy"        : "/* language: Alloy */",
    "awk"          : "// language: AWK",
    "thrift"       : "/* language: Thrift */",
    "sparql"       : "# language: SPARQL",
    "augeas"       : "// language: Augeas",
    "cmake"        : "# language: CMake",
    "f-sharp"      : "// language: F#",
    "stan"         : "// language: Stan",
    "isabelle"     : "(*language: Isabelle*)",
    "dockerfile"   : "# language: Dockerfile",
    "rmarkdown"    : "# language: RMarkdown",
    "literate-agda": "-- language: Literate Agda",
    "tcl"          : "// language: Augeas",
    "glsl"         : "// language: GLSL",
    "antlr"        : "// language: ANTLR",
    "verilog"      : "// language: Verilog",
    "racket"       : "; language: Racket",
    "standard-ml"  : "(*language:Standard ML*)",
    "elm"          : "-- language: Elm",
    "yaml"         : "# language: YAML",
    "smalltalk"    : "'' language: Smalltalk",
    "ocaml"        : "(*language: OCaml*)",
    "idris"        : "-- language: Idris",
    "visual-basic" : "' language: Visual Basic",
    "protocol-buffer": "// language: Protocol Buffer",
    "bluespec"     : "// language: Bluespec",
    "applescript"  : "-- language: AppleScript",
    "makefile"     : "# language: Makefile",
    "tcsh"         : "# language: TCSH",
    "maple"        : "# language: Maple",
    "systemverilog": "// language: SystemVerilog",
    "literate-coffeescript": "# language: Literate CoffeeScript",
    "vhdl"         : "-- language: VHDL",
    "restructuredtext": ".. language: reStructuredText",
    "sas"          : "* language: SAS",
    "literate-haskell": "> language: Literate Haskell",
    "java-server-pages": "// language: Java Server Pages",
    "coffeescript" : "# language: CoffeeScript",
    "emacs-lisp"   : "; language: Emacs Lisp",
    "mathematica"  : "// language: Mathematica",
    "xslt"         : "<!--language: XSLT-->",
    "zig"          : "// language: Zig",
    "common-lisp"  : "; language: Common Lisp",
    "stata"        : "* language: Stata",
    "agda"         : "-- language: Agda",
    "ada"          : "-- language: Ada",
}


# 知识库中单段文本长度
CHUNK_SIZE = 250

# 知识库中相邻文本重合长度
OVERLAP_SIZE = 50

#SUPPORTED EXTS DICTS
LOADER_DICT = {"UnstructuredHTMLLoader": ['.html'],
               "UnstructuredMarkdownLoader": ['.md'],
               "JSONLoader": [".json"],
               "CSVLoader": [".csv"],
               #"RapidOCRPDFLoader": [".pdf"],
               #"RapidOCRLoader": ['.png', '.jpg', '.jpeg', '.bmp'],
               "UnstructuredFileLoader": ['.eml', '.msg', '.rst',
                                          '.rtf', '.txt', '.xml',
                                          '.doc', '.docx', '.epub', '.odt',
                                          '.ppt', '.pptx', '.tsv'],  # '.xlsx'
               }


SUPPORTED_EXTS = [ext for sublist in LOADER_DICT.values() for ext in sublist]



#NLTK DATA PATH
NLTK_DATA_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "nltk_data")
