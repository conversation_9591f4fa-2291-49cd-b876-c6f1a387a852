'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 6:04:06 am
'''

'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 5:56:23 am
'''

'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 5:56:14 am
'''

import requests
import json
from utils.common.config_util import get_yml_config

embedding_endpoint = get_yml_config()["model"]["embedding"]["endpoint"]

def get_embeddings(input_text, model="embedding", endpoint=embedding_endpoint):
    # 构造请求数据
    payload = {
        "input": [input_text],
        "model": model
    }

    # 发送 POST 请求
    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(endpoint, json=payload, headers=headers)
        response.raise_for_status()  # 检查 HTTP 状态码
        return response.json()  # 返回 JSON 格式的响应
    except requests.RequestException as e:
        raise Exception(f"Failed to get embeddings: {str(e)}")


if __name__ == "__main__":
    input_text = "Hello, world!"
    print(get_embeddings(input_text))


def get_coder_chat(input_text, model="", endpoint="http://218.78.129.173:30164/generate"):
    # TODO
    return None
