import time
import numpy as np
import psycopg2
from psycopg2.extras import execute_values, quote_ident
from typing import List, Dict, Any, Optional, Union


# 连接到 PostgreSQL
def connect_postgres(db_name: str = 'vector_db', host: str = 'localhost', port: int = 5432,
                     user: str = 'postgres', password: str = 'password') -> psycopg2.extensions.connection:
    print("Connecting to PostgreSQL...")
    conn = psycopg2.connect(dbname=db_name, host=host, port=port, user=user, password=password)
    print("Connected to PostgreSQL successfully.")
    return conn


# 断开 PostgreSQL 连接
def disconnect_postgres(conn: psycopg2.extensions.connection) -> bool:
    print("Disconnecting from PostgreSQL...")
    conn.close()
    print("Disconnected from PostgreSQL successfully.")
    return True


# 检查表是否存在
def has_table(conn: psycopg2.extensions.connection, table_name: str) -> bool:
    with conn.cursor() as cur:
        cur.execute("SELECT EXISTS (SELECT FROM pg_tables WHERE tablename = %s)", (table_name,))
        return cur.fetchone()[0]


# 创建表（类似 Milvus 的 create_collection）
def create_table(conn: psycopg2.extensions.connection, table_name: str = None,
                 fields: List[Dict] = None, description: str = '') -> bool:
    if table_name is None:
        print("Create table failed. table_name is None.")
        return False
    if has_table(conn, table_name):
        print(f"Create table failed. <{table_name}> already exists.")
        return False

    # 如果 fields 是 None 或空列表，初始化为空列表
    fields = fields or []

    # 检查是否已有 partition_key，如果没有则添加默认的
    has_partition_key = any(field.get("name") == "partition_key" for field in fields)
    if not has_partition_key:
        fields.append({
            "name": "partition_key",
            "dtype": "varchar(200)",
            "label": "not null",
            "comment": "分区字段"
        })

    # 构造列定义并收集主键字段
    columns = []
    column_comments = []
    primary_key_fields = []
    for field in fields:
        name = field["name"]
        dtype = field["dtype"]
        col_def = f"{name} {dtype}"
        if field.get("is_primary_key", False):
            primary_key_fields.append(name)
        if field.get("label") == "not null":
            col_def += " NOT NULL"
        else:
            if field.get("label"):
                col_def += field.get("label")
        columns.append(col_def)
        if "comment" in field:
            column_comments.append((table_name, name, field["comment"]))

    # 如果没有指定主键字段，默认不加 PRIMARY KEY 约束
    if not primary_key_fields:
        print("Warning: No primary key specified. Table will be created without a primary key.")
    else:
        # 将 partition_key 添加到联合主键中（如果不在列表中）
        if "partition_key" not in primary_key_fields:
            primary_key_fields.append("partition_key")
        # 在列定义后添加联合主键约束
        columns.append(f"PRIMARY KEY ({', '.join(primary_key_fields)})")

    # 构造 CREATE TABLE SQL，始终使用 PARTITION BY RANGE (partition_key)
    sql = f"CREATE TABLE {table_name} ({', '.join(columns)}) PARTITION BY LIST (partition_key)"
    print(sql)
    with conn.cursor() as cur:
        cur.execute(sql)
        conn.commit()

        # 添加列注释
        for table, col, comment in column_comments:
            cur.execute(f"COMMENT ON COLUMN {table}.{col} IS %s", (comment,))
        conn.commit()

        # 添加表注释
        if description:
            cur.execute(f"COMMENT ON TABLE {table_name} IS %s", (description,))
            conn.commit()

    print(f"<{table_name}> is created successfully.")
    return True


# 删除表（类似 drop_collection）
def drop_table(conn: psycopg2.extensions.connection, table_name: str = None) -> bool:
    if table_name is None:
        print("Drop table failed. table_name is None.")
        return False
    if not has_table(conn, table_name):
        print(f"Drop table failed. <{table_name}> does not exist.")
        return False
    with conn.cursor() as cur:
        cur.execute(f"DROP TABLE {table_name}")
        conn.commit()
    print(f"Dropped <{table_name}> successfully.")
    return True


# 创建向量索引（类似 create_index）
def create_index(conn: psycopg2.extensions.connection, table_name: str = None,
                 field_name: str = None, index_params: Dict = None) -> bool:
    if table_name is None or field_name is None or index_params is None:
        print("Construct vector index failed. table_name, field_name, or index_params is None.")
        return False

    index_type = index_params.get("index_type", "HNSW")
    metric_type = index_params.get("metric_type", "L2").lower()
    params = index_params.get("params", {"m": 16, "ef_construction": 64})

    # HNSW 索引语法
    index_name = f"{table_name}_{field_name}_idx"
    metric_op = "vector_l2_ops" if metric_type == "l2" else "vector_cosine_ops"
    sql = f"CREATE INDEX {index_name} ON {table_name} USING hnsw ({field_name} {metric_op}) " \
          f"WITH (m = {params['m']}, ef_construction = {params['ef_construction']})"

    with conn.cursor() as cur:
        cur.execute(sql)
        conn.commit()
    print("Constructed vector index successfully.")
    return True


# 插入数据（类似 insert_entities）
def insert_entities(conn: psycopg2.extensions.connection, table_name: str = None,
                    entities: List[Dict] = None, partition_name: str = "9888") -> bool:
    if table_name is None or not entities:
        print("Insert failed. table_name and entities are necessary.")
        return False

    # 检查表是否存在
    if not has_table(conn, table_name):
        print(f"Insert failed. Table <{table_name}> does not exist.")
        return False

    partition_value = partition_name  # 明确使用 partition_name，类型为 str

    with conn.cursor() as cur:
        # 查询子分区
        cur.execute("""
            SELECT 
                p.relname, 
                pg_get_expr(p.relpartbound, p.oid)
            FROM pg_class t
            JOIN pg_partitioned_table pt ON t.oid = pt.partrelid
            JOIN pg_inherits i ON i.inhparent = t.oid
            JOIN pg_class p ON p.oid = i.inhrelid
            WHERE t.relname = %s 
                AND t.relkind = 'p'
                AND p.relispartition
        """, (table_name,))
        partitions = cur.fetchall()

        # 提取分区值和表名
        existing_partitions = {}
        for partition_name, partbound in partitions:
            if "FOR VALUES IN" in partbound:
                try:
                    # 假设分区值是字符串
                    value = partbound.split("FOR VALUES IN (")[1].strip(")").strip("'")
                    existing_partitions[value] = partition_name
                except (IndexError, ValueError) as e:
                    print(f"Warning: Failed to parse partition boundary '{partbound}': {str(e)}")
                    continue

        # 创建子分区
        expected_partition_name = f"{table_name}_partition_for_{partition_value}"
        if partition_value not in existing_partitions:
            print(f"Creating partition {expected_partition_name} for partition_key={partition_value}...")
            try:
                # 安全转义表名
                safe_partition_name = quote_ident(expected_partition_name, cur)
                safe_table_name = quote_ident(table_name, cur)
                # 构造分区值，确保字符串被正确引用
                sql = f"""
                    CREATE TABLE {safe_partition_name} 
                    PARTITION OF {safe_table_name} 
                    FOR VALUES IN (%s)
                """
                cur.execute(sql, (partition_value,))
                conn.commit()
                print(f"Partition {expected_partition_name} created successfully.")
            except Exception as e:
                print(f"Create partition failed: {str(e)}")
                conn.rollback()
                return False
        elif existing_partitions[partition_value] != expected_partition_name:
            print(f"Error: Partition value {partition_value} exists as {existing_partitions[partition_value]}, "
                  f"expected {expected_partition_name}. Aborting.")
            return False

        # 插入数据
        columns = list(entities[0].keys())
        if "partition_key" not in columns:
            columns.append("partition_key")
        values = []
        for entity in entities:
            row = [entity.get(col) for col in columns if col != "partition_key"]
            row.append(partition_value)
            values.append(row)

        # 使用参数化查询构造插入语句
        columns_str = ", ".join(columns)
        try:
            execute_values(cur, f"INSERT INTO {quote_ident(table_name, cur)} ({columns_str}) VALUES %s", values)
            conn.commit()
            print(
                f"Inserted {len(entities)} entities into {table_name} with partition_key={partition_value} successfully.")
            return True
        except Exception as e:
            print(f"Insert failed: {str(e)}")
            conn.rollback()
            return False


# 删除数据（类似 delete_entities）
def delete_entities(conn: psycopg2.extensions.connection, table_name: str = None,
                    expr: str = None, partition_name: str = None) -> bool:
    if table_name is None or expr is None:
        print("Delete failed. table_name and expr are necessary.")
        return False

    # 检查表是否存在
    if not has_table(conn, table_name):
        print(f"Delete failed. Table <{table_name}> does not exist.")
        return False

    # 构造 WHERE 子句
    where_conditions = []
    params = []
    if expr.strip():  # 确保 expr 非空
        where_conditions.append(expr)
    if partition_name is not None:
        where_conditions.append("partition_key = %s")
        params.append(partition_name)

    if not where_conditions:
        print("Delete failed. At least one condition (expr or partition_name) is required.")
        return False

    # 执行删除
    with conn.cursor() as cur:
        try:
            sql = f"DELETE FROM {table_name} WHERE {' AND '.join(where_conditions)}"
            cur.execute(sql, params)
            row_count = cur.rowcount
            conn.commit()
            print(f"Deleted {row_count} entities from {table_name} successfully.")
            return True
        except Exception as e:
            print(f"Delete failed: {str(e)}")
            conn.rollback()
            return False


def select_entities(conn: psycopg2.extensions.connection, table_name: str = None,
                    output_fields: List[str] = None, expr: str = None,
                    partition_name=None, limit: int = None) -> List[Dict]:
    if table_name is None:
        print("Select failed. table_name is necessary.")
        return []

    # 检查表是否存在
    if not has_table(conn, table_name):
        print(f"Select failed. Table <{table_name}> does not exist.")
        return []

    # 默认返回所有字段
    output_fields = output_fields or ["*"]
    fields_str = ", ".join(output_fields)

    # 构造 WHERE 子句
    where_conditions = []
    params = []
    if expr:
        where_conditions.append(expr)
    if partition_name is not None:
        where_conditions.append("partition_key = %s")
        params.append(partition_name)

    # 构造 SQL 查询
    query = f"SELECT {fields_str} FROM {table_name}"
    if where_conditions:
        query += " WHERE " + " AND ".join(where_conditions)
    if limit is not None:
        query += f" LIMIT {limit}"

    # 执行查询
    results = []
    with conn.cursor() as cur:
        try:
            # print(query)
            cur.execute(query, params)
            column_names = [desc[0] for desc in cur.description]
            for row in cur.fetchall():
                results.append(dict(zip(column_names, row)))
            print(f"Selected {len(results)} entities from {table_name} successfully.")
            return results
        except Exception as e:
            print(f"Select failed: {str(e)}")
            conn.rollback()
            return []


# 搜索向量
def search_entities(
        conn: psycopg2.extensions.connection,
        table_name: str = None,
        data: List[float] = None,
        vector_field: str = "embeddings",
        limit: int = 3,
        metric_type: str = "L2",
        output_fields: List[str] = None,
        expr: str = None,
        partition_name: str = None  # 明确类型为 str
) -> List[Dict]:
    # 输入验证
    if table_name is None or data is None:
        print("Search failed. table_name and data are necessary.")
        return []

    # 检查表是否存在
    if not has_table(conn, table_name):
        print(f"Search failed. Table <{table_name}> does not exist.")
        return []

    # 确定距离运算符
    metric_op = {
        "l2": "<->",
        "cosine": "<=>",
        "hybrid": "<+>"
    }.get(metric_type.lower(), "<->")

    # 默认输出字段
    output_fields = output_fields or ["id"]

    # 构造 WHERE 子句
    where_conditions = []
    params = []
    if expr:
        where_conditions.append(expr)
    if partition_name is not None:
        where_conditions.append("partition_key = %s")
        params.append(partition_name)

    # 构造查询
    query = (
        f"SELECT {', '.join(output_fields)}, "
        f"{vector_field} {metric_op} %s::vector AS distance "
        f"FROM {table_name} "
    )
    if where_conditions:
        query += f"WHERE {' AND '.join(where_conditions)} "
    query += f"ORDER BY distance LIMIT {limit}"

    with conn.cursor() as cur:
        try:
            # 合并向量和分区参数
            all_params = [data] + params
            cur.execute(query, all_params)
            results = [
                {
                    "distance": row[-1],
                    **{field: row[i] for i, field in enumerate(output_fields)}
                }
                for row in cur.fetchall()
            ]
            return results
        except psycopg2.Error as e:
            print(f"Query failed: {str(e)}")
            return []


def delete_partition(conn: psycopg2.extensions.connection, table_name: Optional[str] = None,
                     partition_value: Optional[str] = None) -> bool:
    """
    删除指定表的分区。

    Args:
        conn: PostgreSQL 连接对象
        table_name: 主表名
        partition_value: 分区值（字符串）

    Returns:
        bool: 删除是否成功
    """
    if table_name is None or partition_value is None:
        print("Delete partition failed. table_name and partition_value are necessary.")
        return False

    # 检查主表是否存在
    if not has_table(conn, table_name):
        print(f"Delete partition failed. Table <{table_name}> does not exist.")
        return False

    # 构造分区表名
    partition_name = f"{table_name}_partition_for_{partition_value}"

    # 检查分区是否存在
    with conn.cursor() as cur:
        cur.execute("""
            SELECT EXISTS (
                SELECT 1 
                FROM pg_class p 
                JOIN pg_partitioned_table pt ON p.relpartbound IS NOT NULL
                WHERE p.relname = %s AND p.relispartition
            )
        """, (partition_name,))
        exists = cur.fetchone()[0]

    if not exists:
        print(f"Partition {partition_name} does not exist.")
        return False

    # 删除分区
    with conn.cursor() as cur:
        try:
            # 安全转义表名
            safe_partition_name = quote_ident(partition_name, cur)
            cur.execute(f"DROP TABLE {safe_partition_name}")
            conn.commit()
            print(f"Deleted partition {partition_name} successfully.")
            return True
        except Exception as e:
            print(f"Delete partition failed: {str(e)}")
            conn.rollback()
            return False


def describe_table(conn: psycopg2.extensions.connection, table_name: str = None) -> List[Dict]:
    if table_name is None:
        print("Describe failed. table_name is necessary.")
        return []

    # 检查表是否存在（可选）
    if not has_table(conn, table_name):
        print(f"Describe failed. Table <{table_name}> does not exist.")
        return []

    # 构造查询
    query = """
    SELECT column_name, data_type, character_maximum_length
    FROM information_schema.columns
    WHERE table_name = %s
    """

    results = []
    with conn.cursor() as cur:
        try:
            # 使用参数化查询防止 SQL 注入
            cur.execute(query, (table_name.lower(),))  # 表名通常不区分大小写，转换为小写
            for row in cur.fetchall():
                results.append({
                    "column_name": row[0],
                    "data_type": row[1],
                    "character_maximum_length": row[2]
                })
            if not results:
                print(f"No columns found for table <{table_name}>.")
            else:
                print(f"Described table <{table_name}> successfully.")
            return results
        except Exception as e:
            print(f"Describe failed: {str(e)}")
            conn.rollback()
            return []
