import psycopg2
from psycopg2.pool import SimpleConnectionPool
from typing import Optional, Dict, List, Union, Generator
from contextlib import contextmanager
import numpy as np
from modules.pg_database.pgvector.pgvector_fun import *

class PgVectorClass:
    """管理 PostgreSQL 向量数据库操作的类"""

    def __init__(self, db_name: str = "vector_db", host: str = "localhost",
                 port: int = 9888, user: str = "postgres", password: str = "password",
                 search_schema: Optional[Dict] = None, query_schema: Optional[Dict] = None,
                 min_connection: int = 10, max_connection: int = 30):
        """初始化数据库连接参数和连接池"""
        self.db_name = db_name
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.table_name: Optional[str] = None
        self.search_schema = search_schema
        self.query_schema = query_schema
        self.min_connection = min_connection
        self.max_connection = max_connection
        self.pool = self._create_pool()

    def _create_pool(self) -> SimpleConnectionPool:
        """创建并返回数据库连接池"""
        try:
            pool = SimpleConnectionPool(
                minconn=self.min_connection,
                maxconn=self.max_connection,
                dbname=self.db_name,
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password
            )
            print(f"Database connection pool created with {self.min_connection} to {self.max_connection} connections.")
            return pool
        except Exception as e:
            raise ConnectionError(f"无法连接到 PostgreSQL: {str(e)}")

    @contextmanager
    def _get_connection(self) -> Generator[psycopg2.extensions.connection, None, None]:
        """从连接池获取连接，并在使用后归还"""
        conn = self.pool.getconn()
        try:
            yield conn
        finally:
            self.pool.putconn(conn)

    @contextmanager
    def _get_cursor(self) -> Generator[psycopg2.extensions.cursor, None, None]:
        """获取游标，自动处理事务和资源清理"""
        with self._get_connection() as conn:
            cur = conn.cursor()
            try:
                yield cur
                conn.commit()  # 提交事务
            except Exception as e:
                conn.rollback()  # 回滚事务
                raise e
            finally:
                cur.close()  # 确保游标关闭

    def disconnect_postgres(self) -> bool:
        """关闭连接池并清理所有连接"""
        if self.pool:
            try:
                self.pool.closeall()
                print("Connection pool closed successfully.")
                self.pool = None
                return True
            except Exception as e:
                print(f"断开连接时出错: {str(e)}")
                return False
        return True

    def set_table(self, table_name: str) -> bool:
        """设置当前表名"""
        if not table_name or not isinstance(table_name, str):
            raise ValueError("表名必须是非空字符串")
        self.table_name = table_name
        return True

    def create_and_set_table(self, table_name: str, fields_list: List[Dict], description: str = "") -> bool:
        """创建表并将其设置为当前表"""
        with self._get_cursor() as cur:
            self.set_table(table_name)
            return create_table(cur.connection, table_name, fields_list, description)

    def create_index(self, field_name: str, index_params: Dict) -> bool:
        """在指定字段上创建索引"""
        if not self._ensure_connection_and_table():
            return False
        with self._get_cursor() as cur:
            return create_index(cur.connection, self.table_name, field_name, index_params)

    def drop_table(self) -> bool:
        """删除指定表或当前表"""
        print(f"正在删除表 {self.table_name}")
        if not self.table_name:
            return False
        with self._get_cursor() as cur:
            return drop_table(cur.connection, self.table_name)

    def describe_table(self, table_name: str = None) -> Union[List[Dict], bool]:
        target_table = table_name or self.table_name
        if not target_table:
            print("未指定表或未设置当前表")
            return False

        with self._get_cursor() as cur:
            try:
                return describe_table(cur.connection, target_table)
            except psycopg2.Error as e:
                print(f"查询表结构失败: {str(e)}")
                return False

    def insert(self, entities: List[Dict], partition_name="9888") -> bool:
        """向表中插入数据"""
        if not self._ensure_connection_and_table():
            return False
        with self._get_cursor() as cur:
            return insert_entities(cur.connection, self.table_name, entities, partition_name)

    def delete(self, expr: str, partition_name=None) -> bool:
        """删除匹配表达式的记录"""
        if not self._ensure_connection_and_table():
            return False
        with self._get_cursor() as cur:
            return delete_entities(cur.connection, self.table_name, expr, partition_name)

    def set_search_schema(self, search_schema: Dict) -> None:
        """设置搜索模式"""
        if not isinstance(search_schema, dict):
            raise ValueError("搜索模式必须是字典")
        self.search_schema = search_schema

    def set_query_schema(self, query_schema: Dict) -> None:
        """设置查询模式"""
        if not isinstance(query_schema, dict):
            raise ValueError("查询模式必须是字典")
        self.query_schema = query_schema
        print(f"设置查询模式: {self.query_schema}")

    def search(self, search_data: List[np.ndarray], search_schema: Dict = None) -> Union[bool, list]:
        """搜索表中的向量"""
        if not self._ensure_connection_and_table():
            return False
        schema = search_schema or self.search_schema
        if not schema:
            print("搜索失败，未提供或未设置搜索模式")
            return False
        with self._get_cursor() as cur:
            return search_entities(cur.connection, self.table_name, search_data, **schema)

    def multi_partition_search(
            self,
            search_data: List[np.ndarray],
            partition_names: List[str],
            search_schema: Dict = None
    ) -> Union[bool, List[Dict]]:
        """对多个分区进行向量搜索，并合并结果到一个列表"""
        if not self._ensure_connection_and_table():
            return False

        schema = search_schema or self.search_schema
        if not schema:
            print("搜索失败，未提供或未设置搜索模式")
            return False

        combined_results = []
        for partition_name in partition_names:
            try:
                with self._get_cursor() as cur:
                    results = search_entities(
                        conn=cur.connection,
                        table_name=self.table_name,
                        data=search_data,
                        vector_field=schema.get("vector_field", "embeddings"),
                        limit=schema.get("limit", 3),
                        metric_type=schema.get("metric_type", "L2"),
                        output_fields=schema.get("output_fields"),
                        expr=schema.get("expr"),
                        partition_name=partition_name
                    )
                    if results and isinstance(results, list):
                        combined_results.extend(results)
            except Exception as e:
                print(f"分区 {partition_name} 搜索失败: {str(e)}")
                continue

        if not combined_results:
            print("所有分区搜索均无结果")
            return False

        return combined_results

    def query(self, query_schema: Dict = None) -> Union[bool, list]:
        """从表中查询数据"""
        if not self._ensure_connection_and_table():
            return False
        schema = query_schema or self.query_schema
        if not schema:
            print("查询失败，未提供或未设置查询模式")
            return False
        with self._get_cursor() as cur:
            return select_entities(cur.connection, self.table_name, **schema)

    def delete_partition(self, partition_name) -> bool:
        """删除表中的指定分区"""
        if not self._ensure_connection_and_table():
            return False
        with self._get_cursor() as cur:
            return delete_partition(cur.connection, self.table_name, partition_name)

    def _ensure_connection_and_table(self) -> bool:
        """确保连接有效且表已设置"""
        if not self.pool:
            self.pool = self._create_pool()
        if not self.table_name:
            print("未设置表，请先设置或创建表")
            return False
        return True

    def __del__(self):
        """析构函数，确保断开连接"""
        self.disconnect_postgres()