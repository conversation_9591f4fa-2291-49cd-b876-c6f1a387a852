from collections import defaultdict


def rag_search(vector_client, table_name, search_data, vector_field, output_fields, expr='', partition_name="9888",
               limit=3, metric_type='cosine'):
    vector_client.set_table(table_name)
    search_schema = {"vector_field": vector_field, "limit": limit,
                     "metric_type": metric_type,
                     "output_fields": output_fields, "partition_name": partition_name, "expr": expr}
    vector_client.set_search_schema(search_schema)
    # print("search_data:",search_data)
    search_data = vector_client.search(search_data)
    return search_data


from collections import defaultdict

from collections import defaultdict


def rank(rank_type, data_dict, rank_rule=None, top_k=10):
    """
    对动态 data_dict 进行排序，支持 'weighted'、'rrf' 和 'hybrid-weighted' 类型。

    Args:
        rank_type (str): 排序类型，支持 'weighted' 或 'rrf' 或 'hybrid-weighted'
        data_dict (dict): 动态数据字典，格式为 {colname: [point_dict, ...], ...}
        rank_rule (dict): 配置字典，'weighted' 类型为 {colname: weight_str, ...}，
                         'rrf' 类型为 {'k': int}，
                         'hybrid-weighted' 类型为 [{'key': weight_dict}, ...]
        top_k (int): 对于 'hybrid-weighted'，返回前 top_k 个结果，默认 10

    Returns:
        list: 排序后的数据点列表，每个点包含分组键和组合得分
    """

    # 获取分组键
    def get_group_by():
        for col, points in data_dict.items():
            if points:
                return [k for k in points[0].keys() if k != 'distance']
        return []

    if rank_type == 'weighted':
        weights = {col: float(rank_rule[col]) for col in rank_rule if col != 'k'} if rank_rule else {}
        all_cols = set(weights.keys())
        group_by = get_group_by()
        if not group_by:
            return []

        grouped = defaultdict(list)
        for col, points in data_dict.items():
            if col in weights:
                for point in points:
                    key = tuple(point[k] for k in group_by)
                    grouped[key].append((col, point['distance']))

        result = []
        for key, existing in grouped.items():
            existing_sum = sum(weights[col] * d_i for col, d_i in existing)
            existing_cols = {col for col, _ in existing}
            missing_sum = sum(weights.get(col, 0) for col in all_cols - existing_cols)
            combined_d = existing_sum + missing_sum
            point_dict = {k: v for k, v in zip(group_by, key)}
            point_dict['distance'] = combined_d
            result.append(point_dict)
        return sorted(result, key=lambda p: p['distance'])  # 升序

    elif rank_type == 'rrf':
        k = rank_rule.get('k', 60) if rank_rule else 60
        group_by = get_group_by()
        if not group_by:
            return []

        ranked_lists = []
        item_to_dict = {}
        for col, points in data_dict.items():
            ranked_list = []
            for point in points:
                key = tuple(point[k] for k in group_by)
                ranked_list.append(key)
                item_to_dict[key] = point
            ranked_lists.append(ranked_list)

        all_items = set().union(*ranked_lists)
        scores = {}
        distances = defaultdict(list)

        for item in all_items:
            rrf_score = sum(1 / (k + ranked_list.index(item) + 1)
                            for ranked_list in ranked_lists if item in ranked_list)
            scores[item] = rrf_score
            distances[item] = [item_to_dict[item]['distance'] for _ in
                               range(sum(1 for rl in ranked_lists if item in rl))]

        result = []
        for item in all_items:
            rrf_score = scores[item]
            avg_distance = sum(distances[item]) / len(distances[item]) if distances[item] else 1.0
            combined_score = avg_distance / rrf_score if rrf_score > 0 else float('inf')

            point_dict = item_to_dict[item].copy()
            point_dict['distance'] = combined_score
            point_dict['rrf_score'] = rrf_score
            point_dict['avg_distance'] = avg_distance
            result.append(point_dict)

        return sorted(result, key=lambda p: p['distance'])  # 升序

    elif rank_type == 'hybrid-weighted':
        if not rank_rule or not isinstance(rank_rule, list):
            raise ValueError("'hybrid-weighted' requires rank_rule as a list of weight dictionaries")

        group_by = get_group_by()
        if not group_by:
            return []

        # 对每个权重字典执行 weighted 排序并取前 x 条
        intermediate_results = []
        for weight_dict in rank_rule:
            weights = {k: float(v) for k, v in weight_dict.items()}
            all_cols = set(weights.keys())

            grouped = defaultdict(list)
            for col, points in data_dict.items():
                if col in weights:
                    for point in points:
                        key = tuple(point[k] for k in group_by)
                        grouped[key].append((col, point['distance']))

            weighted_results = []
            for key, existing in grouped.items():
                existing_sum = sum(weights[col] * d_i for col, d_i in existing)
                existing_cols = {col for col, _ in existing}
                missing_sum = sum(weights.get(col, 0) for col in all_cols - existing_cols)
                combined_d = existing_sum + missing_sum
                point_dict = {k: v for k, v in zip(group_by, key)}
                point_dict['distance'] = combined_d
                weighted_results.append(point_dict)

            # 按距离升序排序并取前 top_k 条
            sorted_results = sorted(weighted_results, key=lambda p: p['distance'])  # 升序
            intermediate_results.extend(sorted_results[:top_k])
            # print(sorted_results)

        # 去重并按距离重排
        unique_results = {}
        for point in intermediate_results:
            key = tuple(point[k] for k in group_by)
            if key not in unique_results or point['distance'] < unique_results[key]['distance']:
                unique_results[key] = point

        # 最终按距离升序排序
        final_results = sorted(unique_results.values(), key=lambda p: p['distance'])  # 升序
        return final_results[:top_k]

    else:
        raise NotImplementedError(f"未知的排序类型 {rank_type}")


def hybrid_search(pgvector_client, table_name, vec_dict: dict, rank_dict: dict,
                  out_filed: list, topk: int = 3, expr='',
                  partition_name="9888", metric_type='cosine'):
    """
    混合搜索，支持多分区查询，并返回排名后的 topk 结果。

    Args:
        pgvector_client: PgVectorClass 实例
        table_name: 表名
        vec_dict: 包含向量字段和搜索数据的字典
        rank_dict: 排名参数字典
        out_filed: 输出字段列表
        topk: 返回结果数量
        expr: 过滤表达式
        partition_name: 分区名（字符串或字符串列表）
        metric_type: 距离度量类型

    Returns:
        List[Dict]: 排名后的 topk 结果
    """
    result = {}

    # 处理 partition_name 为字符串或列表
    partition_names = partition_name if isinstance(partition_name, list) else [partition_name]

    for vector_field, search_data in vec_dict.items():
        formatted_result = []

        # 对每个分区进行搜索
        for part_name in partition_names:
            search_result = rag_search(
                pgvector_client,
                table_name,
                search_data,
                vector_field,
                out_filed,
                expr,
                part_name,  # 传递单个分区名
                topk,
                metric_type
            )

            # 格式化搜索结果
            for item in search_result:
                if isinstance(item, list):
                    formatted_entry = {field: item[i] for i, field in enumerate(out_filed)}
                    if len(item) > len(out_filed):
                        formatted_entry['distance'] = item[-1]
                    formatted_result.append(formatted_entry)
                elif isinstance(item, dict):
                    formatted_entry = {field: item[field] for field in out_filed}
                    if 'distance' in item:
                        formatted_entry['distance'] = item['distance']
                    formatted_result.append(formatted_entry)
                else:
                    raise ValueError(f"Unexpected item type in search_result: {type(item)}")

        result[vector_field] = formatted_result

    # 排名处理
    rank_type = rank_dict.get("type", "weighted")
    rank_rule = rank_dict.get("rank_rule", {})
    ranked_result = rank(rank_type, result, rank_rule)

    # 返回 topk 结果
    return ranked_result[:topk]
