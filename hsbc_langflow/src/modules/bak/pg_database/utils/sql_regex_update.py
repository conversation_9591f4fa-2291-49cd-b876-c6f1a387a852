from utils.common.logger_util import logger

def update_sql_with_regex(original_sql, original_where_formula, modified_where_formula):
    """
    使用正则表达式更新SQL中的WHERE条件值
    
    参数:
        original_sql (str): 原始SQL语句
        original_where_formula (list/dict): 原始WHERE条件的结构化表示
        modified_where_formula (list/dict): 修正后WHERE条件的结构化表示
        
    返回:
        str: 更新后的SQL语句
    """
    import re
    
    # 确保输入格式一致
    if isinstance(original_where_formula, list) and len(original_where_formula) == 1:
        original_where_formula = original_where_formula[0]
    if isinstance(modified_where_formula, list) and len(modified_where_formula) == 1:
        modified_where_formula = modified_where_formula[0]
    
    # 打印调试信息
    logger.debug(f"原始WHERE公式类型: {type(original_where_formula)}")
    logger.debug(f"修改后WHERE公式类型: {type(modified_where_formula)}")
    logger.debug(f"原始WHERE公式: {original_where_formula}")
    logger.debug(f"修改后WHERE公式: {modified_where_formula}")
    
    # 提取需要修改的字段和值的映射关系
    changes = []
    
    def extract_changes(orig_formula, mod_formula, path=[]):
        """递归提取字段的原始值和修正值的映射"""
        logger.debug(f"检查路径 {path}: orig={orig_formula.get('filter', {}).get('modelColId', 'N/A') if 'filter' in orig_formula else 'N/A'}")
        
        if 'combine' in orig_formula and 'combine' in mod_formula:
            if orig_formula['combine'] == mod_formula['combine'] and len(orig_formula.get('groups', [])) == len(mod_formula.get('groups', [])):
                for i, (orig_group, mod_group) in enumerate(zip(orig_formula.get('groups', []), mod_formula.get('groups', []))):
                    extract_changes(orig_group, mod_group, path + [i])
        elif 'filter' in orig_formula and 'filter' in mod_formula:
            orig_filter = orig_formula['filter']
            mod_filter = mod_formula['filter']
            
            # 确认字段和操作符匹配
            if orig_filter.get('modelColId') == mod_filter.get('modelColId') and orig_filter.get('op') == mod_filter.get('op'):
                field_name = orig_filter.get('modelColId')
                op = orig_filter.get('op')
                
                # 检查普通值是否变更
                orig_vals = orig_filter.get('val_list', [])
                mod_vals = mod_filter.get('val_list', [])
                
                logger.debug(f"比较字段 {field_name} {op}: 原始值={orig_vals}, 修改后值={mod_vals}")
                
                # 值列表长度匹配时逐个比较
                if len(orig_vals) == len(mod_vals):
                    for i, (orig_val, mod_val) in enumerate(zip(orig_vals, mod_vals)):
                        if str(orig_val) != str(mod_val):
                            logger.debug(f"发现变更: {field_name} {op} {orig_val} -> {mod_val}")
                            changes.append({
                                'field': field_name,
                                'op': op,
                                'old_value': orig_val,
                                'new_value': mod_val,
                                'path': path
                            })
                else:
                    # 值列表长度不匹配，但都不为空
                    if orig_vals and mod_vals:
                        logger.debug(f"值列表长度不匹配: 原始={len(orig_vals)}, 修改后={len(mod_vals)}")
                        for i in range(min(len(orig_vals), len(mod_vals))):
                            if i < len(orig_vals) and i < len(mod_vals) and str(orig_vals[i]) != str(mod_vals[i]):
                                logger.debug(f"发现变更: {field_name} {op} {orig_vals[i]} -> {mod_vals[i]}")
                                changes.append({
                                    'field': field_name,
                                    'op': op,
                                    'old_value': orig_vals[i],
                                    'new_value': mod_vals[i],
                                    'path': path
                                })
                
                # 检查between的范围值是否变更
                if op == 'between':
                    if orig_filter.get('start') != mod_filter.get('start'):
                        changes.append({
                            'field': field_name,
                            'op': 'between_start',
                            'old_value': orig_filter.get('start'),
                            'new_value': mod_filter.get('start'),
                            'path': path
                        })
                    if orig_filter.get('end') != mod_filter.get('end'):
                        changes.append({
                            'field': field_name,
                            'op': 'between_end',
                            'old_value': orig_filter.get('end'),
                            'new_value': mod_filter.get('end'),
                            'path': path
                        })
    
    # 直接遍历原始和修改后的WHERE条件树，找出所有变化的叶子节点
    def traverse_and_compare(orig, mod, path=[]):
        """遍历两棵树并比较叶子节点的值"""
        if 'combine' in orig and 'combine' in mod:
            # 是复合条件，继续遍历子节点
            if orig['combine'] == mod['combine']:
                orig_groups = orig.get('groups', [])
                mod_groups = mod.get('groups', [])
                for i in range(min(len(orig_groups), len(mod_groups))):
                    traverse_and_compare(orig_groups[i], mod_groups[i], path + [i])
        elif 'filter' in orig and 'filter' in mod:
            # 比较叶子节点
            orig_filter = orig['filter']
            mod_filter = mod['filter']
            if orig_filter.get('modelColId') == mod_filter.get('modelColId'):
                field = orig_filter.get('modelColId')
                op = orig_filter.get('op')
                
                # 比较值列表
                orig_vals = orig_filter.get('val_list', [])
                mod_vals = mod_filter.get('val_list', [])
                
                logger.debug(f"比较字段 {field} {op}: 原始值={orig_vals}, 修改后值={mod_vals}")
                
                for i in range(min(len(orig_vals), len(mod_vals))):
                    if str(orig_vals[i]) != str(mod_vals[i]):
                        logger.debug(f"发现变更: {field} {op} {orig_vals[i]} -> {mod_vals[i]}")
                        changes.append({
                            'field': field,
                            'op': op,
                            'old_value': orig_vals[i],
                            'new_value': mod_vals[i],
                            'path': path
                        })
    
    # 使用更简单的比较逻辑
    traverse_and_compare(original_where_formula, modified_where_formula)
    
    # 打印所有需要修改的项
    if changes:
        for change in changes:
            logger.info(f"需要修改: {change['field']} {change['op']} 从 '{change['old_value']}' 到 '{change['new_value']}'")
    else:
        logger.debug("没有找到需要修改的值，启用额外检测...")
        extract_changes(original_where_formula, modified_where_formula)
    
    if not changes:
        logger.debug("仍未找到需要修改的值！检查深度复制是否正确工作。")
        return original_sql
    
    # 将SQL分割为行，以便更好地处理可能跨行的SQL语句
    sql_lines = original_sql.splitlines()
    sql_text = ' '.join(sql_lines)
    
    # 对找到的每个变更进行替换
    updated_sql = sql_text
    for change in changes:
        field = change['field']
        op = change['op']
        old_value = str(change['old_value'])
        new_value = str(change['new_value'])
        
        # 构建模式，精确匹配字段条件
        if op == 'between_start':
            pattern = rf"({field}\s+BETWEEN\s+)(['\"]?{re.escape(old_value)}['\"]?)"
            repl = rf"\1'{new_value}'"
        elif op == 'between_end':
            pattern = rf"(AND\s+)(['\"]?{re.escape(old_value)}['\"]?)"
            repl = rf"\1'{new_value}'"
        else:
            # 处理常规操作符
            op_pattern = '='
            if op == '!=':
                op_pattern = '!='
            elif op == '>':
                op_pattern = '>'
            elif op == '>=':
                op_pattern = '>='
            elif op == '<':
                op_pattern = '<'
            elif op == '<=':
                op_pattern = '<='
            elif op == 'like':
                op_pattern = 'LIKE'
            elif op == 'in':
                op_pattern = 'IN'
            
            # 为字符串和数字构建不同的模式
            if isinstance(old_value, (int, float)) or (isinstance(old_value, str) and old_value.isdigit()):
                # 数字不需要引号
                pattern = rf"{field}\s*{op_pattern}\s*{re.escape(old_value)}"
                repl = f"{field} {op_pattern} {new_value}"
            else:
                # 字符串需要匹配引号情况
                # 注意单引号和双引号都可能出现
                pattern = rf"{field}\s*{op_pattern}\s*(['\"]){re.escape(old_value)}(['\"])"
                # 使用花括号避免\1后接数字被解释为八进制
                repl = rf"{field} {op_pattern} \g<1>{new_value}\g<2>"
        
        # 执行替换
        logger.info(f"应用正则表达式: {pattern} -> {repl}")
        updated_result = re.sub(pattern, repl, updated_sql)
        
        if updated_result == updated_sql:
            logger.debug(f"警告: 正则表达式没有匹配到任何内容: {pattern}")
            # 尝试更简单的模式
            simpler_pattern = rf"{field}\s*{op_pattern}\s*['\"]?{re.escape(old_value)}['\"]?"
            logger.debug(f"尝试更简单的模式: {simpler_pattern}")
            updated_result = re.sub(simpler_pattern, f"{field} {op_pattern} '{new_value}'", updated_sql)
            
            if updated_result == updated_sql:
                logger.debug(f"警告: 简化的正则表达式仍然没有匹配到任何内容")
                # 最后尝试直接替换引号内的值
                pattern = rf"(['\"]){re.escape(old_value)}(['\"])"
                repl = rf"\1{new_value}\2"
                updated_result = re.sub(pattern, repl, updated_sql)
        
        updated_sql = updated_result
    
    # 如果替换后的SQL与原始SQL相同，尝试直接替换所有出现的值
    if updated_sql == sql_text:
        logger.debug("警告: 所有正则表达式替换都失败了，尝试直接替换值")
        for change in changes:
            old_value = str(change['old_value'])
            new_value = str(change['new_value'])
            # 直接替换带引号的值
            updated_sql = updated_sql.replace(f"'{old_value}'", f"'{new_value}'")
            updated_sql = updated_sql.replace(f'"{old_value}"', f'"{new_value}"')
    
    return updated_sql