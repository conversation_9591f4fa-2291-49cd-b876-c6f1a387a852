import sqlglot
from sqlglot import exp
import re
from utils.common.logger_util import logger
def update_sql_with_ast(original_sql, original_where_formula, modified_where_formula):
    """
    使用AST操作更新SQL中的WHERE条件值
    
    参数:
        original_sql (str): 原始SQL语句
        original_where_formula (list/dict): 原始WHERE条件的结构化表示
        modified_where_formula (list/dict): 修正后WHERE条件的结构化表示
        
    返回:
        str: 更新后的SQL语句
    """
    # 确保输入格式一致
    if isinstance(original_where_formula, list) and len(original_where_formula) == 1:
        original_where_formula = original_where_formula[0]
    if isinstance(modified_where_formula, list) and len(modified_where_formula) == 1:
        modified_where_formula = modified_where_formula[0]
    
    # 打印调试信息
    logger.debug(f"原始WHERE公式类型: {type(original_where_formula)}")
    logger.debug(f"修改后WHERE公式类型: {type(modified_where_formula)}")
    logger.debug(f"原始WHERE公式: {original_where_formula}")
    logger.debug(f"修改后WHERE公式: {modified_where_formula}")
    
    # 提取需要修改的条件和值
    changes = []
    
    # 直接遍历原始和修改后的WHERE条件树，找出所有变化的叶子节点
    def traverse_and_compare(orig, mod, path=[]):
        """遍历两棵树并比较叶子节点的值"""
        if 'combine' in orig and 'combine' in mod:
            # 是复合条件，继续遍历子节点
            if orig['combine'] == mod['combine']:
                orig_groups = orig.get('groups', [])
                mod_groups = mod.get('groups', [])
                for i in range(min(len(orig_groups), len(mod_groups))):
                    traverse_and_compare(orig_groups[i], mod_groups[i], path + [i])
        elif 'filter' in orig and 'filter' in mod:
            # 比较叶子节点
            orig_filter = orig['filter']
            mod_filter = mod['filter']
            if orig_filter.get('modelColId') == mod_filter.get('modelColId'):
                field = orig_filter.get('modelColId')
                op = orig_filter.get('op')
                
                # 比较值列表
                orig_vals = orig_filter.get('val_list', [])
                mod_vals = mod_filter.get('val_list', [])
                
                logger.debug(f"比较字段 {field} {op}: 原始值={orig_vals}, 修改后值={mod_vals}")
                
                for i in range(min(len(orig_vals), len(mod_vals))):
                    if str(orig_vals[i]) != str(mod_vals[i]):
                        logger.debug(f"发现变更: {field} {op} {orig_vals[i]} -> {mod_vals[i]}")
                        changes.append({
                            'field': field,
                            'op': op,
                            'old_value': orig_vals[i],
                            'new_value': mod_vals[i],
                            'path': path
                        })
                
                # 检查between的范围值是否变更
                if op == 'between':
                    if orig_filter.get('start') != mod_filter.get('start'):
                        changes.append({
                            'field': field,
                            'op': 'between_start',
                            'old_value': orig_filter.get('start'),
                            'new_value': mod_filter.get('start'),
                            'path': path
                        })
                    if orig_filter.get('end') != mod_filter.get('end'):
                        changes.append({
                            'field': field,
                            'op': 'between_end',
                            'old_value': orig_filter.get('end'),
                            'new_value': mod_filter.get('end'),
                            'path': path
                        })
    
    # 使用更简单的比较逻辑
    traverse_and_compare(original_where_formula, modified_where_formula)
    
    # 打印所有需要修改的项
    if changes:
        for change in changes:
            logger.debug(f"需要修改: {change['field']} {change['op']} 从 '{change['old_value']}' 到 '{change['new_value']}'")
    else:
        # 使用备用方法检测变更
        logger.debug("没有找到需要修改的值，检查深度复制是否正确工作")
        return original_sql
    
    try:
        # 解析原始SQL为AST
        parsed_sql = sqlglot.parse_one(original_sql)
        
        # 更新AST中的值
        updated = update_values_in_ast(parsed_sql, changes)
        
        # 将修改后的AST转换为SQL
        modified_sql = parsed_sql.sql()
        
        # 检查SQL是否真的被修改
        if modified_sql == original_sql or not updated:
            logger.debug("警告: AST方法未能修改SQL，尝试使用直接替换方法")
            return direct_replace_sql(original_sql, changes)
        
        return modified_sql
    except Exception as e:
        logger.debug(f"AST处理出错: {str(e)}，使用备用方法")
        return direct_replace_sql(original_sql, changes)


def update_values_in_ast(ast_node, changes):
    """在AST中更新值"""
    updated = False
    
    def match_field_op(field_expr, field_name):
        """检查表达式是否匹配字段名"""
        if isinstance(field_expr, exp.Column):
            # 获取完整字段名
            col_parts = str(field_expr).split('.')
            col_name = col_parts[-1] if len(col_parts) > 1 else str(field_expr)
            return col_name == field_name or str(field_expr) == field_name
        return False
    
    def recurse_where(node):
        """递归遍历WHERE条件并更新值"""
        nonlocal updated
        
        # 处理比较操作符
        if isinstance(node, (exp.EQ, exp.NEQ, exp.GT, exp.GTE, exp.LT, exp.LTE, exp.Like)):
            op_map = {
                exp.EQ: '=',
                exp.NEQ: '!=',
                exp.GT: '>',
                exp.GTE: '>=',
                exp.LT: '<',
                exp.LTE: '<=',
                exp.Like: 'like'
            }
            
            for change in changes:
                if change['op'] in ('=', '!=', '>', '>=', '<', '<=', 'like'):
                    op_cls = next((k for k, v in op_map.items() if v == change['op']), None)
                    if (isinstance(node, op_cls) and 
                        match_field_op(node.left, change['field']) and
                        isinstance(node.right, exp.Literal) and 
                        str(node.right.this) == str(change['old_value'])):
                        
                        try:
                            logger.debug(f"AST: 尝试将 {change['field']} {change['op']} '{node.right.this}' 更改为 '{change['new_value']}'")
                            # 创建一个新的Literal节点而不是修改现有节点
                            new_literal = exp.Literal.create(change['new_value'])
                            node.args['expression'] = new_literal
                            updated = True
                            return True
                        except Exception as e:
                            logger.debug(f"更新节点值时出错: {str(e)}")
        
        # 处理BETWEEN子句
        elif isinstance(node, exp.Between):
            for change in changes:
                if match_field_op(node.this, change['field']):
                    if change['op'] == 'between_start' and isinstance(node.args['low'], exp.Literal):
                        if str(node.args['low'].this) == str(change['old_value']):
                            try:
                                logger.debug(f"AST: 将BETWEEN的下界从 '{node.args['low'].this}' 更改为 '{change['new_value']}'")
                                # 创建新的Literal节点
                                node.args['low'] = exp.Literal.create(change['new_value'])
                                updated = True
                                return True
                            except Exception as e:
                                logger.debug(f"更新BETWEEN下界时出错: {str(e)}")
                    elif change['op'] == 'between_end' and isinstance(node.args['high'], exp.Literal):
                        if str(node.args['high'].this) == str(change['old_value']):
                            try:
                                logger.debug(f"AST: 将BETWEEN的上界从 '{node.args['high'].this}' 更改为 '{change['new_value']}'")
                                # 创建新的Literal节点
                                node.args['high'] = exp.Literal.create(change['new_value'])
                                updated = True
                                return True
                            except Exception as e:
                                logger.debug(f"更新BETWEEN上界时出错: {str(e)}")
        
        # 处理IN子句
        elif isinstance(node, exp.In):
            for change in changes:
                if change['op'] == 'in' and match_field_op(node.this, change['field']):
                    for i, expr in enumerate(node.expressions):
                        if isinstance(expr, exp.Literal) and str(expr.this) == str(change['old_value']):
                            try:
                                logger.debug(f"AST: 将IN列表中的 '{expr.this}' 更改为 '{change['new_value']}'")
                                # 替换表达式列表中的元素
                                node.expressions[i] = exp.Literal.create(change['new_value'])
                                updated = True
                                return True
                            except Exception as e:
                                logger.debug(f"更新IN列表值时出错: {str(e)}")
        
        # 递归处理条件组合
        if isinstance(node, (exp.And, exp.Or)):
            left_updated = recurse_where(node.left) if hasattr(node, 'left') and node.left else False
            right_updated = recurse_where(node.right) if hasattr(node, 'right') and node.right else False
            return left_updated or right_updated
        
        # 处理括号
        elif isinstance(node, exp.Paren):
            return recurse_where(node.this)
        
        # 处理WHERE
        elif isinstance(node, exp.Where):
            return recurse_where(node.this)
        
        return False
    
    # 查找并处理所有WHERE子句
    for where_expr in ast_node.find_all(exp.Where):
        recurse_where(where_expr)
    
    return updated


def direct_replace_sql(original_sql, changes):
    """使用正则表达式直接替换SQL中的值"""
    # 将SQL分割为行，以便更好地处理可能跨行的SQL语句
    sql_lines = original_sql.splitlines()
    sql_text = ' '.join(sql_lines)
    updated_sql = sql_text
    
    for change in changes:
        field = change['field']
        op = change['op']
        old_value = str(change['old_value'])
        new_value = str(change['new_value'])
        
        # 构建模式，精确匹配字段条件
        if op == 'between_start':
            pattern = rf"({field}\s+BETWEEN\s+)(['\"]?{re.escape(old_value)}['\"]?)"
            repl = rf"\1'{new_value}'"
        elif op == 'between_end':
            pattern = rf"(AND\s+)(['\"]?{re.escape(old_value)}['\"]?)"
            repl = rf"\1'{new_value}'"
        else:
            # 处理常规操作符
            op_pattern = '='
            if op == '!=':
                op_pattern = '!='
            elif op == '>':
                op_pattern = '>'
            elif op == '>=':
                op_pattern = '>='
            elif op == '<':
                op_pattern = '<'
            elif op == '<=':
                op_pattern = '<='
            elif op == 'like':
                op_pattern = 'LIKE'
            elif op == 'in':
                op_pattern = 'IN'
            
            # 为字符串和数字构建不同的模式
            if isinstance(old_value, (int, float)) or (isinstance(old_value, str) and old_value.isdigit()):
                # 数字不需要引号
                pattern = rf"{field}\s*{op_pattern}\s*{re.escape(old_value)}"
                repl = f"{field} {op_pattern} {new_value}"
            else:
                # 字符串需要匹配引号情况
                pattern = rf"{field}\s*{op_pattern}\s*(['\"]){re.escape(old_value)}(['\"])"
                # 使用\g<N>语法避免八进制转义问题
                repl = rf"{field} {op_pattern} \g<1>{new_value}\g<2>"
        
        # 执行替换
        logger.debug(f"直接替换: {pattern} -> {repl}")
        updated_result = re.sub(pattern, repl, updated_sql)
        
        if updated_result == updated_sql:
            logger.debug(f"警告: 正则表达式没有匹配到任何内容: {pattern}")
            # 尝试更简单的模式
            simpler_pattern = rf"{field}\s*{op_pattern}\s*['\"]?{re.escape(old_value)}['\"]?"
            logger.debug(f"尝试更简单的模式: {simpler_pattern}")
            updated_result = re.sub(simpler_pattern, f"{field} {op_pattern} '{new_value}'", updated_sql)
            
            if updated_result == updated_sql:
                # 最后尝试直接替换引号内的值
                pattern = rf"(['\"]){re.escape(old_value)}(['\"])"
                # 使用\g<N>语法避免八进制转义问题
                repl = rf"\g<1>{new_value}\g<2>"
                updated_result = re.sub(pattern, repl, updated_sql)
        
        updated_sql = updated_result
    
    # 如果替换后的SQL与原始SQL相同，尝试最终的直接值替换
    if updated_sql == sql_text:
        logger.debug("警告: 所有正则表达式替换都失败了，尝试直接替换值")
        return fallback_replace_all(original_sql, changes)
    
    return updated_sql


def fallback_replace_all(sql, changes):
    """最后的备用方法：直接替换所有匹配的字符串值"""
    updated_sql = sql
    
    logger.debug("使用最终备用方法: 直接替换引号内的值")
    for change in changes:
        old_value = str(change['old_value'])
        new_value = str(change['new_value'])
        
        # 直接替换带引号的值
        updated_sql = updated_sql.replace(f"'{old_value}'", f"'{new_value}'")
        updated_sql = updated_sql.replace(f'"{old_value}"', f'"{new_value}"')
    
    return updated_sql 