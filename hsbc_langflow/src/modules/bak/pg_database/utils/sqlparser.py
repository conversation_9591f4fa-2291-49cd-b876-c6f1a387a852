import re

import sqlglot
from sqlglot import exp


def extract_having_filters(sql):
    """
    从 SQL 查询中提取 HAVING 子句并转换为指定格式
    参数:
        sql (str): 包含 HAVING 子句的 SQL 查询字符串
    返回:
        list: 格式化后的 HAVING 条件列表
    """
    # 解析 SQL
    parsed = sqlglot.parse_one(sql)

    # 如果没有 HAVING 子句，返回空列表
    if "having" not in parsed.args:
        return []

    having = parsed.args["having"]

    # 定义转换表达式的辅助函数
    def extract_condition(expr):
        # 如果是 Having 类型，提取其内部条件
        if isinstance(expr, exp.Having):
            return extract_condition(expr.this)

        # 处理 AND 条件
        if isinstance(expr, exp.And):
            return {
                "combine": "and",
                "groups": [extract_condition(expr.left), extract_condition(expr.right)]
            }
        # 处理 OR 条件
        elif isinstance(expr, exp.Or):
            return {
                "combine": "or",
                "groups": [extract_condition(expr.left), extract_condition(expr.right)]
            }
        # 处理等于 (EQ)
        elif isinstance(expr, exp.EQ):
            return {
                "filter": {
                    "op": "=",
                    "if_not": False,
                    "start": "",
                    "end": "",
                    "val_list": [expr.right.this],
                    "modelColId": str(expr.left)
                }
            }
        # 处理不等于 (NEQ)
        elif isinstance(expr, exp.NEQ):
            return {
                "filter": {
                    "op": "!=",
                    "if_not": False,
                    "start": "",
                    "end": "",
                    "val_list": [expr.right.this],
                    "modelColId": str(expr.left)
                }
            }
        # 处理大于 (GT)
        elif isinstance(expr, exp.GT):
            return {
                "filter": {
                    "op": ">",
                    "if_not": False,
                    "start": "",
                    "end": "",
                    "val_list": [expr.right.this],
                    "modelColId": str(expr.left)
                }
            }
        # 处理大于等于 (GTE)
        elif isinstance(expr, exp.GTE):
            return {
                "filter": {
                    "op": ">=",
                    "if_not": False,
                    "start": "",
                    "end": "",
                    "val_list": [expr.right.this],
                    "modelColId": str(expr.left)
                }
            }
        # 处理小于 (LT)
        elif isinstance(expr, exp.LT):
            return {
                "filter": {
                    "op": "<",
                    "if_not": False,
                    "start": "",
                    "end": "",
                    "val_list": [expr.right.this],
                    "modelColId": str(expr.left)
                }
            }
        # 处理小于等于 (LTE)
        elif isinstance(expr, exp.LTE):
            return {
                "filter": {
                    "op": "<=",
                    "if_not": False,
                    "start": "",
                    "end": "",
                    "val_list": [expr.right.this],
                    "modelColId": str(expr.left)
                }
            }
        # 处理 LIKE
        elif isinstance(expr, exp.Like):
            return {
                "filter": {
                    "op": "like",
                    "if_not": False,
                    "start": "",
                    "end": "",
                    "val_list": [expr.right.this],
                    "modelColId": str(expr.left)
                }
            }
        # 处理 BETWEEN
        elif isinstance(expr, exp.Between):
            return {
                "filter": {
                    "op": "between",
                    "if_not": False,
                    "start": expr.args["low"].this,
                    "end": expr.args["high"].this,
                    "val_list": [],  # 根据之前的讨论，这里改为空列表
                    "modelColId": str(expr.this)
                }
            }
        else:
            raise ValueError(f"Unsupported expression type: {type(expr)}")

    # 从 Having 中提取条件并处理
    return extract_condition(having)


def having_parse(sql):
    """
    解析 SQL 中的 HAVING 子句，当只有一个条件时将 modelColId 设置为 -1（业务函数）
    参数:
        sql (str): 包含 HAVING 子句的 SQL 查询字符串
    返回:
        list: 格式化后的 HAVING 条件列表
    """
    # 调用基础函数获取解析结果
    condition = extract_having_filters(sql)

    # 检查是否为单一条件
    def is_single_condition(cond):
        return "combine" not in cond  # 如果没有 combine，说明是单一条件

    # 如果是单一条件，将 modelColId 改为 -1
    if is_single_condition(condition):
        condition["filter"]["modelColId"] = -1

    return [condition]


def filter_index_parse(sql):
    """
    解析 SQL 中的 HAVING 子句，转换为 filterIndex 格式
    参数:
        sql (str): 包含 HAVING 子句的 SQL 查询字符串
    返回:
        dict: 格式化后的 filterIndex 结构，如果没有 HAVING 子句则返回空字典
    """
    # 调用基础函数获取解析结果
    condition = extract_having_filters(sql)

    # 如果结果为空列表或None，表示没有HAVING子句，返回空字典
    if not condition:
        return {}

    # 检查是否为单一条件
    def is_single_condition(cond):
        return "combine" not in cond  # 如果没有 combine，说明是单一条件

    # 如果是单一条件，将 modelColId 改为 -1
    if is_single_condition(condition):
        condition["filter"]["modelColId"] = "-1"  # 确保是字符串类型
        return {
            "combine": "and",
            "groups": [
                {
                    "combine": "and",
                    "filter": condition["filter"]
                }
            ]
        }
    
    # 如果不是单一条件，直接返回
    return condition



def parse_case_when(sql: str):
    """Parse CASE WHEN statements from SQL string, including WHEN and ELSE clauses."""
    parsed_sql = sqlglot.parse_one(sql)
    case_expressions = parsed_sql.find_all(exp.Case)
    result = []

    alias_map = {}
    select = parsed_sql.find(exp.Select)
    if select:
        for expr in select.expressions:
            if isinstance(expr, exp.Alias) and isinstance(expr.this, exp.Case):
                alias_map[id(expr.this)] = expr.alias

    for case in case_expressions:
        case_id = id(case)
        col_name = alias_map.get(case_id, "unnamed_case_when")

        ifs = case.args.get('ifs', [])
        for if_exp in ifs:
            condition = if_exp.this.sql()
            then_value = if_exp.args.get('true')
            value = then_value.args['this'] if isinstance(then_value, exp.Literal) else str(then_value)
            result.append({'modelColId': col_name, 'where_parse': condition, 'value': value})

        default = case.args.get('default')
        if default:
            default_value = default.this
            value = default_value.args['this'] if isinstance(default_value, exp.Literal) else str(default_value)
            result.append({'modelColId': col_name, 'where_parse': 'ELSE', 'value': value})

    return result


def where_parse(sql: str):
    """Parse WHERE clause from SQL string with alias resolution."""
    op_dict_value = {'EQ': '=', 'NEQ': '!=', 'GT': '>', 'GTE': '>=', 'LT': '<', 'LTE': '<=', 'Like': 'like'}

    def sql_tt(sql: str) -> str:
        processed_sql = re.sub(r'(?i)\bIS\s+NOT\s+NULL\b', '!= NULL', sql)
        return processed_sql
    sql = sql_tt(sql)
    # Parse CASE WHEN statements first to resolve aliases
    case_statements = parse_case_when(sql)
    alias_map = {}
    parsed_sql = sqlglot.parse_one(sql)
    for select in parsed_sql.args.get('expressions', []):
        if isinstance(select, exp.Alias):
            alias_map[select.args['alias'].args['this']] = select.args['this']

    def find_col_name(where_dict):
        """Extract column name from nested structure."""
        current = where_dict
        while current['class'] != 'Column':
            current = current['args']['this']
        return current['args']['this']['args']['this']

    def single_where_parse(where_dict):
        """Parse WHERE clause into a structured representation."""
        filter_type = where_dict['class']

        if filter_type == 'Where':
            return single_where_parse(where_dict['args']['this'])

        if filter_type == 'Paren':
            return single_where_parse(where_dict['args']['this'])

        if filter_type == 'Case':
            return {}  # CASE in WHERE not directly supported

        if filter_type == 'Column':
            col_name = find_col_name(where_dict)
            return {'modelColId': col_name}

        if filter_type == 'Literal':
            return where_dict['args']['this']

        if filter_type in ('And', 'Or'):
            return {
                'combine': filter_type.lower(),
                'groups': [
                    single_where_parse(where_dict['args']['this']),
                    single_where_parse(where_dict['args']['expression'])
                ]
            }

        if filter_type in op_dict_value:
            left = single_where_parse(where_dict['args']['this'])
            right = single_where_parse(where_dict['args']['expression'])
            return {
                'op': op_dict_value[filter_type],
                'left': left,
                'right': right
            }

        if filter_type == 'Not':
            return {
                'op': 'not',
                'expression': single_where_parse(where_dict['args']['this'])
            }

        if filter_type == 'In':
            return {
                'op': 'in',
                'modelColId': single_where_parse(where_dict['args']['this'])['modelColId'],
                'val_list': [exp['args']['this'] for exp in where_dict['args']['expressions']]
            }

        if filter_type == 'Between':
            return {
                'op': 'between',
                'modelColId': single_where_parse(where_dict['args']['this'])['modelColId'],
                'low': single_where_parse(where_dict['args']['low']),
                'high': single_where_parse(where_dict['args']['high'])
            }

        if filter_type == 'Is':
            print(where_dict)
            return {
                'op': 'is',
                'modelColId': single_where_parse(where_dict['args']['this'])['modelColId'],
                'val_list': [where_dict['args']['expression']['class']]
            }
        return {}

    def convert_to_filter_dict(parsed, case_statements, alias_map):
        """将解析后的 WHERE 结构转换为目标过滤器格式。"""
        if isinstance(parsed, dict):
            if 'combine' in parsed:
                return {
                    'combine': parsed['combine'],
                    'groups': [convert_to_filter_dict(child, case_statements, alias_map) for child in
                                 parsed['groups']]
                }
            elif 'op' in parsed:
                filter_dict = {
                    'op': parsed['op'],
                    'if_not': False,
                    'start': '',
                    'end': '',
                    'val_list': []
                }
                left = parsed.get('left')
                right = parsed.get('right')
                expression = parsed.get('expression')

                # 处理 modelColId（包括 BETWEEN 的情况）
                if 'modelColId' in parsed:  # BETWEEN 直接提供 modelColId
                    filter_dict['modelColId'] = parsed['modelColId']
                elif left and isinstance(left, dict) and 'modelColId' in left:  # 其他操作符从 left 获取
                    filter_dict['modelColId'] = left['modelColId']
                    col_name = left['modelColId']
                    if col_name in alias_map and isinstance(alias_map[col_name], exp.Case):
                        filter_dict['is_alias'] = True
                        filter_dict['case_when'] = [case for case in case_statements if case['modelColId'] == col_name]

                # 处理 BETWEEN 子句
                if parsed['op'] == 'between':
                    filter_dict['start'] = parsed['low'] if isinstance(parsed['low'], str) else ''
                    filter_dict['end'] = parsed['high'] if isinstance(parsed['high'], str) else ''
                    filter_dict['val_list'] = []
                    return {'filter': filter_dict}

                # 处理其他类型的 right 或 expression
                if right:
                    if isinstance(right, str):
                        filter_dict['val_list'] = [right]
                    elif isinstance(right, list):
                        filter_dict['val_list'] = right

                if expression:
                    filter_dict['val_list'] = [convert_to_filter_dict(expression, case_statements, alias_map)]

                return {'filter': filter_dict}
            else:
                return parsed
        return parsed

    def rm_false_col_name(where_dict):
        """Remove filters with false column names."""
        if not where_dict:
            return None
        result = where_dict.copy()
        if 'combine' in result:
            child1 = rm_false_col_name(result['groups'][0])
            child2 = rm_false_col_name(result['groups'][1])
            if not child1:
                return child2
            if not child2:
                return child1
            result['groups'] = [child1, child2]
        elif 'filter' in result and result['filter'].get('modelColId') is False:
            return None
        return result

    where_clauses = [w.dump() for w in parsed_sql.find_all(exp.Where)]
    parsed_where = [single_where_parse(w) for w in where_clauses]
    result = [rm_false_col_name(convert_to_filter_dict(p, case_statements, alias_map)) for p in parsed_where]

    if len(result) > 1:
        return [{'combine': 'and', 'groups': result}]
    return result


def group_parse(sql: str):
    def parse_expression(expr_dict):
        expr_class = expr_dict['class']
        # print(expr_class)
        if expr_class == 'Column':
            return f"{parse_identifier(expr_dict)['args']['this']}"  # Assuming we want a SUM function here

        elif expr_class == 'Case':  # TODO
            if "this" in expr_dict['args']:
                return parse_expression(expr_dict['args']['this'])
            elif "ifs" in expr_dict['args']:
                return parse_expression(expr_dict['args']['ifs'][0])

        elif expr_class == 'If':
            return parse_expression(expr_dict['args']['true'])

        elif expr_class == 'Identifier':
            return expr_dict['args']['this']

        elif expr_class == 'Literal':
            return expr_dict['args']['this']

        elif expr_class == 'Alias':
            return parse_expression(expr_dict['args']['this'])

        elif expr_class == 'Paren':
            return f"({parse_expression(expr_dict['args']['this'])})"

        else:
            raise ValueError(f"Unsupported expression class: {expr_class}")

    def parse_identifier(identifier_dict):
        return identifier_dict['args']['this']

    group = sqlglot.parse_one(sql).find(exp.Group)
    if group is None:
        return []
    group_col_list = []
    for g in group.expressions:
        group_dict = g.dump()
        g_col = parse_expression(group_dict['args']['this'])
        group_col_list.append(g_col)

    return group_col_list



def index_dims_parse(sql: str):
    """
    解析 SQL 中的 GROUP BY 子句，转换为 indexDims 格式
    参数:
        sql (str): 包含 GROUP BY 子句的 SQL 查询字符串
    返回:
        list: 格式化后的 indexDims 列表，如果没有 GROUP BY 子句则返回空列表
    """
    group_cols = group_parse(sql)
    
    # 如果没有 GROUP BY 子句，返回空列表
    if not group_cols:
        return []
        
    result = []
    
    for col in group_cols:
        # 从列名中提取 modelColId
        # 这里假设列名格式为 "表名.列名" 或 "列名"
        parts = col.split('.')
        if len(parts) > 1:
            col_name = parts[1]
        else:
            col_name = parts[0]
            
        # 创建 indexDims 项
        dim_item = {
            "modelColId": col_name
        }
        
        # 如果是日期类型，添加 graint 属性
        # 这里需要根据实际情况判断是否为日期类型
        # 简单判断：如果列名包含 date, time, day, month, year 等关键字
        date_keywords = ['date', 'time', 'day', 'month', 'year']
        if any(keyword in col_name.lower() for keyword in date_keywords):
            dim_item["graint"] = "日月年"
            
        result.append(dim_item)
    
    return result 

def select_parse(sql: str):
    """
    Parse SQL SELECT expressions into string format, excluding CASE WHEN details.
    TODO: Need to consider date function
    """
    # Operator mapping dictionary
    OP_DICT = {
        'EQ': '=', 'NEQ': '!=', 'GT': '>', 'GTE': '>=',
        'LT': '<', 'LTE': '<=', 'LIKE': 'like', 'In': 'in', 'Is': 'is'
    }

    def parse_expression(expr_dict):
        """Parse individual SQL expression based on its class, skipping CASE WHEN details."""
        expr_class = expr_dict['class']
        args = expr_dict['args']

        # Basic column and identifier handling
        if expr_class == 'Column':
            return parse_identifier(args['this'])

        elif expr_class == 'Case':
            # 对于 CASE WHEN，如果有别名，在外部处理，这里返回空字符串
            return ""  # 不解析 CASE WHEN 的具体内容

        elif expr_class == 'If':
            return parse_expression(args['true'])

        elif expr_class in OP_DICT:
            return parse_expression(args['this'])

        # Arithmetic operations
        elif expr_class in ('Add', 'Sub', 'Mul', 'Div'):
            this_expr = parse_expression(args['this'])
            expr = parse_expression(args['expression'])
            operators = {'Add': '+', 'Sub': '-', 'Mul': '*', 'Div': '/'}
            return f"{this_expr} {operators[expr_class]} {expr}"

        # Aggregate functions
        elif expr_class == 'Sum':
            return f"SUM({parse_expression(args['this'])})"

        elif expr_class == 'Count':
            if 'class' in args['this'] and args['this']['class'] == 'Star':
                return "COUNT(*)"
            return f"COUNT({parse_expression(args['this'])})"

        elif expr_class == 'Avg':
            return f"AVG({parse_expression(args['this'])})"

        # Simple expressions
        elif expr_class == 'Star':
            return "*"

        elif expr_class in ('Identifier', 'Literal'):
            return args['this']

        elif expr_class == 'Alias':
            # 如果是别名，返回别名本身而不是解析内部表达式
            alias_name = args['alias']['args']['this']
            inner_expr = parse_expression(args['this'])
            return f"{inner_expr} AS {alias_name}" if inner_expr else alias_name

        elif expr_class == 'Paren':
            return f"({parse_expression(args['this'])})"

        elif expr_class == 'Min':
            return f"MIN({parse_expression(args['this'])})"

        elif expr_class == 'Max':
            return f"MAX({parse_expression(args['this'])})"

        raise ValueError(f"Unsupported expression class: {expr_class}")

    def parse_identifier(identifier_dict):
        """Parse SQL identifier."""
        return identifier_dict['args']['this']

    # Parse SQL and process expressions
    select = sqlglot.parse_one(sql).find(exp.Select)
    expressions = select.expressions

    if not expressions:
        return ""

    # Build result string with proper comma separation
    return ", ".join(parse_expression(expr.dump()) for expr in expressions)


def find_all_col(sql: str):
    """Find all column names in SQL."""
    cols = sqlglot.parse_one(sql).find_all(exp.Column)
    col_list = [col.dump()['args']['this']['args']['this'] for col in cols]
    return col_list


from sqlglot import parse_one, exp


def get_table_names(sql_query: str) -> list[str]:
    # 解析 SQL 查询为抽象语法树 (AST)
    parsed = parse_one(sql_query)

    # 查找所有 Table 类型的节点并提取表名
    table_names = []
    for table in parsed.find_all(exp.Table):
        # table.name 获取表名，不包括数据库或别名
        table_name = table.name
        if table_name not in table_names:  # 去重
            table_names.append(table_name)

    return table_names





# 测试代码
if __name__ == "__main__":
    sql = "SELECT SUM(A+B) + SUM(CASE id WHEN '10' THEN 'AA' ELSE 'DD' END)- SUM(CASE id1 WHEN '10' THEN 'AAA' ELSE 'DDD' END) FROM table1 WHERE id=1;"
    sql = "SELECT (p.price - ps.supply_price) * p.quantity / p.boss_number AS profit FROM products p JOIN product_suppliers ps ON p.product_id = ps.product_id WHERE p.name = '莫辛纳甘' GROUP BY p.name, ps.supply_price, p.boss_number;"

    # 这些情况不会发生
    sql = "SELECT COUNT(*),COUNT(CASE WHEN p.name = 'chicken egg' THEN 0 ELSE p.price END) AS total_chicken_egg_price, AVG(CASE WHEN p.name = 'duck egg' THEN p.price ELSE 0 END) AS total_duck_egg_price, SUM(CASE WHEN p.name = 'chicken egg' THEN p.price ELSE 0 END) + SUM(CASE WHEN p.name = 'duck egg' THEN p.price ELSE 0 END) AS total_egg_price FROM products p;"
    # sql = "SELECT p.price, p.quantity, ps.supply_price, (p.price - ps.supply_price) AS profit_per_unit, (p.price - ps.supply_price) * p.quantity AS total_profit FROM products p JOIN product_suppliers ps ON p.product_id = ps.product_id WHERE p.name = '莫幸纳甘';"

    # select parse
    select_formula = select_parse(sql)
    print("#"*25, "parse result", "#"*25)
    print("sql statement:", sql, "\n")
    print("select_formula:", select_formula, "\n")

    # group parse
    group_col_list = group_parse(sql)
    print("group parse:", group_col_list, "\n")

    # index_dims parse
    index_dims = index_dims_parse(sql)
    print("index_dims:", index_dims, "\n")

    # where parse
    wh_sql = "SELECT * FROM orders WHERE YEAR(date) = '2025' AND id IN (1000,2555) AND YEAR(date) = '2024' AND MONTH(date) >= 5 AND e.deptno NOT <= 10000 AND NOT(d.ename LIKE 'UZI%') AND DAY(date) <= 128 AND SECOND(date) >= 2568 AND id IS NOT NULL;"
    where_formula = where_parse(wh_sql)
    if len(where_formula) > 1:
        where_formula = [{'combine': 'and', 'groups': where_formula}]
    else:
        where_formula = where_formula[0]
    print("where_formula:", where_formula, "\n")

    # having parse
    having_sql = "SELECT department, SUM(salary) FROM employees GROUP BY department HAVING SUM(salary) >= 5000;"
    filter_index = filter_index_parse(having_sql)
    print("filter_index:", filter_index, "\n")
    
