import json
from typing import List, Dict, Any
import numpy as np
from pgvector.rag import hybrid_search
from llm.llm_chat import get_embeddings


# 针对包含字段值表的向量搜索
# TODO:后续可以根据表中对字段的类型的处理，将输出的条件添加上合适的类型
def vector_search(
        pgvector_client: Any,
        table_name: str,
        value: str,
        col_name: str,
        distance: float
) -> str:
    """封装向量搜索逻辑"""
    embedding = np.array(get_embeddings(value)["data"][0]['embedding'])
    # print(f"向量搜索的嵌入向量为：{embedding}")
    search_dict = {"embedding_value_name": embedding}
    rank_rule = {"type": "weighted", "rank_rule": {'embedding_value_name': 1.0}}

    search_result = hybrid_search(
        pgvector_client=pgvector_client,
        table_name="condition",
        vec_dict=search_dict,
        rank_dict=rank_rule,
        out_filed=["value_name"],
        topk=1,
        expr=f"table_name = '{table_name}' and column_name = '{col_name}'",
        partition_name=9888,
        metric_type="cosine"
    )

    try:
        search_data = json.loads(json.dumps(search_result, ensure_ascii=False))
        # print(f"搜索结果为：{search_data}")
        valid_results = [item["value_name"] for item in search_data if item.get("distance", 1.0) <= distance]
        return valid_results[0] if valid_results else value
    except (json.JSONDecodeError, IndexError, KeyError):
        return value


def process_filter_node(
        node: Dict[str, Any],
        pgvector_client: Any,
        table_name: str,
        distance: float = 0.2
) -> Dict[str, Any]:
    """递归处理单个条件节点"""
    new_node = {'join_type': node.get('join_type', 'and')}

    if 'children' in node:
        new_node['children'] = [
            process_filter_node(child, pgvector_client, table_name, distance)
            for child in node['children']
        ]

    elif 'filter' in node:
        filter_data = node['filter']
        new_filter = {
            'type': filter_data.get('type', '='),
            'col_name': filter_data.get('col_name', ''),
            'if_not': filter_data.get('if_not', False),
            'start': filter_data.get('start', ''),
            'end': filter_data.get('end', ''),
            'value': filter_data.get('value', ''),
            # 保留原始的 is_alias 和 case_when 字段
            'is_alias': filter_data.get('is_alias', False),
            'case_when': filter_data.get('case_when', [])
        }

        # 只有当 is_alias 为 False 时才进行向量搜索
        if not new_filter.get('is_alias', False):
            if new_filter['value']:
                new_filter['value'] = vector_search(
                    pgvector_client, table_name, new_filter['value'],
                    new_filter['col_name'], distance
                )

            # 如果 type 为 between，处理 start 和 end
            if new_filter['type'] == 'between':
                if new_filter['start']:
                    new_filter['start'] = vector_search(
                        pgvector_client, table_name, new_filter['start'],
                        new_filter['col_name'], distance
                    )
                if new_filter['end']:
                    new_filter['end'] = vector_search(
                        pgvector_client, table_name, new_filter['end'],
                        new_filter['col_name'], distance
                    )

        return {'filter': new_filter}
    return new_node


def process_filter_conditions(
        conditions: List[Dict[str, Any]],
        pgvector_client: Any,
        table_name: str,
        distance: float = 0.2
) -> List[Dict[str, Any]]:
    """
    处理过滤条件，支持多层嵌套结构和between类型。
    当 filter 中的 is_alias 为 True 时，不进行向量搜索。

    Args:
        conditions: 输入的条件列表
        pgvector_client: pgvector客户端实例
        table_name: 表名
        distance: 向量搜索距离阈值

    Returns:
        处理后的条件列表
    """
    if not conditions:
        return []
    return [
        process_filter_node(condition, pgvector_client, table_name, distance)
        for condition in conditions
    ]
