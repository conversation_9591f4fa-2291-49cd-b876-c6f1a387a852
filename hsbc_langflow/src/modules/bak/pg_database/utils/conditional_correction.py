import json
from typing import List, Dict, Any
import numpy as np
from modules.pg_database.pgvector.rag import hybrid_search
from modules.pg_database.llm.llm_chat import get_embeddings
from utils.db.get_all_content import get_all_content
from utils.common.logger_util import logger
from utils.db.get_col_id import get_col_id_from_mysql

def vector_search(
        pgvector_client: Any,
        mysql_client: Any,
        table_name: str,
        value: str,
        col_name: str,
        distance: float = 0.2,
        model_id: str = ""
) -> str:
    
    def extract_where_value(data):
        # 检查是否有no_type键
        if 'no_type' not in data:
            logger.warning("警告：搜索结果中缺少'no_type'键")
            raise ValueError("搜索结果缺少'where_value'字段'no_type'")
        
        # 如果no_type列表为空
        if not data['no_type']:
            logger.warning("警告：搜索结果中'no_type'为空列表")
            raise ValueError("搜索结果缺少'where_value'字段'no_type'数据")
        
        # 如果有多个记录，只取第一个
        if len(data['no_type']) > 1:
            logger.warning(f"警告：搜索结果包含{len(data['no_type'])}个记录，只取第一个")
        
        # 提取where_value
        where_value = data['no_type'][0].get('where_value')
        if where_value is None:
            logger.warning("警告：搜索结果缺少'where_value'字段")
            raise ValueError("搜索结果缺少'where_value'字段'where_value'数据")
        
        return where_value
    
    partition_name = None
    try:
        # Use MySQL lookup to get col_id for partition construction
        # Note: table_name here might have "table_" prefix depending on when process_filter_conditions is called.
        # Assuming it's the original table name passed from process_sql_query.
        col_id_dict = {
            "adm_lon_varoius_loan_purpose":"98",
            "adm_lon_varoius_term_type":"87",
            "adm_lon_varoius_business_type":"72",
            "adm_lon_varoius_is_cbirc_loan":"137",
            "adm_lon_varoius_is_kownledge_industry":"116",
            "adm_lon_varoius_digital_industry_type":"115",
            "adm_lon_varoius_is_high_tech_industry":"114"
            }
        col_info_dict = get_col_id_from_mysql(mysql_client, col_name)
        col_name = col_info_dict['col_name']
        col_id=col_id_dict[col_name]
        if col_id:
            # Construct partition name using the found col_id
            # Assuming the format: meta#model_id.table_name#col_id
            # partition_name = f'meta#{model_id}.{table_name}#{col_id}'
            partition_name = f'meta#1.adm_lon_varoius#{col_id}'
            logger.info(f"Constructed partition name for value vector search: {partition_name}")
        else:
            # If col_id lookup fails, we cannot determine the partition.
            # Log and return the original value.
            logger.warning(f"MySQL lookup failed for col_id of '{col_name}' in table '{table_name}'. Cannot perform vector search for value correction. Returning original value: {value}")
            return value
        
        # Proceed with get_all_content using the constructed partition name
        search_results = get_all_content(
            pg_client=pgvector_client,
            mysql_client=mysql_client,
            mysql_name='where_info',
            partition=partition_name,
            serach_content=value,
            type=[],
            topk=1
        )
        search_data = json.loads(json.dumps(search_results, ensure_ascii=False))
        valid_result = extract_where_value(search_data)
        logger.info(f"Vector search corrected value '{value}' to '{valid_result}' for column '{col_name}'.")
        return valid_result
    except Exception as e:  # Catch potential errors during get_all_content or JSON processing
        logger.warning(f"警告：搜索结果处理错误 (using partition: {partition_name}): {e}，返回原始值: {value}", exc_info=True)
        return value

def process_filter_node(
        node: Dict[str, Any],
        pgvector_client: Any,
        mysql_client: Any,
        table_name: str,
        distance: float = 0.2,
        model_id: str = ""
) -> Dict[str, Any]:
    """递归处理单个条件节点"""
    new_node = {'combine': node.get('combine', 'and')}

    if 'groups' in node:
        new_node['groups'] = [
            process_filter_node(child, pgvector_client, mysql_client, table_name, distance, model_id)
            for child in node['groups']
        ]

    elif 'filter' in node:
        filter_data = node['filter']
        new_filter = {
            'op': filter_data.get('op', '='),
            'modelColId': filter_data.get('modelColId', ''),
            'if_not': filter_data.get('if_not', False),
            'start': filter_data.get('start', ''),
            'end': filter_data.get('end', ''),
            'val_list': filter_data.get('val_list', ['']),
            'is_alias': filter_data.get('is_alias', False),
            'case_when': filter_data.get('case_when', [])
        }

        # 只有当 is_alias 为 False 时才进行向量搜索
        if not new_filter.get('is_alias', False):
            if new_filter['val_list'] and new_filter['val_list'][0]:
                new_filter['val_list'][0] = vector_search(
                    pgvector_client, 
                    mysql_client,
                    table_name, 
                    new_filter['val_list'][0],
                    new_filter['modelColId'],
                    distance,
                    model_id
                )

            # 如果 op 为 between，处理 start 和 end
            if new_filter['op'] == 'between':
                if new_filter['start']:
                    new_filter['start'] = vector_search(
                        pgvector_client, 
                        mysql_client,
                        table_name, 
                        new_filter['start'],
                        new_filter['modelColId'],
                        distance,
                        model_id
                    )
                if new_filter['end']:
                    new_filter['end'] = vector_search(
                        pgvector_client, 
                        mysql_client,
                        table_name, 
                        new_filter['end'],
                        new_filter['modelColId'],
                        distance,
                        model_id
                    )

        return {'filter': new_filter}

    return new_node

def process_filter_conditions(
        conditions: List[Dict[str, Any]],
        pgvector_client: Any,
        mysql_client: Any,
        table_name: str,
        distance: float = 0.2,
        model_id: str = ""
) -> List[Dict[str, Any]]:
    """
    处理过滤条件，支持多层嵌套结构和between类型。
    当 filter 中的 is_alias 为 True 时，不进行向量搜索。

    Args:
        conditions: 输入的条件列表
        pgvector_client: pgvector客户端实例
        table_name: 表名
        distance: 向量搜索距离阈值

    Returns:
        处理后的条件列表
    """
    if not conditions:
        return []

    return [
        process_filter_node(condition, pgvector_client, mysql_client, table_name, distance, model_id)
        for condition in conditions
    ]