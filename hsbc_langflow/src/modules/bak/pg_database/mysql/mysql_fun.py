import pymysql
from pymysql import MySQLError
from DBUtils.PooledDB import PooledDB
from utils.common.logger_util import logger

class MySQLClient(object):
    def __init__(self, host, port, user, password, database):
        logger.info('Connecting to MySQL database...')
        self.pool = PooledDB(
            creator=pymysql,
            maxconnections=5,
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )

    def __new__(cls, *args, **kwargs):
        if not hasattr(cls, '__instance'):
            cls.__instance = object.__new__(cls)
        return cls.__instance

    def get_connection(self):
        return self.pool.connection()

    def close_connection(self, connection):
        connection.close()

    def execute_query(self, query, params=None):
        connection = self.get_connection()
        cursor = connection.cursor()
        try:
            cursor.execute(query, params)
            results = cursor.fetchall()
            connection.commit()
            return results
        except MySQLError as e:
            logger.error(f"Error: {e}")
            raise e
        finally:
            cursor.close()
            self.close_connection(connection)

    def execute_update(self, query, params=None):
        connection = self.get_connection()
        cursor = connection.cursor()
        try:
            cursor.execute(query, params)
            connection.commit()
            return cursor.rowcount
        except MySQLError as e:
            logger.error(f"Error: {e}")
            raise e
        finally:
            cursor.close()
            self.close_connection(connection)

    def batch_execute(self, queries_and_params):
        connection = self.get_connection()
        cursor = connection.cursor()
        try:
            results = []
            rowcount = 0
            for query, params in queries_and_params:
                cursor.execute(query, params)
                if cursor.description:
                    results.append(cursor.fetchall())
                else:
                    rowcount += cursor.rowcount
            connection.commit()
            if results:
                return results
            else:
                return rowcount
        except MySQLError as e:
            logger.error(f"Error: {e}")
            raise e
        finally:
            cursor.close()
            self.close_connection(connection)

    def batch_insert(self, queries_and_values):
        return self.batch_execute(queries_and_values)

    def batch_query(self, queries_and_params):
        return self.batch_execute(queries_and_params)

    def batch_delete(self, queries_and_params):
        return self.batch_execute(queries_and_params)

    def close(self):
        """
        Closes the connection pool and releases all database connections.
        Safe to call multiple times (idempotent).
        """
        # 确保 close 方法本身是健壮的
        try:
            if hasattr(self, 'pool') and self.pool is not None:
                self.pool.close()
                self.pool = None
                logger.info("Connection pool closed successfully.")
        except Exception as e:
            logger.error(f"Error closing pool in close method: {e}")
            self.pool = None

# if __name__ == '__main__':
#     host = 'localhost'
#     port = 3306
#     user = 'root'
#     password = '181228'
#     database = 'memory'
#
#     mysql_client = MySQLClient(host, port, user, password, database)
#
#     # 示例批量插入不同的查询
#     insert_queries_and_values = [
#         ("INSERT INTO test1 (user_id, session_id, request_id) VALUES (%s, %s, %s)", ("89", "19", "12")),
#         ("INSERT INTO test (id, sex) VALUES (%s, %s)", (2, '男'))
#     ]
#     x = mysql_client.batch_insert(insert_queries_and_values)
#     print(x)

# # 示例批量查询不同的查询
# query_and_params = [
#     ("SELECT * FROM test1 WHERE user_id = %s", ("89",)),
#     ("SELECT * FROM test WHERE id = %s and sex = %s", (1,'男'))
# ]
# results = mysql_client.batch_query(query_and_params)
# print(results)
#
# # 示例批量删除不同的查询
# delete_queries_and_params = [
#     ("DELETE FROM test1 WHERE user_id = %s", ("89",)),
#     ("DELETE FROM test WHERE id = %s ", (2,))
# ]
# results =mysql_client.batch_delete(delete_queries_and_params)
# print(results)
