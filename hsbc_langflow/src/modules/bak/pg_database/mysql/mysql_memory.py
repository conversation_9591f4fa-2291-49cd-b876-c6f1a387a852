from modules.pg_database.mysql.mysql_fun import MySQLClient


class MySQLClientExtended(MySQLClient):
    def __init__(self, host, port, user, password, database):
        super().__init__(host, port, user, password, database)

    def _build_condition(self, data_dict: dict, values: list) -> str:
        """递归构建 WHERE 条件，返回 SQL 片段，并收集绑定值"""
        if "conditions" not in data_dict or not data_dict["conditions"]:
            raise ValueError("Invalid data_dict: 'conditions' missing or empty")

        logic = data_dict.get("logic", "AND").upper()
        if logic not in ["AND", "OR"]:
            raise ValueError(f"Invalid logic operator: {logic}")

        conditions = []
        for item in data_dict["conditions"]:
            if "col_name" in item and "col_val" in item:
                if item.get("type") == "in":
                    # 处理 IN 条件
                    if not isinstance(item["col_val"], (list, tuple)) or not item["col_val"]:
                        raise ValueError(f"col_val for IN condition must be a non-empty list, got {item['col_val']}")
                    placeholders = ", ".join(["%s"] * len(item["col_val"]))
                    conditions.append(f"{item['col_name']} IN ({placeholders})")
                    values.extend(item["col_val"])  # 展开列表中的值
                else:
                    # 默认等于条件
                    if isinstance(item["col_val"], (list, tuple)):
                        raise ValueError(f"col_val must be a scalar for non-IN condition, got {item['col_val']}")
                    conditions.append(f"{item['col_name']} = %s")
                    values.append(item["col_val"])
            elif "conditions" in item:
                # 嵌套条件
                sub_condition = self._build_condition(item, values)
                conditions.append(f"({sub_condition})")
            else:
                raise ValueError(f"Invalid condition format: {item}")

        return f" {logic} ".join(conditions)

    def batch_operation(self, data: dict):
        results = []
        table_name = data["table_name"]

        if data["op_type"] == 'INSERT':
            for item in data["data_dict"]:
                fields = ", ".join(item.keys())
                placeholders = ", ".join(["%s"] * len(item))
                sql = f"INSERT INTO {table_name} ({fields}) VALUES ({placeholders})"
                values = tuple(item.values())
                results.append((sql, values))
            result = self.batch_insert(results)
            result = f'INSERT SUCCESS {result} data'

        elif data["op_type"] == 'DELETE':

            for item in data["data_dict"]:
                condition = " AND ".join([f"{k} = %s" for k in item.keys()])

                sql = f"DELETE FROM {table_name} WHERE {condition}"

                values = tuple(item.values())

                results.append((sql, values))

            # print(results)

            result = self.batch_delete(results)

            result = f'SUCCESS DELETE  {result} data'

        elif data["op_type"] == 'SELECT':
            limit = data.get("limit", 1000)
            order_column = data.get("order_column", None)
            desc_order = data.get("desc_order", False)

            values = []
            condition = self._build_condition(data["data_dict"], values)
            sql = f"SELECT * FROM {table_name} WHERE {condition}"
            if order_column:
                sql += f" ORDER BY {order_column} {'DESC' if desc_order else 'ASC'}"
            sql += f" LIMIT {limit}"
            # print(f"SQL: {sql}, Values: {values}")  # 调试输出
            results.append((sql, tuple(values)))
            result = self.batch_query(results)

        else:
            result = "Invalid operation type"

        return result

# if __name__ == '__main__':
#     pg_config = {
#         'host': '**************',
#         'port': 30103,
#         'user': 'root',
#         'password': 'idea@1008',
#         'database': 'check_process'
#     }
#     # 创建一个 MySQLClientExtended 实例
#     mysql_client = MySQLClientExtended(**pg_config)

# data = {"op_type":"SELECT","limit":4,"table_name":"test_memory","data_dict":[{'user_id':89}]}
# select_data = {"op_type": "SELECT", "limit": 4, "table_name": "test_memory",
#                "data_dict": [{"user_id": "001", "form_id": 0}]}

# data = mysql_client.batch_operation(select_data)
#     data = sorted(data, key=lambda x: int(x['request_id']), reverse=True)
# print(data)
