import openai
import os

# 设置 OpenAI API 密钥
openai.api_key = "your_openai_api_key_here"  # 替换为你的实际 API 密钥


def get_embedding(text, model="text-embedding-ada-002"):
    """
    调用 OpenAI 的 embedding 接口生成文本的嵌入向量。

    参数:
        text (str): 输入的文本。
        model (str): 使用的模型名称，默认为 "text-embedding-ada-002"。

    返回:
        list: 嵌入向量（一个浮点数列表）。
    """
    try:
        # 调用 OpenAI 的 embeddings 接口
        response = openai.Embedding.create(
            input=text,
            model=model
        )

        # 提取嵌入向量
        embedding = response['data'][0]['embedding']
        return embedding

    except Exception as e:
        print(f"Error: {e}")
        return None


# 示例调用
if __name__ == "__main__":
    # 输入文本
    text = "This is an example sentence for generating embeddings."

    # 获取嵌入向量
    embedding = get_embedding(text)

    if embedding:
        print("Embedding vector length:", len(embedding))
        print("Embedding vector (first 10 dimensions):", embedding[:10])