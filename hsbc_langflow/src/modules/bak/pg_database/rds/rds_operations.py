from typing import List, Dict, Any, Optional
from utils.common.logger_util import logger
from utils.db.pg_database.pgvector.pgvector_class import PgVectorClass
from utils.db.pg_database.configs.config import pgvector_config # 导入配置
import psycopg2 # 导入 psycopg2 以便捕获特定数据库错误

# 可以在全局或按需创建客户端实例
# 如果 FastAPI 是多进程/线程运行，需考虑连接池或每次请求创建新连接
# pgvector_client = PgVectorClass(**pgvector_config)

# -- 保留原注释供参考 --
# # 数据库连接配置 (需要您根据实际情况修改)
# # import psycopg2
# # from psycopg2 import pool
# # try:
# #     db_pool = pool.SimpleConnectionPool(
# #         minconn=1,
# #         maxconn=10,
# #         user="your_db_user",
# #         password="your_db_password",
# #         host="your_db_host",
# #         port="your_db_port",
# #         database="your_db_name"
# #     )
# # except Exception as e:
# #     logger.error(f"无法连接到数据库: {e}")
# #     db_pool = None

def get_model_info_by_id(model_id: str) -> List[Dict[str, Any]]:
    """
    根据 model_id 从数据库获取模型相关的全量字段信息。
    使用 PgVectorClass 连接并查询 'data_model' 表。

    Args:
        model_id: 模型ID

    Returns:
        包含模型字段信息的字典列表，每个字典代表一个字段。
        如果找不到或发生错误，则返回空列表。
        
    Raises:
        ConnectionError: 如果无法连接到数据库。
        psycopg2.Error: 如果查询过程中发生数据库错误。
        Exception: 其他潜在错误。
    """
    logger.info(f"开始查询 model_id: {model_id} 的信息 (表: data_model)")
    pgvector_client: Optional[PgVectorClass] = None
    
    try:
        # 创建 PgVectorClass 实例并连接数据库
        pgvector_client = PgVectorClass(**pgvector_config)
        
        # 设置目标表
        table_name = "data_model"
        pgvector_client.set_table(table_name)
        
        # --- 实际数据库查询逻辑 ---
        # 确定用于过滤的字段名，这里假设是 'model_id_column'
        # !! 重要: 请将 'model_id_column' 替换为您数据库中实际存储 model_id 的列名 !!
        filter_field = "table_code" 
        
        # 构造查询表达式，精确匹配 model_id
        # 如果 model_id 存储为字符串，需要加引号
        # 如果 model_id 存储为数字，则不需要
        # 这里假设 model_id 存储为字符串
        query_expr = f"{filter_field} == '{model_id}'" 
        
        # 或者，如果需要基于 table_code 过滤（假设格式为 model_id.table_name）
        # query_expr = f"table_code LIKE '{model_id}.%'" 

        # 定义需要查询返回的字段
        # 请根据您 'data_model' 表的实际列名进行调整
        output_fields = [
            "col_code", 
            "table_code", 
            "col_name", 
            "col_name_cn", 
            "col_desc", 
            "col_type", 
            "create_at", # 确保数据库返回的是可序列化格式（如字符串或时间戳）
            "update_at", # 同上
            "col_data_example"
        ]

        # 设置查询参数
        query_schema = {
            "filter": query_expr,
            "output_fields": output_fields,
            # "limit": 100 # 如果结果可能很多，可以考虑添加 limit
        }
        pgvector_client.set_query_schema(query_schema)

        # 执行查询
        logger.info(f"执行查询: table='{table_name}', filter='{query_expr}', output_fields={output_fields}")
        results = pgvector_client.query()

        if results is False:
            # query 方法在发生错误时可能返回 False 或抛出异常
            logger.error(f"查询 model_id: {model_id} 时出错，query() 返回 False")
            return []
        elif results is None:
             # 如果 query 方法在没有找到匹配项时返回 None (请检查 PgVectorClass 实现)
             logger.warning(f"未找到 model_id: {model_id} 的信息")
             return []
        elif isinstance(results, list):
             # 检查返回的是否是列表
             logger.info(f"为 model_id: {model_id} 查询到 {len(results)} 条记录")
             # 直接返回查询结果，因为 output_fields 已经指定了所需的列
             # 如果 PgVectorClass.query() 返回的不是字典列表，需要在这里进行转换
             # 确保时间戳等字段格式正确
             formatted_results = []
             for record in results:
                 # 假设 record 已经是字典
                 # 进行必要的格式转换，例如日期时间
                 if 'create_at' in record and record['create_at']:
                     record['create_at'] = str(record['create_at']) # 转换为字符串
                 if 'update_at' in record and record['update_at']:
                     record['update_at'] = str(record['update_at']) # 转换为字符串
                 # 确保 is_editable 是布尔值
                 if 'is_editable' in record:
                     record['is_editable'] = bool(record['is_editable']) # 转换为布尔值
                 formatted_results.append(record)

             return formatted_results
        else:
             logger.error(f"查询 model_id: {model_id} 返回了意外的类型: {type(results)}")
             return []

    except psycopg2.Error as db_err:
        logger.error(f"数据库查询 model_id: {model_id} 时出错: {db_err}", exc_info=True)
        # 可以选择向上抛出特定异常或返回空列表
        # raise db_err 
        return []
    except ConnectionError as conn_err:
         logger.error(f"数据库连接失败: {conn_err}", exc_info=True)
         # raise conn_err
         return []
    except Exception as e:
        logger.error(f"查询 model_id: {model_id} 时发生未知错误: {e}", exc_info=True)
        # raise e
        return []
    finally:
        # 确保断开连接
        if pgvector_client:
            logger.debug(f"断开 model_id: {model_id} 查询的数据库连接")
            pgvector_client.disconnect_postgres()


