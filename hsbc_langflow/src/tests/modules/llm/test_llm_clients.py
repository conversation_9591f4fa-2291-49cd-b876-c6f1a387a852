import pytest
from unittest.mock import MagicMock

# 模块导入路径需要根据你的项目结构进行调整
from modules.llm.clients import set_provider_factory, get_llm_provider, reset_provider_registry
from utils.llm.llm_util import llm_provider

# 定义一个用于测试的 Mock LLM Provider
class MockLLMProvider:
    def __init__(self, name="mock_llm"):
        self.name = name
        self.invoked = False

    def invoke(self, query: str) -> str:
        self.invoked = True
        return f"Response from {self.name} for query: '{query}'"

@pytest.fixture(autouse=True)
def cleanup_registry():
    """在每次测试后自动重置注册表，确保测试隔离性"""
    yield
    reset_provider_registry()

def test_llm_provider_factory_injection():
    """
    测试:
    1. 能否成功注入一个自定义的 LLM Provider 工厂。
    2. get_llm_provider() 是否返回由该工厂创建的实例。
    3. 全局的 llm_provider (LazyProvider) 是否能正确代理到该实例。
    """
    # 1. 准备一个 Mock 实例和工厂函数
    mock_instance = MockLLMProvider()
    
    # 2. 使用 set_provider_factory 进行注入
    set_provider_factory('llm', lambda: mock_instance)
    
    # 3. 测试 get_llm_provider
    # 第一次调用，应该通过工厂创建并缓存实例
    provider_from_getter = get_llm_provider()
    assert isinstance(provider_from_getter, MockLLMProvider)
    assert provider_from_getter is mock_instance
    assert provider_from_getter.name == "mock_llm"
    
    # 再次调用，应该返回缓存的实例
    provider_from_getter_cached = get_llm_provider()
    assert provider_from_getter_cached is mock_instance

    # 4. 测试全局的 llm_provider (LazyProvider)
    # 第一次访问属性，将触发 _get_instance()，它会调用 get_llm_provider()
    assert not mock_instance.invoked
    response = llm_provider.invoke("hello")
    assert mock_instance.invoked
    assert response == "Response from mock_llm for query: 'hello'"
    
    # 访问代理对象的属性
    assert llm_provider.name == "mock_llm"

def test_reset_provider_registry():
    """
    测试 reset_provider_registry 是否能清除缓存。
    """
    # 1. 注入一个 mock provider 并获取它，使其被缓存
    mock_instance1 = MockLLMProvider(name="first")
    set_provider_factory('llm', lambda: mock_instance1)
    provider1 = get_llm_provider()
    assert provider1.name == "first"

    # 2. 重置注册表
    reset_provider_registry()

    # 3. 注入一个新的 mock provider
    mock_instance2 = MockLLMProvider(name="second")
    set_provider_factory('llm', lambda: mock_instance2)
    provider2 = get_llm_provider()
    assert provider2.name == "second"

    # 确保我们获取到的是新的实例，而不是缓存的旧实例
    assert provider1 is not provider2 