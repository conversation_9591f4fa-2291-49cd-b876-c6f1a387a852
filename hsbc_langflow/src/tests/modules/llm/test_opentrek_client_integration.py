import hydra
import sys
import os
import asyncio
from omegaconf import DictConfig

# Setup paths for standalone execution
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# Import the global config singleton and other necessary classes
from utils.common.config_util import config
from base.model_serve.model_runtime.model_providers.llm_model.opentrek_llm import OpenTrekLLM
from base.model_serve.model_runtime.entities.message_entities import PromptMessage

async def main_async(cfg: DictConfig):
    """
    Tests the refactored OpenTrekLLM as a standalone client,
    adhering to the project's config patterns.
    """
    # Initialize the global config singleton, following the project's established pattern
    config.__init__(cfg)
    print("Hydra config loaded and global config initialized.")

    print("\n--- Running New OpenTrekLLM Integration Test ---")
    
    # 1. Instantiate the client directly using Hydra.
    #    The correct path to the llm config block is cfg.model.llm
    try:
        print("\nInstantiating OpenTrekLLM client from config...")
        llm_client_instance: OpenTrekLLM = hydra.utils.instantiate(cfg.model.llm)
        print(f"✅ Client instantiated successfully: {type(llm_client_instance)}")
        print(f"   - Base URL: {llm_client_instance.base_url}")
    except Exception as e:
        print(f"❌ Client instantiation failed: {e}")
        import traceback
        traceback.print_exc()
        return

    # Helper function to inspect the connection pool
    def log_connection_pool_status(connector, context_message):
        if connector:
            print(f"\n--- Connection Pool Status ({context_message}) ---")
            # Check if the connector itself is closed
            is_closed = getattr(connector, 'closed', False)
            print(f"Connector state: {'Closed' if is_closed else 'Open'}")

            # NOTE: _conns is an internal attribute, used here for demonstration purposes.
            # Its structure is a dictionary mapping a connection key to a deque of connections.
            conns_info = getattr(connector, '_conns', {})
            total_pooled = sum(len(conns) for conns in conns_info.values())
            print(f"Total idle connections in pool: {total_pooled}")
            if total_pooled > 0:
                print("Connections per host:")
                for key, conns_deque in conns_info.items():
                    # The key is a named tuple containing host, port, etc.
                    print(f"  - Host: {getattr(key, 'host', 'N/A')}:{getattr(key, 'port', 'N/A')}, Idle Connections: {len(conns_deque)}")
            print("-------------------------------------------------")
        else:
            print("\n--- Connector not available ---")

    # Get a reference to the connector before the session is managed by async with
    # This allows us to inspect its state even after the session is closed.
    connector = getattr(llm_client_instance._async_session, 'connector', None)

    async with llm_client_instance as llm_client:
        # 2. Get parameters from the global `config` object for invoke calls.
        #    This makes the test truly config-driven.
        prompt_messages = [PromptMessage(role="user", content="你好，请用中文做一个不超过50字的自我介绍")]
        model_name_from_config = config.llm.model_name
        model_parameters_from_config = config.llm.model_parameters
        
        # --- Test 1: Synchronous Streaming ---
        try:
            print(f"\n--- 1. Testing Synchronous Streaming (Model: {model_name_from_config}) ---")
            result_generator = llm_client.invoke(
                model=model_name_from_config,
                credentials={}, # Kept for compatibility
                prompt_messages=prompt_messages,
                model_parameters=model_parameters_from_config,
                stream=True
            )
            
            full_response = ""
            print("Response: ", end="")
            for chunk in result_generator:
                content = chunk.delta.message.content
                print(content, end="", flush=True)
                full_response += content
            print("\n✅ Sync Streaming test PASSED.")
            assert len(full_response) > 0

        except Exception as e:
            print(f"\n❌ Sync Streaming test FAILED: {e}")
            import traceback
            traceback.print_exc()

        # --- Test 2: Synchronous Non-Streaming ---
        try:
            print(f"\n--- 2. Testing Synchronous Non-Streaming (Model: {model_name_from_config}) ---")
            result = llm_client.invoke(
                model=model_name_from_config,
                credentials={},
                prompt_messages=prompt_messages,
                model_parameters=model_parameters_from_config,
                stream=False
            )
            print(f"Response: {result.message.content}")
            print("✅ Sync Non-Streaming test PASSED.")
            assert len(result.message.content) > 0

        except Exception as e:
            print(f"\n❌ Sync Non-Streaming test FAILED: {e}")
            import traceback
            traceback.print_exc()

        # Log pool status before async tests
        log_connection_pool_status(getattr(llm_client._async_session, 'connector', None), "Before any async calls")

        # --- Test 3: Asynchronous Streaming ---
        try:
            print(f"\n--- 3. Testing Asynchronous Streaming (Model: {model_name_from_config}) ---")
            async_result_generator = await llm_client.ainvoke(
                model=model_name_from_config,
                credentials={},
                prompt_messages=prompt_messages,
                model_parameters=model_parameters_from_config,
                stream=True
            )

            full_response = ""
            print("Response: ", end="")
            async for chunk in async_result_generator:
                content = chunk.delta.message.content
                print(content, end="", flush=True)
                full_response += content
            print("\n✅ Async Streaming test PASSED.")
            assert len(full_response) > 0

        except Exception as e:
            print(f"\n❌ Async Streaming test FAILED: {e}")
            import traceback
            traceback.print_exc()

        # Log pool status after streaming test
        log_connection_pool_status(getattr(llm_client._async_session, 'connector', None), "After async streaming call")

        # --- Test 4: Asynchronous Non-Streaming ---
        try:
            print(f"\n--- 4. Testing Asynchronous Non-Streaming (Model: {model_name_from_config}) ---")
            async_result = await llm_client.ainvoke(
                model=model_name_from_config,
                credentials={},
                prompt_messages=prompt_messages,
                model_parameters=model_parameters_from_config,
                stream=False
            )
            print(f"Response: {async_result.message.content}")
            print("✅ Async Non-Streaming test PASSED.")
            assert len(async_result.message.content) > 0
            
        except Exception as e:
            print(f"\n❌ Async Non-Streaming test FAILED: {e}")
            import traceback
            traceback.print_exc()
        
        # Log pool status after non-streaming test
        log_connection_pool_status(getattr(llm_client._async_session, 'connector', None), "Inside 'async with', before exit")

    # Now, check the status *after* the `async with` block has exited.
    # This will demonstrate that the __aexit__ method has correctly
    # closed the connector and cleared the connection pool.
    log_connection_pool_status(connector, "After 'async with' block has fully exited")

@hydra.main(config_path="../../../config", config_name="config", version_base=None)
def run_tests(cfg: DictConfig):
    asyncio.run(main_async(cfg))

if __name__ == "__main__":
    run_tests() 