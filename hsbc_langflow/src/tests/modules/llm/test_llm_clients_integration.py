import hydra
import sys
import os

# Setup paths for standalone execution
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入全局 config 实例
from utils.common.config_util import config
from utils.llm.llm_util import llm_provider
from modules.llm.clients import reset_provider_registry
# 导入 PromptMessage 用于构造真实请求
from base.model_serve.model_runtime.entities.message_entities import PromptMessage



@hydra.main(config_path="../../../config", config_name="config", version_base=None)
def main(cfg):
    """
    This function now performs a REAL API call to the configured LLM endpoint.
    """
    config.__init__(cfg)
    print("Hydra config loaded and global config initialized.")

    reset_provider_registry()

    # 从配置中直接读取结构化的 credentials 和 model_parameters
    # 这种方式更简洁，且与配置文件结构保持一致
    model_name = config.llm.model_name
    credentials = config.llm.credentials
    model_parameters = config.llm.model_parameters

    # 构造一个真实的、简单的提示
    prompt_messages = [
        PromptMessage(role="user", content="你好，请用中文做一个自我介绍")
    ]
    
    print(f"Attempting a REAL API call to: {credentials.base_url}")
    print(f"Stream mode is set to: {config.llm.stream}")

    try:
        # 使用从配置中读取的参数字典
        result = llm_provider.invoke_llm(
            tenant_id="1",
            user_id="1",
            provider=credentials.provider, # 从 credentials 中获取 provider
            model= model_name,
            prompt_messages=prompt_messages,
            credentials=credentials,
            model_parameters=model_parameters,
            stream=config.llm.stream
        )

        print("\n✅ Real API call successful!")
        print("--- API Response ---")

        # 根据您的修改，我们假设响应总是流式的
        full_response = ""
        for chunk in result:
            # 流式块的处理逻辑
            if chunk and hasattr(chunk, 'delta') and hasattr(chunk.delta, 'message') and chunk.delta.message.content:
                content = chunk.delta.message.content
                print(content, end='', flush=True)
                full_response += content
        print("\n--- End of Stream ---")
        
        print("--------------------")

    except Exception as e:
        print(f"\n❌ Real API call failed: {e}")
        # 打印更详细的错误信息，帮助调试
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 