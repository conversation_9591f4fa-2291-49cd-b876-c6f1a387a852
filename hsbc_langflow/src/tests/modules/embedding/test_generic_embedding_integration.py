import hydra
import sys
import os
import asyncio
from omegaconf import DictConfig

# Setup paths for standalone execution
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# Import the global config singleton and other necessary classes
from utils.common.config_util import config
from base.model_serve.model_runtime.model_providers.embedding_model.generic_embedding import GenericEmbedding

async def main_async(cfg: DictConfig):
    """
    Tests the GenericEmbedding as a standalone client,
    adhering to the project's config patterns.
    """
    # Initialize the global config singleton
    config.__init__(cfg)
    print("Hydra config loaded and global config initialized.")

    print("\n--- Running GenericEmbedding Integration Test ---")
    
    # 1. Instantiate the client directly using Hydra.
    #    The path to the embedding config block is cfg.model.embedding
    try:
        print("\nInstantiating GenericEmbedding client from config...")
        # Note: Ensure you have a corresponding config block in your hydra files
        # e.g., in `config/model/embedding.yaml`
        embedding_client_instance: GenericEmbedding = hydra.utils.instantiate(cfg.model.embedding)
        print(f"✅ Client instantiated successfully: {type(embedding_client_instance)}")
        print(f"   - Base URL: {embedding_client_instance.base_url}")
    except Exception as e:
        print(f"❌ Client instantiation failed: {e}")
        import traceback
        traceback.print_exc()
        return

    async with embedding_client_instance as client:
        # 2. Get parameters from the global `config` object
        model_name_from_config = config.embedding.model_name
        texts_to_embed = ["Hello world", "你好，世界"]
        
        # --- Test 1: Synchronous call ---
        try:
            print(f"\n--- 1. Testing Synchronous Embedding (Model: {model_name_from_config}) ---")
            result = client.invoke(
                model=model_name_from_config,
                credentials={}, # Kept for compatibility, credentials managed by client
                texts=texts_to_embed,
            )
            
            print(f"✅ Sync call PASSED.")
            print(f"   - Embeddings received: {len(result.embeddings)}")
            print(f"   - Dimension of first embedding: {len(result.embeddings[0]) if result.embeddings else 'N/A'}")
            print(f"   - Usage: {result.usage}")

            assert len(result.embeddings) == len(texts_to_embed)
            assert len(result.embeddings[0]) > 0

        except Exception as e:
            print(f"\n❌ Sync call FAILED: {e}")
            import traceback
            traceback.print_exc()

        # --- Test 2: Asynchronous call ---
        try:
            print(f"\n--- 2. Testing Asynchronous Embedding (Model: {model_name_from_config}) ---")
            async_result = await client.ainvoke(
                model=model_name_from_config,
                credentials={},
                texts=texts_to_embed,
            )

            print(f"✅ Async call PASSED.")
            print(f"   - Embeddings received: {len(async_result.embeddings)}")
            print(f"   - Dimension of first embedding: {len(async_result.embeddings[0]) if async_result.embeddings else 'N/A'}")
            print(f"   - Usage: {async_result.usage}")
            
            assert len(async_result.embeddings) == len(texts_to_embed)
            assert len(async_result.embeddings[0]) > 0

        except Exception as e:
            print(f"\n❌ Async call FAILED: {e}")
            import traceback
            traceback.print_exc()

@hydra.main(config_path="../../../config", config_name="config", version_base=None)
def run_tests(cfg: DictConfig):
    asyncio.run(main_async(cfg))

if __name__ == "__main__":
    run_tests() 