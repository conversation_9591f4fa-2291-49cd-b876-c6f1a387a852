'''
File Created: Tuesday, 11th June 2025 2:03:04 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
'''

"""
数据库客户端实现的集成测试。
注意：这些测试需要实际的数据库连接才能成功运行。
"""

import pytest
from base.db.implementations.rdb.mysql.mysql_sqlalchemy_client import MySQLSQLAlchemyClient
from base.db.implementations.vs.pgvector.pgvector_client import PGVectorClient
from base.db.base.schemas import RDBConnectionConfig, VDBConnectionConfig, RDSTableSchema, CollectionSchema, FieldSchema, ColumnSchema

class TestMySQLClient:
    """MySQL 客户端实现测试"""
    
    @pytest.fixture
    def mysql_client(self):
        """创建测试用的MySQL客户端实例"""
        # 使用测试数据库的配置
        config = RDBConnectionConfig(
            host="**************",
            port=37615,
            user="root",
            password="idea@1008",
            db_name="hsbc_data"
        )
        client = MySQLSQLAlchemyClient(connection=config)
        client.connect()
        yield client
        client.disconnect()
    
    def test_mysql_connection(self, mysql_client):
        """测试MySQL连接是否成功"""
        # 简单查询以验证连接是否正常
        try:
            result = mysql_client.execute_query("SELECT 1 as test")
            assert result == [{'test': 1}]
        except Exception as e:
            pytest.fail(f"MySQL连接测试失败: {str(e)}")
    
    def test_mysql_list_tables(self, mysql_client):
        """测试列出数据库中的表"""
        try:
            tables = mysql_client.list_tables()
            # 只验证返回类型，不验证具体表名
            assert isinstance(tables, list)
            print(f"数据库中的表: {tables}")
        except Exception as e:
            pytest.fail(f"列出表失败: {str(e)}")
    
    def test_mysql_select(self, mysql_client):
        """测试查询功能"""
        # 假设数据库中有某个表，这里用第一个可用的表进行测试
        try:
            tables = mysql_client.list_tables()
            if tables:
                # 选择第一个表进行查询
                table_name = tables[0]
                result = mysql_client.select(table_name, limit=1)
                # 只验证返回结构
                assert isinstance(result, list)
                if result:
                    assert isinstance(result[0], dict)
                print(f"查询 {table_name} 的结果: {result}")
            else:
                pytest.skip("数据库中没有表，跳过测试")
        except Exception as e:
            pytest.fail(f"查询测试失败: {str(e)}")


class TestPGVectorClient:
    """PGVector 客户端实现测试"""
    
    @pytest.fixture
    def pgvector_client(self):
        """创建测试用的PGVector客户端实例"""
        # 使用测试数据库的配置
        config = VDBConnectionConfig(
            host="**************",
            port=30146,
            user="pgvector",
            password="pgvector",
            db_name="postgres"
        )
        client = PGVectorClient(connection=config)
        client.connect()
        yield client
        client.disconnect()
    
    def test_pgvector_connection(self, pgvector_client):
        """测试PGVector连接是否成功"""
        try:
            # 简单查询检查连接是否正常
            with pgvector_client._get_cursor() as cur:
                cur.execute("SELECT 1 as test")
                result = cur.fetchone()
                assert result['test'] == 1
        except Exception as e:
            pytest.fail(f"PGVector连接测试失败: {str(e)}")
    
    def test_pgvector_list_collections(self, pgvector_client):
        """测试列出所有集合"""
        try:
            collections = pgvector_client.list_collections()
            # 只验证返回类型，不验证具体集合名
            assert isinstance(collections, list)
            print(f"数据库中的集合: {collections}")
        except Exception as e:
            pytest.fail(f"列出集合失败: {str(e)}")
    
    def test_pgvector_count(self, pgvector_client):
        """测试计数功能"""
        try:
            collections = pgvector_client.list_collections()
            if collections:
                # 选择第一个集合进行计数测试
                collection_name = collections[0]
                count = pgvector_client.count(collection_name)
                # 只验证返回类型
                assert isinstance(count, int)
                print(f"集合 {collection_name} 中的记录数: {count}")
            else:
                pytest.skip("数据库中没有集合，跳过测试")
        except Exception as e:
            pytest.fail(f"计数测试失败: {str(e)}")


if __name__ == "__main__":
    # 为便于手动运行测试，提供简单的测试入口
    pytest.main(["-xvs", "test_db_clients_impl.py"]) 