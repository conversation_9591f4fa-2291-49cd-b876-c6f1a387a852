'''
File Created: Monday, 9th June 2025 2:46:33 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 8:44:57 am
'''

'''
File Created: Monday, 9th June 2025 8:45:30 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 8:25:18 am
'''

"""
数据库客户端单元测试。
展示如何在不依赖Hydra和实际数据库的情况下测试代码。
"""

import pytest
from unittest.mock import MagicMock, patch
import sys

# 导入被测试的模块
from modules.db.clients import (
    get_rdb_client, 
    get_vdb_client, 
    set_client_factory, 
    reset_client_registry,
    _client_registry
)

class TestDBClients:
    """数据库客户端测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置客户端缓存
        reset_client_registry()
    
    def test_rdb_client_factory(self):
        """测试RDB客户端工厂模式"""
        # 创建一个模拟的RDB客户端
        mock_client = MagicMock()
        mock_client.query.return_value = ["test_result"]
        
        # 设置工厂函数
        set_client_factory('rdb', lambda: mock_client)
        
        # 获取客户端并测试
        client = get_rdb_client()
        
        # 验证客户端是否正确缓存
        assert client is mock_client
        assert "rdb_primary" in _client_registry
        
        # 验证客户端功能
        result = client.query("SELECT * FROM test")
        assert result == ["test_result"]
        mock_client.query.assert_called_once_with("SELECT * FROM test")
    
    def test_vdb_client_factory(self):
        """测试VDB客户端工厂模式"""
        # 创建一个模拟的VDB客户端
        mock_client = MagicMock()
        mock_client.search.return_value = [{"id": 1, "score": 0.95}]
        
        # 设置工厂函数
        set_client_factory('vdb', lambda: mock_client)
        
        # 获取客户端并测试
        client = get_vdb_client()
        
        # 验证客户端是否正确缓存
        assert client is mock_client
        assert "vdb_primary" in _client_registry
        
        # 验证客户端功能
        result = client.search("query vector", limit=1)
        assert result == [{"id": 1, "score": 0.95}]
        mock_client.search.assert_called_once_with("query vector", limit=1)
    
    def test_singleton_pattern(self):
        """测试单例模式是否正常工作"""
        # 创建两个模拟客户端
        mock_client1 = MagicMock()
        mock_client2 = MagicMock()
        
        # 设置工厂函数返回第一个模拟客户端
        set_client_factory('rdb', lambda: mock_client1)
        
        # 获取客户端实例
        client1 = get_rdb_client()
        
        # 这时尝试更改工厂函数，返回第二个模拟客户端
        set_client_factory('rdb', lambda: mock_client2)
        
        # 再次获取客户端实例
        client2 = get_rdb_client()
        
        # 由于设置新工厂时会重置缓存，所以应该返回新的客户端
        assert client1 is mock_client1
        assert client2 is mock_client2
        assert client1 is not client2

# 这是一个模拟服务函数，展示如何在业务逻辑中使用数据库客户端
def user_service_get_user(user_id):
    """
    获取用户信息的服务函数
    
    Args:
        user_id: 用户ID
        
    Returns:
        用户信息字典
    """
    db = get_rdb_client()
    return db.query(f"SELECT * FROM users WHERE id = {user_id}")

# 测试业务逻辑函数
def test_user_service():
    """测试使用数据库客户端的业务逻辑"""
    # 创建模拟客户端
    mock_client = MagicMock()
    mock_client.query.return_value = {"id": 123, "name": "Test User"}
    
    # 设置工厂函数
    set_client_factory('rdb', lambda: mock_client)
    
    # 测试业务逻辑函数
    result = user_service_get_user(123)
    
    # 验证结果
    assert result == {"id": 123, "name": "Test User"}
    mock_client.query.assert_called_once_with("SELECT * FROM users WHERE id = 123")