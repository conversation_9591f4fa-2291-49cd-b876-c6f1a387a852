'''
File Created: Tuesday, 11th June 2025 2:05:04 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
'''

"""
数据库客户端工厂函数与实际实现结合的测试。
这个测试展示如何将工厂模式与实际客户端结合使用。
"""

import pytest
from unittest.mock import patch
from modules.db.clients import (
    get_rdb_client, 
    get_vdb_client, 
    set_client_factory, 
    reset_client_registry
)
from base.db.implementations.rdb.mysql.mysql_sqlalchemy_client import MySQLSQLAlchemyClient
from base.db.implementations.vs.pgvector.pgvector_client import PGVectorClient
from base.db.base.schemas import RDBConnectionConfig, VDBConnectionConfig

class TestDBClientsFactory:
    """数据库客户端工厂函数测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        reset_client_registry()
    
    def test_real_mysql_client_factory(self):
        """测试使用实际MySQL客户端的工厂函数"""
        # 使用lambda创建实际的MySQL客户端
        def create_mysql_client():
            config = RDBConnectionConfig(
                host="**************",
                port=37615,
                user="root",
                password="idea@1008",
                db_name="hsbc_data"
            )
            client = MySQLSQLAlchemyClient(connection=config)
            client.connect()
            return client
        
        # 设置工厂函数
        set_client_factory('rdb', create_mysql_client)
        
        # 获取客户端实例
        client = get_rdb_client()
        
        # 验证客户端类型
        assert isinstance(client, MySQLSQLAlchemyClient)
        
        # 测试简单查询
        try:
            result = client.execute_query("SELECT 1 as test")
            assert result == [{'test': 1}]
        except Exception as e:
            pytest.fail(f"MySQL客户端工厂测试失败: {str(e)}")
        finally:
            # 确保断开连接
            client.disconnect()
    
    def test_real_pgvector_client_factory(self):
        """测试使用实际PGVector客户端的工厂函数"""
        # 使用lambda创建实际的PGVector客户端
        def create_pgvector_client():
            config = VDBConnectionConfig(
                host="**************",
                port=30146,
                user="pgvector",
                password="pgvector",
                db_name="postgres"
            )
            client = PGVectorClient(connection=config)
            client.connect()
            return client
        
        # 设置工厂函数
        set_client_factory('vdb', create_pgvector_client)
        
        # 获取客户端实例
        client = get_vdb_client()
        
        # 验证客户端类型
        assert isinstance(client, PGVectorClient)
        
        # 测试简单查询
        try:
            collections = client.list_collections()
            assert isinstance(collections, list)
        except Exception as e:
            pytest.fail(f"PGVector客户端工厂测试失败: {str(e)}")
        finally:
            # 确保断开连接
            client.disconnect()
    
    def test_client_registry_singleton(self):
        """测试客户端注册表单例行为"""
        # 创建MySQL客户端的工厂函数
        def create_mysql_client():
            config = RDBConnectionConfig(
                host="**************",
                port=37615,
                user="root",
                password="idea@1008",
                db_name="hsbc_data"
            )
            client = MySQLSQLAlchemyClient(connection=config)
            client.connect()
            return client
        
        # 设置工厂函数
        set_client_factory('rdb', create_mysql_client)
        
        # 多次获取客户端实例
        client1 = get_rdb_client()
        client2 = get_rdb_client()
        
        # 验证是否是同一个实例
        assert client1 is client2
        
        # 断开连接
        client1.disconnect()


# 模拟使用客户端的业务服务
class UserService:
    """用户服务，展示如何在业务逻辑中使用数据库客户端"""
    
    @staticmethod
    def list_tables():
        """列出所有表"""
        db = get_rdb_client()
        return db.list_tables()


def test_service_with_real_client():
    """测试使用实际客户端的业务服务"""
    # 设置工厂函数创建实际客户端
    def create_mysql_client():
        config = RDBConnectionConfig(
            host="**************",
            port=37615,
            user="root",
            password="idea@1008",
            db_name="hsbc_data"
        )
        client = MySQLSQLAlchemyClient(connection=config)
        client.connect()
        return client
    
    try:
        # 重置并设置工厂
        reset_client_registry()
        set_client_factory('rdb', create_mysql_client)
        
        # 调用业务服务
        tables = UserService.list_tables()
        
        # 验证结果
        assert isinstance(tables, list)
    except Exception as e:
        pytest.fail(f"业务服务测试失败: {str(e)}")
    finally:
        # 获取客户端并断开连接
        client = get_rdb_client()
        client.disconnect()


if __name__ == "__main__":
    # 为便于手动运行测试，提供简单的测试入口
    pytest.main(["-xvs", "test_db_clients_factory.py"]) 