'''
File Created: Monday, 9th June 2025 2:46:15 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 4:36:56 am
'''

# tests/conftest.py
import pytest
import hydra
from omegaconf import DictConfig, OmegaConf
import sys
import os

# 动态添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    
# 导入我们的全局配置单例
from utils.common.config_util import config, reset_config_for_testing
from utils.common.service_client import reset_service_client_registry

@pytest.fixture(scope="session", autouse=True)
def initialize_hydra_for_tests():
    """
    这是一个Pytest fixture, 它会在整个测试会话（session）开始时自动运行一次。
    它的核心职责是：在任何测试用例执行前，加载Hydra配置并初始化我们的全局config对象。
    """
    print("\n--- Initializing Hydra configuration for test session ---")
    
    # 在会话开始前，重置所有可能存在的旧状态
    reset_config_for_testing()
    reset_service_client_registry()
    
    # 使用Hydra的Compose API，这是在非main函数中加载配置的官方方法。
    with hydra.initialize(config_path="../config", version_base=None):
        cfg = hydra.compose(config_name="config")
        
        # 将Hydra加载并组合好的配置对象注入到我们的全局单例中。
        config.initialize(cfg)   
        
        # 添加断言确保配置已加载
        assert config.get_raw_config() is not None, "Config failed to initialize."
        
        print("--- Hydra configuration initialized successfully. Running tests... ---")

    # `yield` 关键字表示，完成初始化后，去执行所有测试。
    yield
    
    print("\n--- Test session finished. ---") 