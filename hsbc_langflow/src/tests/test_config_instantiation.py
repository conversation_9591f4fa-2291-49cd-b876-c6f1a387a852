import pytest
from omegaconf import DictConfig, OmegaConf
import sys
import os

# 动态添加项目根目录到sys.path
# 这使得我们可以像在项目根目录运行一样导入模块
# PS：直接修改sys.path是一种简便方法，在更复杂的项目中，通常会使用更规范的打包和安装方式（如setup.py）
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入我们项目中的配置和客户端实现
from utils.common.config_util import config
from utils.common.service_client import ServiceClient
from base.db.implementations.rdb.mysql.mysql_sqlalchemy_client import MySQLSQLAlchemyClient
from base.db.implementations.vs.pgvector.pgvector_client import PGVectorClient
from base.model_serve.model_runtime.model_providers.llm_model.opentrek_llm import OpenTrekLLM
from base.model_serve.model_runtime.model_providers.embedding_model.generic_embedding import GenericEmbedding

# 注意：@hydra.main 装饰器已被移除，配置现在由 tests/conftest.py 全局提供

def test_rdb_client_instantiation():
    """测试关系型数据库 (RDB) 客户端的实例化和单例模式。"""
    print("\nTesting RDB client instantiation via ServiceClient...")
    rdb_client_1 = ServiceClient(config.rdb)
    rdb_client_1.connect()
    
    print(f"✅ Successfully created RDB client proxy. Underlying type: {rdb_client_1.__class__.__name__}")
    assert isinstance(rdb_client_1, MySQLSQLAlchemyClient)
    
    rdb_client_2 = ServiceClient(config.rdb)
    assert rdb_client_1._get_instance() is rdb_client_2._get_instance(), "RDB instances should be the same object (singleton)"
    assert rdb_client_1._cache_key == rdb_client_2._cache_key, "Singleton cache keys should be identical"
    print("✅ RDB client is a singleton.")
    print("RDB client instantiation test passed.")

def test_vdb_client_instantiation():
    """测试向量数据库 (VDB) 客户端的实例化和单例模式。"""
    print("\nTesting VDB client instantiation via ServiceClient...")
    vdb_client_1 = ServiceClient(config.vdb)
    vdb_client_1.connect()
    
    print(f"✅ Successfully created VDB client proxy. Underlying type: {vdb_client_1.__class__.__name__}")
    assert isinstance(vdb_client_1, PGVectorClient)

    vdb_client_2 = ServiceClient(config.vdb)
    assert vdb_client_1._get_instance() is vdb_client_2._get_instance(), "VDB instances should be the same object (singleton)"
    assert vdb_client_1._cache_key == vdb_client_2._cache_key, "Singleton cache keys should be identical"
    print("✅ VDB client is a singleton.")
    print("VDB client instantiation test passed.")

def test_llm_instance_instantiation():
    """测试大语言模型 (LLM) 实例的实例化和单例模式。"""
    print("\nTesting LLM instance instantiation via ServiceClient...")
    llm_instance_1 = ServiceClient(config.llm)
    _ = llm_instance_1.base_url

    print(f"✅ Successfully created LLM proxy. Underlying type: {llm_instance_1.__class__.__name__}")
    assert isinstance(llm_instance_1, OpenTrekLLM)
    
    llm_instance_2 = ServiceClient(config.llm)
    assert llm_instance_1._get_instance() is llm_instance_2._get_instance(), "LLM instances should be the same object (singleton)"
    assert llm_instance_1._cache_key == llm_instance_2._cache_key, "Singleton cache keys should be identical"
    print("✅ LLM instance is a singleton.")
    print("LLM instance instantiation test passed.")

def test_embedding_instance_instantiation():
    """测试 Embedding 模型实例的实例化和单例模式。"""
    print("\nTesting Embedding instance instantiation via ServiceClient...")
    embedding_instance_1 = ServiceClient(config.embedding)
    _ = embedding_instance_1.base_url
    
    print(f"✅ Successfully created Embedding proxy. Underlying type: {embedding_instance_1.__class__.__name__}")
    assert isinstance(embedding_instance_1, GenericEmbedding)
    
    embedding_instance_2 = ServiceClient(config.embedding)
    assert embedding_instance_1._get_instance() is embedding_instance_2._get_instance(), "Embedding instances should be the same object (singleton)"
    assert embedding_instance_1._cache_key == embedding_instance_2._cache_key, "Singleton cache keys should be identical"
    print("✅ Embedding instance is a singleton.")
    print("Embedding instance instantiation test passed.")

# 如果你想直接运行这个文件进行调试，可以取消下面的注释
# if __name__ == "__main__":
#     # 为了能独立运行，我们需要手动调用fixture的功能
#     from conftest import initialize_hydra_for_tests
#     # 使用生成器协议来手动执行fixture的setup部分
#     gen = initialize_hydra_for_tests()
#     next(gen)
    
#     test_rdb_client_instantiation()
#     test_vdb_client_instantiation()
#     test_llm_instance_instantiation()
#     test_embedding_instance_instantiation()
    
#     # 手动执行fixture的teardown部分
#     try:
#         next(gen)
#     except StopIteration:
#         pass 