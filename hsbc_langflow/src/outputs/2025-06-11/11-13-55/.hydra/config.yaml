active_rdb: mysql
active_vdb: pgvector
active_llm: opentrek
active_embedding: moka-m3e-base
api_server:
  host: 0.0.0.0
  port: 31316
  reload: false
  log_level: info
  access_log: true
  limit_concurrency: 1000
level: INFO
extra:
- request_id
- service
sinks:
  main:
    sink: logs/main.log
    level: INFO
    format: '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {extra[service]: <8} |
      {extra[request_id]} | {name}:{function}:{line} - {message}'
    filter:
      service: main
    rotation: 10 MB
    retention: 7 days
    encoding: utf-8
    enqueue: true
    backtrace: true
    diagnose: true
  report:
    sink: logs/report.log
    level: INFO
    format: '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {extra[service]: <8} |
      {extra[request_id]} | {name}:{function}:{line} - {message}'
    filter:
      service: report
    rotation: 10 MB
    retention: 7 days
    encoding: utf-8
    enqueue: true
    backtrace: true
    diagnose: true
  process:
    sink: logs/process.log
    level: INFO
    format: '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {extra[service]: <8} |
      {extra[request_id]} | {name}:{function}:{line} - {message}'
    filter:
      service: process
    rotation: 10 MB
    retention: 7 days
    encoding: utf-8
    enqueue: true
    backtrace: true
    diagnose: true
  schema:
    sink: logs/schema.log
    level: INFO
    format: '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {extra[service]: <8} |
      {extra[request_id]} | {name}:{function}:{line} - {message}'
    filter:
      service: schema
    rotation: 10 MB
    retention: 7 days
    encoding: utf-8
    enqueue: true
    backtrace: true
    diagnose: true
database:
  rdbs:
    mysql:
      _target_: base.db.implementations.rdb.mysql.mysql_sqlalchemy_client.MySQLSQLAlchemyClient
      db_type: mysql
      connection:
        host: **************
        port: 37615
        user: root
        password: idea@1008
        db_name: hsbc_data
      usage:
        model_info:
          table_name: model_info
        where_info:
          table_name: where_info
        partition_info:
          table_name: partition_key
  vdbs:
    pgvector:
      _target_: base.db.implementations.vs.pgvector.pgvector_client.PGVectorClient
      db_type: pgvector
      connection:
        host: **************
        port: 30146
        user: pgvector
        password: pgvector
        db_name: postgres
      usage:
        general:
          table_name: hsbc_embedding_data
          search_schema:
            vector_field: embedding_column_name
            limit: 3
            metric_type: cosine
            output_fields:
            - id
            - partition_key
            expr: ''
            partition_name: ''
          query_schema:
            limit: 3
            expr: ''
            partition_name: ''
            output_fields:
            - id
            - partition_key
model:
  llms:
    opentrek:
      _target_: base.model_serve.model_runtime.model_providers.llm_model.opentrek_llm.OpenTrekLLM
      api_key: empty
      base_url: http://**************:30167/v1/chat/completions
      model_name: qwen_vllm
      provider: opentrek
      model_parameters:
        max_tokens: 8192
        temperature: 0.9
      stream: true
  embeddings:
    moka-m3e-base:
      _target_: base.model_serve.model_runtime.model_providers.embedding_model.generic_embedding.GenericEmbedding
      base_url: http://**************:30167/v1/embeddings
      api_key: empty
      model_name: embedding
      provider: moka-m3e-base
workflows:
  process:
    steps:
    - information_retriever
    - column_retriever
    - column_selector
    - schema_generator
    - sql_generator
    - sql_modifier
  schema:
    steps:
    - information_retriever
    - column_retriever
    - column_selector
    - schema_generator
