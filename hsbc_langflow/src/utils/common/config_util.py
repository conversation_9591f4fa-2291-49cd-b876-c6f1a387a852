'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 10:40:25 am
'''

'''
一个纯粹的、由Hydra驱动的配置工具类
'''
from dataclasses import dataclass, field
from typing import List, Optional, Any, Dict
from omegaconf import DictConfig
import hydra
from loguru import logger

# 导入新的模型schemas
from base.model_serve.model_runtime.entities.model_schemas import LLMConfig, EmbeddingConfig

# --- 结构化配置 DataClasses ---

@dataclass
class ApiServerConfig:
    host: str = "0.0.0.0"
    port: int = 31316
    reload: bool = False
    log_level: str = "info"
    access_log: bool = True
    limit_concurrency: int = 1000

@dataclass
class RDBConnectionConfig:
    host: str = "localhost"
    port: int = 3306
    user: str = "root"
    password: str = ""
    db_name: str = ""

@dataclass
class RDBUsageTableConfig:
    table_name: str

@dataclass
class RDBConfig:
    db_type: str = "mysql"
    connection: RDBConnectionConfig = field(default_factory=RDBConnectionConfig)
    usage: Dict[str, RDBUsageTableConfig] = field(default_factory=dict)
    _target_: Optional[str] = None # 显式添加_target_，便于ServiceClient实例化

@dataclass
class VDBConnectionConfig:
    host: str = "localhost"
    port: int = 5432
    user: str = "pgvector"
    password: str = "pgvector"
    db_name: str = "postgres"

@dataclass
class VDBSearchSchemaConfig:
    vector_field: str
    limit: int
    metric_type: str
    output_fields: List[str]
    expr: str
    partition_name: str

@dataclass
class VDBQuerySchemaConfig:
    limit: int
    expr: str
    partition_name: str
    output_fields: List[str]

@dataclass
class VDBUsageGeneralConfig:
    table_name: str
    search_schema: VDBSearchSchemaConfig
    query_schema: VDBQuerySchemaConfig

@dataclass
class VDBConfig:
    db_type: str = "pgvector"
    connection: VDBConnectionConfig = field(default_factory=VDBConnectionConfig)
    usage: Dict[str, VDBUsageGeneralConfig] = field(default_factory=dict)
    _target_: Optional[str] = None # 显式添加_target_，便于ServiceClient实例化

@dataclass
class DatabaseConfig:
    """容纳多套命名的数据库配置"""
    rdbs: Dict[str, RDBConfig] = field(default_factory=dict)
    vdbs: Dict[str, VDBConfig] = field(default_factory=dict)

@dataclass
class ModelConfig:
    """容纳多套命名的模型配置"""
    llms: Dict[str, LLMConfig] = field(default_factory=dict)
    embeddings: Dict[str, EmbeddingConfig] = field(default_factory=dict)

@dataclass
class WorkflowConfig:
    steps: List[str] = field(default_factory=list)

@dataclass
class AppConfig:
    # 别名 - 改为 active_... 来表示当前选中的配置
    active_rdb: str
    active_vdb: str
    active_llm: str
    active_embedding: str

    # 组合配置
    api_server: ApiServerConfig = field(default_factory=ApiServerConfig)
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    workflow: WorkflowConfig = field(default_factory=WorkflowConfig)

    # Hydra默认值列表 - 这里不需要了，Hydra在@hydra.main中处理
    # defaults: List[Any] = field(default_factory=lambda: [
    #     "_self_",
    #     {"api_server": "default"},
    #     {"database": "default"},
    #     {"model": "default"}
    # ])


# --- 单例封装 ---

class Config:
    """
    配置类，为应用程序提供一个稳定的、类型安全的配置API。
    它包装了由Hydra加载和实例化的AppConfig对象。
    """
    _instance = None
    _config_data: Optional[DictConfig] = None
    _initialized: bool = False

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(Config, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """
        构造函数保持为空，以支持单例模式的清晰用法。
        真正的初始化由 initialize 类方法完成。
        """
        pass

    @classmethod
    def initialize(cls, cfg: DictConfig):
        """
        使用从Hydra加载的配置，显式地、一次性地初始化全局Config单例。
        如果配置已被重置，此方法可用于重新初始化。
        
        Args:
            cfg (DictConfig): 由Hydra加载的配置对象。
        """
        if cls._initialized:
            logger.warning("Config has already been initialized. Skipping re-initialization.")
            return

        instance = cls() # 获取单例实例
        instance._config_data = cfg
        cls._initialized = True
        logger.info("Global Config singleton has been successfully initialized.")

    @property
    def rdb(self) -> RDBConfig:
        """获取由 active_rdb 指定的主关系型数据库配置。"""
        if not Config._initialized or self._config_data is None:
            raise ValueError("Config not initialized. Please call Config.initialize(cfg) first.")
        
        alias = self._config_data.active_rdb
        if not alias:
            raise ValueError("'active_rdb' is not defined in the configuration.")
        
        if alias not in self._config_data.database.rdbs:
            available = list(self._config_data.database.rdbs.keys())
            raise ValueError(f"RDB alias '{alias}' not found in available configurations: {available}")
            
        return self._config_data.database.rdbs[alias]
    
    @property
    def vdb(self) -> VDBConfig:
        """获取由 active_vdb 指定的主向量数据库配置。"""
        if not Config._initialized or self._config_data is None:
            raise ValueError("Config not initialized. Please call Config.initialize(cfg) first.")

        alias = self._config_data.active_vdb
        if not alias:
            raise ValueError("'active_vdb' is not defined in the configuration.")
            
        if alias not in self._config_data.database.vdbs:
            available = list(self._config_data.database.vdbs.keys())
            raise ValueError(f"VDB alias '{alias}' not found in available configurations: {available}")
            
        return self._config_data.database.vdbs[alias]
    
    @property
    def llm(self) -> LLMConfig:
        """获取由 active_llm 指定的大语言模型配置。"""
        if not Config._initialized or self._config_data is None:
            raise ValueError("Config not initialized. Please call Config.initialize(cfg) first.")

        alias = self._config_data.active_llm
        if not alias:
            raise ValueError("'active_llm' is not defined in the configuration.")

        if alias not in self._config_data.model.llms:
            available = list(self._config_data.model.llms.keys())
            raise ValueError(f"LLM alias '{alias}' not found in available configurations: {available}")

        return self._config_data.model.llms[alias]
    
    @property
    def embedding(self) -> EmbeddingConfig:
        """获取由 active_embedding 指定的 Embedding 模型配置。"""
        if not Config._initialized or self._config_data is None:
            raise ValueError("Config not initialized. Please call Config.initialize(cfg) first.")

        alias = self._config_data.active_embedding
        if not alias:
            raise ValueError("'active_embedding' is not defined in the configuration.")

        if alias not in self._config_data.model.embeddings:
            available = list(self._config_data.model.embeddings.keys())
            raise ValueError(f"Embedding alias '{alias}' not found in available configurations: {available}")
            
        return self._config_data.model.embeddings[alias]
    
    @property
    def workflow(self) -> WorkflowConfig:
        """获取工作流配置。"""
        if not Config._initialized or self._config_data is None:
            raise ValueError("Config not initialized. Please call Config.initialize(cfg) first.")
        return self._config_data.workflow
    
    @property
    def api_server(self) -> ApiServerConfig:
        """获取API服务配置"""
        if not Config._initialized or self._config_data is None:
            raise ValueError("Config not initialized. Please call Config.initialize(cfg) first.")
        return self._config_data.api_server
    
    def get_raw_config(self) -> Optional[DictConfig]:
        """获取原始的OmegaConf DictConfig对象。"""
        return self._config_data


# 全局配置实例，它将在应用入口被初始化。
config = Config()


def reset_config_for_testing():
    """
    重置全局Config单例的状态。
    **主要用于测试目的**，以确保测试之间的隔离。
    """
    if config._instance is not None:
        config._instance._config_data = None
    Config._initialized = False
    logger.warning("Config singleton has been reset for testing.")