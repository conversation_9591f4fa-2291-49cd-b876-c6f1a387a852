'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 6:45:51 am
'''

'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 6:40:12 am
'''

# -*- coding: utf-8 -*-  # 推荐添加文件编码声明

import os
import time  # 导入 time 模块
import logging
import sys
from loguru import logger
from omegaconf import DictConfig, OmegaConf

# --- 时区设置 ---
# 关键：在导入 loguru 之前设置时区为上海
os.environ['TZ'] = 'Asia/Shanghai'
if hasattr(time, 'tzset'):
    time.tzset()
# --- 时区设置结束 ---

# --- 路径和目录设置 ---
# 使用更健壮的方式获取项目根目录 (假设此文件在项目的某个子目录中)
# 这里假设脚本位于 project_root/src/utils/logging.py 之类的路径
# 需要根据你的实际项目结构调整 `os.path.dirname` 的次数
script_dir = os.path.dirname(__file__)
project_root = os.path.dirname(os.path.dirname(script_dir)) # 示例：向上两级是项目根目录

log_dir = os.path.join(project_root, "logs")
log_file = os.path.join(log_dir, "hsbc_nl2sql.log")

# 确保日志目录存在
os.makedirs(log_dir, exist_ok=True)

# --- 默认日志格式 ---
# 为控制台和文件定义不同的格式，控制台使用颜色以提高可读性
CONSOLE_LOGGER_FORMAT = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS Z}</green> | <level>{level: <8}</level> | <cyan>{name}:{function}:{line}</cyan> - <level>{message}</level>"
FILE_LOGGER_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS Z} | {level: <8} | {name}:{function}:{line} - {message}"

# --- 默认基础日志配置 ---
# 提供一个开箱即用的、只向控制台输出的最小化 logger。
# 这样即便不经过Hydra的 configure_logger 配置，logger本身也是可用的。
# 当 configure_logger 被调用时，它会首先移除这个默认配置。
logger.remove()
logger.add(
    sys.stderr,
    level="INFO",
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}:{function}:{line}</cyan> - <level>{message}</level>",
    colorize=True
)
# --- 默认配置结束 ---

# --- 标准日志库拦截处理器 ---
# 此处理器将 Python 标准 `logging` 模块的日志重定向到 Loguru
class InterceptHandler(logging.Handler):
    """
    将 Python 标准 `logging` 模块的日志重定向到 Loguru。
    """
    def emit(self, record):
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno
        
        frame, depth = logging.currentframe(), 2
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
        
        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

def _create_filter(filter_config):
    """
    根据字典配置创建 Loguru 过滤器函数。
    """
    if not isinstance(filter_config, DictConfig) and not isinstance(filter_config, dict):
        return filter_config

    # 只取第一个键值对作为过滤条件
    key, value = next(iter(filter_config.items()))

    # 特殊处理 'main' 服务，使其也能捕获没有 'service' 上下文的日志
    if key == 'service' and value == 'main':
        return lambda record: record["extra"].get(key) == value or key not in record["extra"]
    
    # 标准过滤器：精确匹配 extra 中的值
    return lambda record: record["extra"].get(key) == value

def configure_logger(cfg: DictConfig):
    """
    根据 Hydra 配置，配置全局 Loguru 日志记录器。
    
    - 清除所有现有处理器（包括上面的默认处理器）。
    - 根据配置添加控制台和文件日志。
    - 拦截标准 `logging` 模块的日志。
    """
    # 1. 重置所有 logger 配置，移除默认的 console logger
    logger.remove()

    # 2. 配置带上下文的控制台日志 (用于API请求)
    logger.add(
        sys.stderr,
        level=cfg.get("level", "INFO").upper(),
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{extra[service]: <8}</cyan> | <yellow>{extra[request_id]:^36}</yellow> | <cyan>{name}:{function}:{line}</cyan> - <level>{message}</level>",
        colorize=True,
        enqueue=True,
        backtrace=True,
        diagnose=True,
        filter=lambda record: 'service' in record['extra'] and 'request_id' in record['extra']
    )
    # 3. 添加一个备用控制台日志 (用于启动等无上下文的日志)
    logger.add(
        sys.stderr,
        level=cfg.get("level", "INFO").upper(),
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>main</cyan>      | <cyan>{name}:{function}:{line}</cyan> - <level>{message}</level>",
        colorize=True,
        enqueue=True,
        backtrace=True,
        diagnose=True,
        filter=lambda record: 'service' not in record['extra']
    )

    # 4. 根据sinks列表配置文件日志
    sinks = cfg.get("sinks", [])
    for sink_cfg in sinks:
        # OmegaConf 会将 `sinks` 目录下的每个文件解析为一个字典
        # `main.yaml` -> {"sink": "logs/main.log", ...}
        
        # 提取sink的具体参数
        sink_params = sink_cfg

        # 确保sink配置有效
        if not sink_params.get("sink"):
            logger.warning(f"Skipping invalid sink configuration: {sink_cfg}")
            continue

        # 确保日志文件的目录存在
        log_dir = os.path.dirname(sink_params.sink)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
            
        logger.add(
            sink=sink_params.sink,
            level=sink_params.get("level", "INFO").upper(),
            format=sink_params.format,
            filter=_create_filter(sink_params.get("filter")),
            encoding=sink_params.get("encoding", "utf-8"),
            rotation=sink_params.get("rotation", "10 MB"),
            retention=sink_params.get("retention", "7 days"),
            enqueue=True, # 异步写入文件，提升性能
            backtrace=True,
            diagnose=True
        )
        logger.info(f"Configured file sink to '{sink_params.sink}' with filter '{sink_params.get('filter')}'")

    # 5. 拦截来自标准 logging 模块的日志
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # 6. 设置全局异常钩子
    sys.excepthook = logger.catch

    logger.info("Logger configuration complete based on Hydra config.")
    return logger

# --- 使用 logger ---
# 现在你可以像这样使用 logger 实例了
# logger.info("这是一条信息日志，现在应该显示北京时间了。")
# logger.debug("这是一条调试信息。")