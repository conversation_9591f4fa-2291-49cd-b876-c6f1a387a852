import time
import contextlib
from datetime import datetime, timezone, timedelta


@contextlib.contextmanager
def timer(logger=None, message="执行时间"):
    """计时上下文管理器
    
    Args:
        logger: 可选的日志记录器
        message: 日志消息前缀
    """
    start_time = time.perf_counter()  # 比time.time()精度更高
    try:
        yield
    finally:
        end_time = time.perf_counter()
        duration = end_time - start_time
        if logger:
            logger.info(f"{message}：{duration:.4f}秒")
        return duration