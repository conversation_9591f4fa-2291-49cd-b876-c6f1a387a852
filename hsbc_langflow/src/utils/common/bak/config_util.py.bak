'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 10:40:25 am
'''

'''
一个纯粹的、由Hydra驱动的配置工具类
'''
from dataclasses import dataclass, field
from typing import List, Optional, Any, Dict
from omegaconf import DictConfig
import hydra

# 导入新的模型schemas
from base.model_serve.model_runtime.entities.model_schemas import LLMConfig, EmbeddingConfig

# --- 结构化配置 DataClasses ---

@dataclass
class ApiServerConfig:
    host: str = "0.0.0.0"
    port: int = 31316
    reload: bool = False
    log_level: str = "info"
    access_log: bool = True
    limit_concurrency: int = 1000

@dataclass
class RDBConnectionConfig:
    host: str = "localhost"
    port: int = 3306
    user: str = "root"
    password: str = ""
    db_name: str = ""

@dataclass
class RDBUsageTableConfig:
    table_name: str

@dataclass
class RDBConfig:
    db_type: str = "mysql"
    connection: RDBConnectionConfig = field(default_factory=RDBConnectionConfig)
    usage: Dict[str, RDBUsageTableConfig] = field(default_factory=dict)

@dataclass
class VDBConnectionConfig:
    host: str = "localhost"
    port: int = 5432
    user: str = "pgvector"
    password: str = "pgvector"
    db_name: str = "postgres"

@dataclass
class VDBSearchSchemaConfig:
    vector_field: str
    limit: int
    metric_type: str
    output_fields: List[str]
    expr: str
    partition_name: str

@dataclass
class VDBQuerySchemaConfig:
    limit: int
    expr: str
    partition_name: str
    output_fields: List[str]

@dataclass
class VDBUsageGeneralConfig:
    table_name: str
    search_schema: VDBSearchSchemaConfig
    query_schema: VDBQuerySchemaConfig

@dataclass
class VDBConfig:
    db_type: str = "pgvector"
    connection: VDBConnectionConfig = field(default_factory=VDBConnectionConfig)
    usage: Dict[str, VDBUsageGeneralConfig] = field(default_factory=dict)

@dataclass
class DatabaseConfig:
    rdb: RDBConfig = field(default_factory=RDBConfig)
    vdb: VDBConfig = field(default_factory=VDBConfig)

@dataclass
class ModelConfig:
    """新的模型配置，直接使用我们定义的结构"""
    llm: LLMConfig = field(default_factory=LLMConfig)
    embedding: EmbeddingConfig = field(default_factory=EmbeddingConfig)

@dataclass
class WorkflowConfig:
    steps: List[str] = field(default_factory=list)

@dataclass
class AppConfig:
    # 别名
    rdb_alias: str
    vdb_alias: str
    llm_alias: str
    embedding_alias: str

    # 组合配置
    api_server: ApiServerConfig = field(default_factory=ApiServerConfig)
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    workflow: WorkflowConfig = field(default_factory=WorkflowConfig)

    # Hydra默认值列表 - 这里不需要了，Hydra在@hydra.main中处理
    # defaults: List[Any] = field(default_factory=lambda: [
    #     "_self_",
    #     {"api_server": "default"},
    #     {"database": "default"},
    #     {"model": "default"}
    # ])


# --- 单例封装 ---

class Config:
    """
    配置类，为应用程序提供一个稳定的、类型安全的配置API。
    它包装了由Hydra加载和实例化的AppConfig对象。
    """
    _instance = None
    _config_data: Optional[DictConfig] = None  # 将类型改为 DictConfig
    
    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(Config, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, cfg: Optional[DictConfig] = None):
        """
        这个方法应该只在应用入口被调用一次，用来填充配置。
        它现在直接存储传入的 DictConfig 对象，而不是立即解析它。
        """
        if cfg is not None and self._config_data is None:
            # 直接存储OmegaConf的DictConfig对象，实现延迟解析
            self._config_data = cfg

    
    @property
    def primary_rdb(self) -> RDBConfig:
        """获取当前配置的主关系型数据库。"""
        if self._config_data is None:
            raise ValueError("Config not initialized")
        # OmegaConf的DictConfig支持直接的点式访问
        return self._config_data.database.rdb
    
    @property
    def primary_vdb(self) -> VDBConfig:
        """获取当前配置的主向量数据库。"""
        if self._config_data is None:
            raise ValueError("Config not initialized")
        return self._config_data.database.vdb
    
    @property
    def llm(self) -> LLMConfig:
        """获取当前配置的大语言模型。"""
        if self._config_data is None:
            raise ValueError("Config not initialized")
        return self._config_data.model.llm
    
    @property
    def embedding(self) -> EmbeddingConfig:
        """获取当前配置的Embedding模型。"""
        if self._config_data is None:
            raise ValueError("Config not initialized")
        return self._config_data.model.embedding
    
    @property
    def workflow(self) -> WorkflowConfig:
        """获取工作流配置。"""
        if self._config_data is None:
            raise ValueError("Config not initialized")
        return self._config_data.workflow
    
    @property
    def api_server(self) -> ApiServerConfig:
        """获取API服务配置"""
        if self._config_data is None:
            raise ValueError("Config not initialized")
        return self._config_data.api_server
    
    def get_raw_config(self) -> Optional[DictConfig]:
        """获取原始的OmegaConf DictConfig对象。"""
        return self._config_data


# 全局配置实例，它将在应用入口被初始化。
config = Config()