from loguru import logger
from typing import Any
import hydra


class DynamicHydraClient:
    """
    一个通用的动态客户端，它使用 Hydra 根据传入的配置动态实例化
    一个底层服务实例 (例如 OpenTrekLLM, MySQLSQLAlchemyClient 等)。

    这个类的实例会代理所有对其底层服务实例的属性访问。

    **重要**: 
    此类是一个**动态实例化器**，每次创建 `DynamicHydraClient` 实例时，
    都会创建一个**全新的**底层服务实例。它**不使用**任何形式的单例缓存。

    **使用场景**:
    -   当需要根据动态的、非全局的配置创建实例时。
    -   例如，为一个特定的请求临时创建一个使用用户私有凭证的客户端。
    -   在测试中，用于方便地实例化带有模拟配置的客户端。

    **注意**: 
    对于需要全局共享、统一管理的单例服务（如应用默认的LLM和数据库连接），
    请**不要**使用此类，而应使用项目提供的单例工厂函数（如 `get_llm_provider`）。

    使用示例:
        # 假设 config 是一个包含 '_target_' 的字典
        llm_config = {
            '_target_': 'base.model_serve.model_runtime.model_providers.llm_model.opentrek_llm.OpenTrekLLM',
            'base_url': 'http://some_url',
            'api_key': 'some_key'
        }
        llm_client = DynamicHydraClient(client_type='llm_model', **llm_config)
        
        # 现在 llm_client 的行为就像一个 OpenTrekLLM 实例
        llm_client.invoke(...) 
    """
    def __init__(self, client_type: str, **kwargs: Any):
        """
        根据提供的配置初始化动态客户端。

        Args:
            client_type (str): 客户端类型 (例如 'llm_model', 'db_client')，用于日志和区分.
            **kwargs (Any): 传递给 Hydra 进行实例化的配置。必须包含 `_target_` 键.
        """
        if '_target_' not in kwargs:
            raise ValueError("Configuration must contain a '_target_' key for instantiation.")
        
        logger.info(f"Initializing DynamicHydraClient for type '{client_type}' with target '{kwargs['_target_']}'")
        
        # 使用 Hydra 实例化
        self._instance = hydra.utils.instantiate(kwargs)
        self._client_type = client_type
        logger.info(f"Successfully created instance of {type(self._instance).__name__} for DynamicHydraClient.")

    def __getattr__(self, name: str) -> Any:
        """
        将属性访问代理到底层的服务实例。
        """
        logger.debug(f"DynamicHydraClient delegating '{name}' to {type(self._instance).__name__}")
        return getattr(self._instance, name)

    @property
    def __class__(self):
        """
        代理 __class__ 属性，以实现更透明的类型检查。
        """
        return self._instance.__class__

    def __repr__(self) -> str:
        """
        提供一个更有信息的字符串表示。
        """
        return f"<DynamicHydraClient type='{self._client_type}' wrapping {repr(self._instance)}>" 