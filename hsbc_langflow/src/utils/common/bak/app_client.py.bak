"""
应用服务客户端 (Application Service Client)

本模块提供了最终的、高度简化的全局服务访问工具 `AppClient`。
它遵循您提出的核心设计思想，将服务的获取与配置完全绑定，
为开发者提供一个极致简洁和统一的API。

核心理念:
- **配置即服务 (Configuration as a Service)**: 只要在全局配置 (config.yaml) 
  中定义了一个服务，就可以通过其配置路径 (key) 直接获取其实例。
- **完全自动化**: 自动处理懒加载、单例缓存和实例初始化 (包括连接)。
- **零工厂函数**: 不再需要为每个服务编写 `get_...()` 形式的工厂函数。

如何工作:
当您编写 `llm = AppClient('llm')` 时：
1. `AppClient` 实例被创建，但内部的真实服务实例尚未创建。
2. 当您第一次调用 `llm.invoke(...)` 时：
   a. `AppClient` 在一个全局缓存中查找key为 'llm' 的实例。
   b. 如果未找到，它会加载全局 `config`，并找到 `config.llm` 这部分配置。
   c. 它使用 `hydra.utils.instantiate` 根据该配置创建真实的 `OpenTrekLLM` 实例。
   d. 它检查该实例是否有 `.connect()` 方法，如果有，则自动调用。
   e. 创建好的实例被存入全局缓存。
3. `invoke` 方法被代理到缓存中的真实实例上。
4. 后续所有 `AppClient('llm')` 的调用都将懒加载并复用同一个缓存实例。
"""

from loguru import logger
from typing import Dict, Any, Optional
import hydra
from omegaconf import OmegaConf

# 导入全局单例配置
from utils.common.config_util import config

# 全局注册表，用于缓存所有AppClient创建的单例实例
_app_client_registry: Dict[str, Any] = {}


class AppClient:
    """
    一个智能的、懒加载的、配置驱动的单例服务客户端。
    通过服务的配置路径 (如 'llm', 'database.vdb') 来获取和管理全局单例服务。
    """
    def __init__(self, config_path: str):
        """
        初始化一个应用服务客户端代理。

        Args:
            config_path (str): 服务在全局Hydra配置中的路径。
                               例如: 'llm', 'embedding', 'database.rdb'
        """
        if not isinstance(config_path, str) or not config_path:
            raise ValueError("`config_path` must be a non-empty string.")
        self._config_path = config_path

    def _get_instance(self) -> Any:
        """
        获取或创建并缓存实际的单例实例。
        这是所有魔法发生的地方。
        """
        # 检查全局注册表中是否已有实例
        if self._config_path not in _app_client_registry:
            logger.info(f"AppClient instance for '{self._config_path}' not in cache. Lazily creating...")
            try:
                # 从全局配置中根据路径选择对应的配置部分
                service_cfg = OmegaConf.select(config, self._config_path)
                
                if service_cfg is None:
                    raise ValueError(f"Config path '{self._config_path}' not found in the global configuration.")
                
                if not hasattr(service_cfg, '_target_'):
                    raise ValueError(f"Configuration at '{self._config_path}' is missing the '_target_' key.")
                
                # 使用Hydra实例化
                # 注意：我们之前改进过，这里我们再次采用只传递必要参数的方式
                # 但为了通用性，这里先直接传递整个cfg，假设目标类的__init__足够鲁棒
                # 或者，我们可以约定一个规范
                instance = hydra.utils.instantiate(service_cfg)
                
                # 智能连接：如果实例有 connect 方法，则自动调用
                if hasattr(instance, 'connect') and callable(getattr(instance, 'connect')):
                    logger.info(f"Instance for '{self._config_path}' has a 'connect' method. Calling it automatically.")
                    instance.connect()

                # 将新实例存入全局注册表
                _app_client_registry[self._config_path] = instance
                logger.info(f"Instance for '{self._config_path}' of type {type(instance).__name__} created and cached successfully.")

            except Exception as e:
                logger.error(f"Error during lazy creation of AppClient for '{self._config_path}': {e}", exc_info=True)
                raise
        
        # 从全局注册表返回实例
        return _app_client_registry[self._config_path]

    def __getattr__(self, name: str) -> Any:
        """将属性访问转发给真实的单例实例。"""
        logger.debug(f"AppClient delegating attribute '{name}' to lazy-loaded instance '{self._config_path}'.")
        instance = self._get_instance()
        return getattr(instance, name)

    @property
    def __class__(self) -> type:
        """代理对象的 class 属性，方便类型检查。"""
        return self._get_instance().__class__

    def __repr__(self) -> str:
        """提供一个有用的表示，而不触发实例化。"""
        if self._config_path not in _app_client_registry:
            return f"<AppClient for '{self._config_path}' (not initialized)>"
        return repr(_app_client_registry[self._config_path])


def reset_app_client_registry() -> None:
    """
    重置全局客户端注册表。
    **主要用于测试目的**，以确保测试之间的隔离。
    """
    global _app_client_registry
    _app_client_registry.clear()
    logger.warning("The global AppClient registry has been reset. This should only be used in tests.")

__all__ = [
    'AppClient',
    'reset_app_client_registry'
] 