"""
统一服务客户端 (Unified Service Client)

本模块提供了最终形态的、统一的服务访问工具 `ServiceClient`。
它完美融合了您提出的所有设计思想，提供了一个前所未有的、
兼具简洁性、灵活性和强大功能于一体的API。

核心理念:
- **一个客户端，掌控一切**: 无论是需要全局共享的单例服务，还是用于测试的
  临时实例，都通过这一个 `ServiceClient` 来获取。
- **配置驱动，灵活调用**:可以直接传入全局`config`对象的一部分，也可以传入
  手动创建的配置字典，实现了完全的调用自由。
- **生命周期，由你定义**: 通过`singleton`和`singleton_id`参数，调用者可以
  精确控制所创建实例的生命周期和缓存策略。

三位一体的工作模式:
1.  **自动单例 (默认)**: `ServiceClient(config.llm)`
    -   根据配置内容的哈希值来缓存实例，确保相同配置获得相同实例。
2.  **ID化单例 (高级)**: `ServiceClient(config.db, singleton_id='db_proc_1')`
    -   使用您手动指定的`singleton_id`作为缓存键，完美满足多进程等场景。
3.  **临时实例 (动态)**: `ServiceClient(my_config, singleton=False)`
    -   完全不使用缓存，总是创建一个全新的实例。
"""

import hashlib
import json
from loguru import logger
from typing import Dict, Any, Optional
import hydra
from omegaconf import OmegaConf, DictConfig

# 全局注册表，用于缓存所有ServiceClient创建的单例实例
_service_client_registry: Dict[str, Any] = {}


def _get_config_hash(cfg: Any) -> str:
    """为配置对象创建一个稳定的、确定性的SHA256哈希值。"""
    if isinstance(cfg, DictConfig):
        # 将OmegaConf对象转换为纯Python字典
        cfg_dict = OmegaConf.to_container(cfg, resolve=True)
    else:
        cfg_dict = cfg
    
    # 使用json.dumps进行序列化，并设置sort_keys=True来保证顺序
    serialized_cfg = json.dumps(cfg_dict, sort_keys=True).encode('utf-8')
    return hashlib.sha256(serialized_cfg).hexdigest()


class ServiceClient:
    """
    一个统一的、智能的、懒加载的、配置驱动的服务客户端。
    它根据传入的配置和参数，灵活地提供单例或临时服务实例。
    """
    def __init__(self, config: Any, singleton_id: Optional[str] = None, singleton: bool = True):
        """
        初始化一个服务客户端代理。

        Args:
            config (Any): 一个包含`_target_`的配置对象 (可以是OmegaConf或普通字典)。
            singleton_id (Optional[str], optional): 手动指定的单例ID。
                如果提供，将优先使用此ID作为缓存键。Defaults to None.
            singleton (bool, optional): 是否将此服务作为单例。
                如果为 False，则每次访问都会创建新实例。Defaults to True.
        """
        if not hasattr(config, '_target_') and '_target_' not in config:
            raise ValueError("Configuration must contain a '_target_' key.")
        
        self._config = config
        self._is_singleton = singleton
        
        if self._is_singleton:
            if singleton_id:
                self._cache_key = singleton_id
            else:
                self._cache_key = _get_config_hash(config)
        else:
            self._cache_key = None # 非单例没有缓存键

        self._instance = None # 用于存储非单例的实例

    def _get_instance(self) -> Any:
        """
        获取或创建服务实例。
        """
        if not self._is_singleton:
            # 模式三：临时实例
            if self._instance is None:
                logger.info(f"Creating a new non-singleton instance for target '{self._config.get('_target_')}'...")
                self._instance = self._create_instance()
            return self._instance

        # 模式一 & 二：单例
        if self._cache_key not in _service_client_registry:
            logger.info(f"Singleton instance with key '{self._cache_key}' not in cache. Lazily creating...")
            instance = self._create_instance()
            _service_client_registry[self._cache_key] = instance

        return _service_client_registry[self._cache_key]
    
    def _create_instance(self) -> Any:
        """根据配置创建实例并尝试连接。"""
        try:
            instance = hydra.utils.instantiate(self._config)
            
            # 智能连接
            if hasattr(instance, 'connect') and callable(getattr(instance, 'connect')):
                logger.info(f"Instance for target '{self._config.get('_target_')}' has 'connect' method. Calling it.")
                instance.connect()
            
            logger.info(f"Instance of type {type(instance).__name__} created successfully.")
            return instance
        except Exception as e:
            logger.error(f"Error during instance creation for target '{self._config.get('_target_')}': {e}", exc_info=True)
            raise

    def __getattr__(self, name: str) -> Any:
        """将属性访问转发给真实的服务实例。"""
        instance = self._get_instance()
        return getattr(instance, name)

    @property
    def __class__(self) -> type:
        """代理对象的 class 属性，方便类型检查。"""
        return self._get_instance().__class__

    def __repr__(self) -> str:
        """提供一个有用的表示，而不触发实例化。"""
        if self._is_singleton and self._cache_key in _service_client_registry:
            return repr(_service_client_registry[self._cache_key])
        if self._instance is not None:
            return repr(self._instance)
        
        mode = f"singleton (key={self._cache_key})" if self._is_singleton else "non-singleton"
        return f"<ServiceClient for '{self._config.get('_target_')}' ({mode}, not initialized)>"


def reset_service_client_registry() -> None:
    """
    重置全局服务客户端注册表。
    **主要用于测试目的**，以确保测试之间的隔离。
    """
    global _service_client_registry
    _service_client_registry.clear()
    logger.warning("The global ServiceClient registry has been reset. This should only be used in tests.")

__all__ = [
    'ServiceClient',
    'reset_service_client_registry'
] 