import hydra
from omegaconf import DictConfig, OmegaConf
import sys
import os

# 动态添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入我们项目中的配置和客户端实现
from utils.common.config_util import Config, config, reset_config_for_testing
from utils.common.service_client import ServiceClient, reset_service_client_registry
from base.db.implementations.rdb.mysql.mysql_sqlalchemy_client import MySQLSQLAlchemyClient
from base.db.implementations.vs.pgvector.pgvector_client import PGVectorClient
from base.model_serve.model_runtime.model_providers.llm_model.opentrek_llm import OpenTrekLLM
from base.model_serve.model_runtime.model_providers.embedding_model.generic_embedding import GenericEmbedding
from base.model_serve.model_runtime.entities import PromptMessage

@hydra.main(version_base=None, config_path="../../../config", config_name="config")
def test_service_client_instantiation(cfg: DictConfig) -> None:
    """
    测试通过 ServiceClient 的正确使用方式，能否根据Hydra加载的配置，
    成功地、延迟地实例化并获取对应的客户端和服务单例，并验证其基本可用性。
    """
    # 在测试开始前重置所有服务和配置注册表，
    # 确保测试隔离性
    reset_service_client_registry()
    reset_config_for_testing()

    # 使用新的、符合Python习惯的方式初始化全局配置
    config.initialize(cfg)
    
    print("--- Configuration Loaded ---")
    print(OmegaConf.to_yaml(cfg))
    print("----------------------------\n")

    # 1. 测试关系型数据库 (RDB) 客户端
    print("Testing RDB client instantiation and availability...")
    try:
        # 正确用法：传入从全局config获取的配置部分来创建客户端代理
        rdb_proxy_1 = ServiceClient(config.rdb)
        rdb_proxy_2 = ServiceClient(config.rdb)
        
        # 验证单例模式 - 更好的方式
        print("Verifying RDB client singleton pattern...")
        assert rdb_proxy_1._cache_key == rdb_proxy_2._cache_key, "Cache keys for the same config should be identical."
        
        rdb_instance_1 = rdb_proxy_1._get_instance()
        rdb_instance_2 = rdb_proxy_2._get_instance()
        assert rdb_instance_1 is rdb_instance_2, "Instances from the same config should be the same object."
        assert isinstance(rdb_instance_1, MySQLSQLAlchemyClient)
        print("✅ RDB client is a singleton.")

        # 验证可用性
        print("Verifying RDB client availability...")
        result = rdb_instance_1.execute_query("SELECT * FROM `model_info` Limit 1")
        print('🤔rdb_test_select_result:', result)
        assert result is not None
        print("✅ RDB client is available and executed a simple query.")
        print("RDB client test passed.\n")

    except Exception as e:
        print(f"❌ Failed to test RDB client: {e}\n")
        raise

    # 2. 测试向量数据库 (VDB) 客户端
    print("Testing VDB client instantiation and availability...")
    try:
        vdb_proxy_1 = ServiceClient(config.vdb)
        vdb_proxy_2 = ServiceClient(config.vdb)

        # 验证单例模式
        print("Verifying VDB client singleton pattern...")
        assert vdb_proxy_1._cache_key == vdb_proxy_2._cache_key
        vdb_instance_1 = vdb_proxy_1._get_instance()
        vdb_instance_2 = vdb_proxy_2._get_instance()
        assert vdb_instance_1 is vdb_instance_2
        assert isinstance(vdb_instance_1, PGVectorClient)
        print("✅ VDB client is a singleton.")

        # 验证可用性
        print("Verifying VDB client availability...")
        collections = vdb_instance_1.list_collections()
        assert isinstance(collections, list)
        print(f"✅ VDB client is available. Found collections: {collections}")
        print("VDB client test passed.\n")

    except Exception as e:
        print(f"❌ Failed to test VDB client: {e}\n")
        raise

    # 3. 测试大语言模型 (LLM) 实例
    print("Testing LLM instance instantiation and availability...")
    try:
        llm_proxy_1 = ServiceClient(config.llm)
        llm_proxy_2 = ServiceClient(config.llm)
        
        # 验证单例模式
        print("Verifying LLM instance singleton pattern...")
        assert llm_proxy_1._cache_key == llm_proxy_2._cache_key
        llm_instance_1 = llm_proxy_1._get_instance()
        llm_instance_2 = llm_proxy_2._get_instance()
        assert llm_instance_1 is llm_instance_2
        assert isinstance(llm_instance_1, OpenTrekLLM)
        print("✅ LLM instance is a singleton.")
        
        # 验证可用性
        print("Verifying LLM instance availability...")
        prompt = [PromptMessage(role="user", content="hello")]
        # 使用同步方式调用
        result = llm_instance_1.invoke(
            model=config.llm.model_name,
            credentials={}, 
            prompt_messages=prompt,
            stream=False
        )
        assert result.message and result.message.content
        print(f"✅ LLM is available. Received response: {result.message.content[:50]}...")
        print("LLM instance test passed.\n")
        
    except Exception as e:
        print(f"❌ Failed to test LLM instance: {e}\n")
        raise

    # 4. 测试 Embedding 模型实例
    print("Testing Embedding instance instantiation and availability...")
    try:
        embedding_proxy_1 = ServiceClient(config.embedding)
        embedding_proxy_2 = ServiceClient(config.embedding)

        # 验证单例模式
        print("Verifying Embedding instance singleton pattern...")
        assert embedding_proxy_1._cache_key == embedding_proxy_2._cache_key
        embedding_instance_1 = embedding_proxy_1._get_instance()
        embedding_instance_2 = embedding_proxy_2._get_instance()
        assert embedding_instance_1 is embedding_instance_2
        assert isinstance(embedding_instance_1, GenericEmbedding)
        print("✅ Embedding instance is a singleton.")

        # 验证可用性
        print("Verifying Embedding instance availability...")
        result = embedding_instance_1.invoke(
            model=config.embedding.model_name,
            credentials={},
            texts=["hello world"]
        )
        assert isinstance(result.embeddings, list)
        assert len(result.embeddings) == 1
        assert isinstance(result.embeddings[0], list)
        assert len(result.embeddings[0]) > 0
        print(f"✅ Embedding model is available. Got embedding vector of dimension {len(result.embeddings[0])}.")
        print("Embedding instance test passed.\n")

    except Exception as e:
        print(f"❌ Failed to test Embedding instance: {e}\n")
        raise
    
    print("🎉 All services were successfully instantiated, tested for singleton property, and validated for availability!")

if __name__ == "__main__":
    test_service_client_instantiation()
