from openai import OpenAI
from openai import AsyncOpenAI
from typing import Callable, AsyncGenerator, Optional, Dict, Any, Union
import asyncio
import aiohttp # Needed for async HTTP requests
import json
from utils.common.logger_util import logger

def call_openai_sdk(**args):
    key = args['key']
    base_url = args['url']
    client = OpenAI(
        api_key=key,
        base_url=base_url,
    )
    del args['key']
    del args['url']
    if 'model_type' in args:
        del args['model_type']
    completion = client.chat.completions.create(
        **args
    )
    return completion

async def async_call_openai_sdk(**args):
    key = args['key']
    base_url = args['url']
    client = AsyncOpenAI(
        api_key=key,
        base_url=base_url,
    )
    
    del args['key']
    del args['url']
    if 'model_type' in args:
        del args['model_type']
    completion = await client.chat.completions.create(
        **args
    )
    return completion

async def async_call_openai_sdk_stream(callback: Optional[Callable] = None, **args) -> AsyncGenerator[Dict[str, Any], None]:
    """
    异步方式调用OpenAI流式API，支持回调函数处理每个流式响应块，实时传递到前端
    
    Args:
        callback: 可选的回调函数，用于处理每个流式响应块
        **args: 传递给OpenAI API的参数
    
    Yields:
        实时生成的响应块
    """
    key = args['key']
    base_url = args['url']
    client = AsyncOpenAI(
        api_key=key,
        base_url=base_url,
    )
    del args['key']
    del args['url']
    del args['model_type']
    # 确保设置stream=True
    args['stream'] = True
    
    # 必须使用await获取流对象，因为create方法返回的是协程
    stream = await client.chat.completions.create(**args)
    
    # 实时处理每个块
    async for chunk in stream:
        # 如果有回调函数，创建任务处理它
        if callback:
            asyncio.create_task(callback(chunk))
        
        # 同时生成响应块
        yield chunk

def call_openai_sdk_stream(callback: Optional[Callable] = None, **args):
    """
    同步方式调用OpenAI流式API，实时处理每个响应块
    
    Args:
        callback: 可选的回调函数，用于处理每个流式响应块
        **args: 传递给OpenAI API的参数
    
    Returns:
        流式响应迭代器
    """
    key = args['key']
    base_url = args['url']
    client = OpenAI(
        api_key=key,
        base_url=base_url,
    )
    del args['key']
    del args['url']
    del args['model_type']
    # 确保设置stream=True
    args['stream'] = True
    
    # 创建流式响应，这是一个迭代器
    return client.chat.completions.create(**args)

# 通用内容处理器
async def process_chunk_content(chunk: Any, model_type="openai") -> str:
    """提取不同模型响应中的文本内容 (更新以处理 OpenAICompatibleObject 和 delta 优先)

    Args:
        chunk: 模型返回的块数据 (可以是 OpenAI SDK 对象或我们的兼容对象)
        model_type: 模型类型，支持 "openai" 和 "opentrek"

    Returns:
        提取的文本内容
    """
    content = ""
    try:
        # 通用逻辑：优先检查 delta.content
        if hasattr(chunk, 'choices') and chunk.choices and hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content'):
            content = chunk.choices[0].delta.content or ""
        # 如果没有 delta，再检查 message.content (适用于非流式或某些特殊流式块)
        elif hasattr(chunk, 'choices') and chunk.choices and hasattr(chunk.choices[0], 'message') and hasattr(chunk.choices[0].message, 'content'):
            content = chunk.choices[0].message.content or ""

    except (AttributeError, IndexError, TypeError, KeyError) as e:
        logger.warning(f"警告：解析 {model_type} chunk 时出错: {e}, chunk: {chunk!r}") # 使用 !r 获取更详细的 repr
        content = ""

    return content


# 统一的流式API调用接口
async def async_call_stream(callback: Optional[Callable[[str], None]] = None, **params) -> AsyncGenerator[Any, None]:
    """
    统一的异步流式LLM调用接口，支持不同的模型类型 (更新 Yield 类型)

    Args:
        callback: 可选的回调函数，用于处理每个流式响应块的 content 字符串。
        **params: 参数字典，必须包含'model_type'指定模型类型

    Yields:
        流式响应块 (OpenAI SDK 对象或 OpenAICompatibleObject)
    """
    model_type = params.get('model_type', 'openai').lower()

    async def handle_chunk_content(chunk_content: str):
        # 这个内部函数只处理提取出的 content 字符串
        if callback:
            await callback(chunk_content)

    # 选择调用哪个流式函数
    if model_type == 'openai':
        # OpenAI 使用其自己的 stream 函数，内部处理回调和 yield chunk
        async for chunk in async_call_openai_sdk_stream(callback=handle_chunk_content, **params):
            yield chunk # Yield OpenAI SDK chunk object
    elif model_type == 'opentrek':
        # Opentrek 使用我们新的 stream 函数，内部处理回调和 yield chunk
        async for chunk in async_call_opentrek_stream(callback=handle_chunk_content, **params):
            yield chunk # Yield OpenAICompatibleObject chunk
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

# Helper class for making dicts attribute-accessible (used for both stream and non-stream)
class OpenAICompatibleObject:
    def __init__(self, data):
        # Store the raw data dictionary
        self._data = data

    def __getattr__(self, name):
        # Allow accessing dictionary keys as attributes
        if name in self._data:
            value = self._data[name]
            # If the value is a dictionary, wrap it recursively
            if isinstance(value, dict):
                return OpenAICompatibleObject(value)
            # If the value is a list, wrap dictionary items within the list
            elif isinstance(value, list):
                return [OpenAICompatibleObject(item) if isinstance(item, dict) else item for item in value]
            # Otherwise, return the plain value
            return value
        # If the attribute doesn't exist in the dictionary, raise AttributeError
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __getitem__(self, key):
        # Allow dictionary-style access as well
        # Apply recursive wrapping for dictionary values accessed via keys
        value = self._data[key]
        if isinstance(value, dict):
            return OpenAICompatibleObject(value)
        elif isinstance(value, list):
            return [OpenAICompatibleObject(item) if isinstance(item, dict) else item for item in value]
        return value

    def __repr__(self):
        # Provide a representation similar to a dictionary
        return repr(self._data)

async def async_call_opentrek(**params):
    """
    异步方式调用Opentrek API，支持流式和非流式输出, 输出格式兼容OpenAI。

    Args:
        **params: 传递给Opentrek API的参数

    Returns:
        如果是非流式请求，返回与OpenAI格式兼容的完整响应对象 (OpenAICompatibleObject)
        如果是流式请求，返回一个可以用async for迭代的流对象 (OpentrekStream)
    """
    url = params['url']
    key = params['key']
    # model = params['model'] # model 参数通常在 payload 中指定，这里注释掉避免混淆
    messages = params['messages']
    is_stream = params.get('stream', False)

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"{key}"
    }

    # 从 params 中提取 opentrek 支持的 payload 参数，或使用默认值
    payload = {
        "model": params.get("model", "qwen_vllm"), # 使用传入的model或默认值
        "messages": messages,
        "stream": is_stream,
        **{k: v for k, v in params.items() if k in ["temperature", "max_tokens", "top_p"]} # 添加其他可选参数
    }
    logger.info(f"Opentrek Request Payload: {payload}")

    session = aiohttp.ClientSession()
    try:
        response = await session.post(url, headers=headers, json=payload)
        response.raise_for_status() # 检查HTTP错误

        if not is_stream:
            # 非流式请求
            result_dict = await response.json()
            await session.close() # 读取完响应后关闭会话
            logger.info(f"Opentrek Non-Stream Response: {result_dict}")
            # 使用通用包装类返回
            return OpenAICompatibleObject(result_dict)
        else:
            # 流式请求
            logger.info("Opentrek Stream Request Initiated")
            # 返回异步迭代器实例
            return OpentrekStream(response, session)

    except aiohttp.ClientResponseError as e:
        # 处理 HTTP 错误
        logger.error(f"Opentrek API HTTP Error: {e.status} {e.message}")
        error_body = await response.text()
        logger.error(f"Error Body: {error_body}")
        await session.close() # 确保关闭会话
        raise # 重新抛出异常
    except aiohttp.ClientError as e:
        # 处理其他 aiohttp 客户端错误
        logger.error(f"Opentrek API Client Error: {e}")
        await session.close()
        raise
    except Exception as e:
        # 处理其他意外错误
        logger.error(f"Unexpected error during Opentrek call: {e}")
        # 尝试关闭会话，即使 response 可能未定义
        if 'session' in locals() and not session.closed:
             await session.close()
        raise


class OpentrekStream:
    """异步迭代器，用于处理Opentrek的流式响应，并包装成兼容OpenAI的格式。"""
    def __init__(self, response: aiohttp.ClientResponse, session: aiohttp.ClientSession):
        self.response = response
        self.session = session
        logger.debug("OpentrekStream initialized.")

    def __aiter__(self):
        return self

    async def __anext__(self):
        # 持续读取，直到流结束或发生错误
        while True:
            try:
                # 从响应内容中读取一行
                line_bytes = await self.response.content.readline()
            except (aiohttp.ClientPayloadError, aiohttp.ClientConnectionError) as e:
                logger.error(f"Opentrek stream reading error: {e}")
                if not self.session.closed:
                    await self.session.close()
                raise StopAsyncIteration from e # 出错时停止迭代

            # 如果读取到空字节，表示流正常结束
            if not line_bytes:
                logger.info("Opentrek stream finished (empty readline).")
                if not self.session.closed:
                    await self.session.close()
                raise StopAsyncIteration # 结束迭代

            # 解码并去除首尾空白
            line = line_bytes.decode('utf-8').strip()

            # 跳过SSE中的空行
            if not line:
                continue

            # 检查SSE标准结束标记 "data: [DONE]"
            if line == "data: [DONE]":
                logger.info("Opentrek stream finished ([DONE] marker).")
                if not self.session.closed:
                    await self.session.close()
                raise StopAsyncIteration # 结束迭代

            # 处理SSE数据行 "data: ..."
            if line.startswith("data:"):
                # 提取JSON数据部分
                data_str = line[len("data:"):].strip()
                # 确保提取的数据非空
                if not data_str:
                    continue
                try:
                    # 解析JSON数据
                    chunk_data = json.loads(data_str)
                    # 使用通用包装类返回，使其属性可访问
                    # logger.debug(f"Yielding chunk: {chunk_data}") # Log chunk before yielding
                    return OpenAICompatibleObject(chunk_data)
                except json.JSONDecodeError:
                    logger.warning(f"无法解析Opentrek流中的JSON数据: {data_str}")
                    continue # 跳过无效JSON，继续读取下一行

            # logger.debug(f"Ignoring non-data line in stream: {line}") # 可以取消注释来调试非预期行


# (确保 OpenAICompatibleObject 类定义在 async_call_opentrek 函数之前或之后都可以，只要它在被使用时已定义)

# 新的 async_call_opentrek_stream 函数
async def async_call_opentrek_stream(callback: Optional[Callable[[str], None]] = None, **params) -> AsyncGenerator:
    """
    异步方式调用Opentrek流式API，行为类似 async_call_openai_sdk_stream。
    支持回调函数处理每个块的内容字符串。

    Args:
        callback: 可选的回调函数，接收每个块的 content 字符串。
        **params: 传递给 Opentrek API 的参数 (需包含 url, key, messages 等)。

    Yields:
        实时生成的兼容 OpenAI 格式的响应块 (OpenAICompatibleObject)。
    """
    # 强制设置 stream=True 并调用核心函数获取流对象
    params['stream'] = True
    try:
        stream = await async_call_opentrek(**params)
        
        # 迭代流对象
        async for chunk in stream:
            # logger.debug(f"async_call_opentrek_stream received chunk: {chunk}") # Optional debug log
            # 如果有回调函数，提取内容并调用
            if callback:
                content = ""
                try:
                    # 优先检查 delta 结构 (类似 OpenAI stream)
                    if hasattr(chunk, 'choices') and chunk.choices and hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content'):
                        content = chunk.choices[0].delta.content or ""
                    # 如果没有 delta，再尝试检查 message 结构 (以防万一)
                    elif hasattr(chunk, 'choices') and chunk.choices and hasattr(chunk.choices[0], 'message') and hasattr(chunk.choices[0].message, 'content'):
                       content = chunk.choices[0].message.content or ""

                except (AttributeError, IndexError, TypeError) as e:
                    logger.warning(f"无法从 Opentrek chunk 中提取 content: {e}, chunk: {chunk!r}") # 使用 !r 获取更详细的 repr
                    content = ""

                if content:
                    # logger.debug(f"Calling callback with content: {content}") # Optional debug log
                    # 使用 asyncio.create_task 异步执行回调，避免阻塞流处理
                    asyncio.create_task(callback(content))
            
            # 无论是否有回调，都生成原始的兼容对象 chunk
            yield chunk

    except Exception as e:
        logger.error(f"Error in async_call_opentrek_stream: {e}")
        # 根据需要决定是否重新抛出异常
        raise

