"""
模型服务全局访问点与工具 (The "Model Service Waiter")

本模块的职责:
1.  **提供统一的、全局的模型服务访问入口**。应用中任何需要调用 LLM 或 Embedding
    模型的代码，都应该从本模块导入 `llm_provider` 和 `embedding_provider`。
2.  **实现懒加载 (Lazy Loading)**。通过 `LazyProvider` 代理类，将模型服务的
    实际初始化操作（可能涉及加载大模型到内存/显存）推迟到第一次实际使用时。

与 `modules.llm.clients` 的关系:
-   本模块是应用层代码的 **“服务员”**。
-   `modules.llm.clients` 模块是 **“后厨”**，负责实际创建和管理模型实例。
-   当服务员（本模块的 `LazyProvider`）第一次被请求服务时，它才会去后厨
    （调用 `get_llm_provider()` 或 `get_embedding_provider()`）获取真正的模型实例。
"""

from loguru import logger
from modules.llm.clients import get_llm_provider, get_embedding_provider


class LazyProvider:
    """
    一个懒加载代理对象，与 utils.db.common.client_util.LazyClient 功能相同。
    它将实际提供者的实例化推迟到第一次访问其任何属性时。
    """
    def __init__(self, creator_func):
        self._creator = creator_func
        self._instance = None

    def _get_instance(self):
        """获取或创建实际的提供者实例。"""
        if self._instance is None:
            logger.info(f"Lazily creating provider instance using '{self._creator.__name__}'")
            try:
                self._instance = self._creator()
                logger.info("Provider instance created successfully.")
            except Exception as e:
                logger.error(f"Error during lazy creation of provider: {e}", exc_info=True)
                raise
        return self._instance

    def __getattr__(self, name):
        """将属性访问转发给真实的提供者实例。"""
        logger.debug(f"Delegating attribute '{name}' to lazy-loaded provider.")
        instance = self._get_instance()
        return getattr(instance, name)

    @property
    def __class__(self):
        """代理对象的 class 属性，方便类型检查。"""
        return self._get_instance().__class__

    def __repr__(self):
        """提供一个有用的表示，而不触发实例化。"""
        if self._instance is None:
            return f"<LazyProvider for {self._creator.__name__} (not initialized)>"
        return repr(self._get_instance())

# ==================== 全局模型服务访问点 ====================
llm_provider = LazyProvider(get_llm_provider)
embedding_provider = LazyProvider(get_embedding_provider)


__all__ = [
    'llm_provider',
    'embedding_provider'
]
