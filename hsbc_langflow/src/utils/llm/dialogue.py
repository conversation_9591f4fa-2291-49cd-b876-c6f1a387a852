import re
from typing import List, Dict, Any, Optional, Union, Tuple
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.common.logger_util import logger
class DialogProcessor:
    """多轮对话处理器，用于组合历史对话记录"""
    
    def __init__(self, history_rounds: int = 3):
        """
        初始化对话处理器
        
        Args:
            history_rounds: 历史对话记录保留轮数，默认为3轮
        """
        self.history_rounds = history_rounds
        self.logger = logger
    
    def _extract_user_message(self, message: str) -> str:
        """从用户消息中提取<query></query>标签之间的内容
        
        Args:
            message: 原始用户消息
            
        Returns:
            提取后的用户消息内容
        """
        pattern = r'<query>(.*?)</query>'
        match = re.search(pattern, message, re.DOTALL)
        if match:
            return match.group(1).strip()
        return message  # 如果没有标签，返回原始消息
    
    def _extract_ai_message(self, message: str) -> str:
        """从AI消息中提取</think>标识符后的部分
        
        Args:
            message: 原始AI消息
            
        Returns:
            提取后的AI回复内容
        """
        parts = message.split("</think>", 1)
        if len(parts) > 1:
            return parts[1].strip()
        return message  # 如果没有标识符，返回原始消息
    
    def process_dialog_from_frontend(self, 
                                  dialog_history: List[Dict], 
                                  rounds: Optional[int] = None) -> str:
        """
        处理来自前端的对话历史记录，格式化为Text2SQL的输入
        用户提问保留历史三轮，AI回答只保留最新一轮
        
        Args:
            dialog_history: 前端发送的历史对话记录  [
                    {
                        "requestId": "1909791700872839168",
                        "prompt": [
                            {
                                "role": "system",
                                "content": "默认提示"
                            }
                        ],
                        "inMessage": "你好",
                        "outMessage": "你好!",
                        "createAt": 1744164798420
                    },
                    {
                        "requestId": "1909791701128691712",
                        "prompt": [
                            {
                                "role": "system",
                                "content": "默认提示"
                            }
                        ],
                        "inMessage": "天气是什么",
                        "createAt": 1744164798420
                    }
                ],
            current_question: 当前用户问题
            rounds: 要保留的历史轮数，如果不指定则使用默认值
            
        Returns:
            组合后的问题字符串
        """
        if rounds is not None:
            self.history_rounds = rounds
            
        # 如果没有历史记录，直接返回当前问题
        if not dialog_history:
            return current_question
        current_question = dialog_history[-1].get("inMessage", "")
        # 获取最近的n轮对话
        history = dialog_history[:-1]
        recent_history = history[-self.history_rounds:] if len(history) > self.history_rounds else history
        
        # 构建组合问题
        combined_question = "【历史对话】\n"
        
        # 添加历史用户提问
        for i, dialog in enumerate(recent_history):
            user_message = dialog.get("inMessage", "")
            processed_user_message = self._extract_user_message(user_message)
            
            # 调整轮次计算方式，使最后一轮为第三轮
            round_num = i + 1
            combined_question += f"【第{round_num}轮用户问题】{processed_user_message}\n"
        
        # 只添加最新一轮的AI回答
        if recent_history:
            last_dialog = recent_history[-1]
            ai_message = last_dialog.get("outMessage", "")
            processed_ai_message = self._extract_ai_message(ai_message)
            # 使用第N轮AI回答的格式
            round_num = len(recent_history)
            combined_question += f"【第{round_num}轮AI回答】{processed_ai_message}\n\n"
        
        # 添加当前用户问题
        combined_question += f"【本轮用户问题】{current_question}"
        self.logger.info(f"[用户问题]: {combined_question}")
        return combined_question


# 示例使用方法
if __name__ == "__main__":
    # 模拟前端传来的历史对话数据
    sample_history = [
        {
            "requestId": "1909791700872839168",
            "prompt": [
                {
                    "role": "system",
                    "content": "默认提示"
                }
            ],
            "inMessage": "2.1.1 境内农、林、牧、渔业的短期人民币本金是多少？",
            "outMessage": "SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='01' AND IS_CBIRC_LOAN='Y' AND LOAN_PURPOSE LIKE 'A%';",
            "createAt": 1744164798420
        },
        {
            "requestId": "1909791700872839168",
            "prompt": [
                {
                    "role": "system",
                    "content": "默认提示"
                }
            ],
            "inMessage": "2.21.1.1 境内信用卡贷款的短期人民币本金是多少？",
            "outMessage": "SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='01' AND IS_CBIRC_LOAN='Y' AND BUSSINESS_TYPE='19';",
            "createAt": 1744164798420
        },
        {
            "requestId": "1909791700872839168",
            "prompt": [
                {
                    "role": "system",
                    "content": "默认提示"
                }
            ],
            "inMessage": "3.1.1 境外贷款的短期人民币本金是多少？",
            "outMessage": "SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='01' AND IS_CBIRC_LOAN='Y' AND LOAN_PURPOSE LIKE 'Z%'",
            "createAt": 1744164798420
        },
        {
            "requestId": "1909791701128691712",
            "prompt": [
                {
                    "role": "system",
                    "content": "默认提示"
                }
            ],
            "inMessage": "5.4.1 数字要素驱动业的短期人民币本金是多少？",
            "createAt": 1744164798420
        }
    ]
    
    # 当前用户问题
    
    # 创建对话处理器实例
    processor = DialogProcessor(history_rounds=3)
    
    # 处理对话历史
    result = processor.process_dialog_from_frontend(sample_history)
    
    # 打印结果
    print(result)