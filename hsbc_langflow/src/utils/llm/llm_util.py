"""
模型服务全局访问点

本模块的职责是提供统一的、全局的、懒加载的模型服务访问入口。
它利用 `ServiceClient` 直接从全局配置中创建和管理单例服务实例。

应用中任何需要调用 LLM 或 Embedding 模型的代码，都应该从本模块导入
`llm_provider` 和 `embedding_provider`。
"""

from utils.common.service_client import ServiceClient
from utils.common.config_util import config

# ==================== 全局模型服务访问点 ====================
# 通过 ServiceClient，我们直接用全局配置对象来定义服务。
# ServiceClient 会自动处理懒加载、单例缓存和实例化。

llm_provider = ServiceClient(config.llm)
embedding_provider = ServiceClient(config.embedding)


__all__ = [
    'llm_provider',
    'embedding_provider'
]
