import json # 仅用于打印下面的示例数据
from utils.common.logger_util import logger
# from loguru import logger


def generate_schema_string(selected_data, merged_info,col_data_examples):
    """
    根据选择的表和列以及包含所有列信息的大字典生成Schema字符串。

    Args:
        selected_data (dict): 字典，键是表名，值是该表中选择的真实列名列表。
                                例如: {'table1': ['col_real_name1', ...], ...}
        merged_info (dict):   包含所有列详细信息的大字典。
                                键的格式应为 f"{table_name}_{col_real_name}"。
                                值是包含列属性（如 col_real_name, col_type, col_name_cn,
                                col_desc, col_data_example）的字典。
        db_id (str):          用于 Schema 头部的数据库 ID。

    Returns:
        str: 格式化的 Schema 字符串。
    """
    table_schemas = {}
    # 按表分组收集所选列的详细信息
    for table_name, real_column_names in selected_data.items():
        if table_name not in table_schemas:
            table_schemas[table_name] = []
        for real_col_name in real_column_names:
            # 根据表名和真实列名构造查找键
            lookup_key = real_col_name
            if lookup_key in merged_info:
                col_details = merged_info[lookup_key]
                # 确保信息匹配 (可选，但更安全)
                if col_details.get('col_name') == real_col_name:
                     table_schemas[table_name].append(col_details)
                else:
                     # 如果需要，可以添加警告或错误处理
                    print(f"警告: 键 '{lookup_key}' 的信息与预期表名/列名不匹配。已跳过。")
            else:
                # 如果需要，可以添加警告或错误处理
                print(f"警告: 列键 '{lookup_key}' 在 merged_info 中未找到。已跳过。")

    # 开始构建 Schema 字符串
    schema_parts = [f"【Schema】"]
    for table_name, column_details_list in table_schemas.items():
        # 如果这个表没有任何成功匹配的列，则跳过
        if not column_details_list:
            continue

        # 添加表头，使用默认描述 "自动导入的表"
        schema_parts.append(f"# Table: {table_name}, 自动导入的表")
        schema_parts.append("[")

        column_lines = []
        for details in column_details_list:
            # 提取所需信息，提供默认值以防万一
            real_name = details.get('col_real_name', '未知列名')
            # 确保获取 col_name，并提供默认值
            col_name = details.get('col_name', real_name) # 如果 col_name 不存在，回退到 real_name 或 '未知列名'
            col_type = details.get('col_type')
            cn_name = details.get('col_name_cn', '无中文名')
            desc = details.get('col_desc', '无描述')
            
            # 安全获取example值
            example = details.get('col_data_example')
            # logger.info(f"{col_name}: {example}")
            # 使用 col_name 从 col_data_examples 查找示例（如果 merged_info 中没有）
            if not example and col_name != '未知列名': # 使用 col_name 进行查找
                example = col_data_examples.get(col_name)
                # logger.info(f"example: {example}")
            # 构建单列的字符串部分
            col_str_parts = [f"({col_name}"]
            # 如果类型存在 (不为 None 或空字符串)，则添加类型
            if col_type:
                col_str_parts.append(f":{col_type}")

            # 添加中文名和描述 (始终添加)
            col_str_parts.append(f", {cn_name}")
            col_str_parts.append(f", {desc}")

            # 如果示例数据存在 (不为 None 或空)，则添加示例
            if example:
                 # 使用 str() 来处理可能的列表或其他类型
                col_str_parts.append(f", Examples: {str(example)}")

            col_str_parts.append(")")
            column_lines.append("".join(col_str_parts))

        # 将所有列的行用 ',\n' 连接起来
        if column_lines:
             schema_parts.append(",\n".join(column_lines))

        # 添加表的结束括号
        schema_parts.append("]")

    # 用换行符连接所有部分，生成最终的 Schema 字符串
    schema_str = "\n".join(schema_parts)
    logger.info(f"schema_str: {schema_str}")
    return schema_str



if __name__ == "__main__":
        # 假设这是你提供的包含所有列信息的大字典
    # 在实际使用中，你需要将这个变量替换成你实际的字典对象
    # 注意：这里的示例数据结合了你提供的结构和为了匹配你目标格式而添加的数据
    merged_column_info = {
    "adm_lon_varoius_loan_bal": {
        "col_id": "1445",
        "col_code": "meta#13.adm_lon_varoius#1445",
        "table_code": "13.adm_lon_varoius",
        "table_name": "adm_lon_varoius",
        "col_name": "adm_lon_varoius_loan_bal",
        "col_real_name": "loan_bal",
        "col_name_cn": "本金余额",
        "col_desc": "本金余额",
        "col_type": "NUMBER",
        "col_data_example": None # 示例：无 Example 数据
    },
    "adm_lon_varoius_loan_bal_cny   ": {
        "col_id": "1446",
        "col_code": "meta#13.adm_lon_varoius#1446",
        "table_code": "13.adm_lon_varoius",
        "table_name": "adm_lon_varoius",
        "col_name": "adm_lon_varoius_loan_bal_cny",
        "col_real_name": "loan_bal_cny",
        "col_name_cn": "本金余额_折人民币",
        "col_desc": "本金余额_折人民币",
        "col_type": "NUMBER",
        "col_data_example": None # 示例：无 Example 数据
    },
    "adm_lon_varoius_corp_hold_type": {
        "col_id": "1419",
        "col_code": "meta#13.adm_lon_varoius#1419",
        "table_code": "13.adm_lon_varoius",
        "table_name": "adm_lon_varoius",
        "col_name": "adm_lon_varoius_corp_hold_type",
        "col_real_name": "corp_hold_type",
        "col_name_cn": "企业控股类型",
        "col_desc": "企业控股类型",
        "col_type": "STRING",
        "col_data_example": ["国有绝对控股", "外商投资企业", "国有实际控制"] # 示例：有 Example 数据
    },
    "adm_lon_varoius_loan_bal_cny": {
        "col_id": "1446",
        "col_code": "meta#13.adm_lon_varoius#1446",
        "table_code": "13.adm_lon_varoius",
        "table_name": "adm_lon_varoius",
        "col_name": "adm_lon_varoius_loan_bal_cny",
        "col_real_name": "loan_bal_cny",
        "col_name_cn": "本金余额_折人民币",
        "col_desc": "本金余额_折人民币",
        "col_type": "NUMBER",
        "col_data_example": None
    },
    "adm_pub_branch_level_info_bank_code": {
        "col_id": "1524",
        "col_code": "meta#13.adm_pub_branch_level_info#1524",
        "table_code": "13.adm_pub_branch_level_info",
        "table_name": "adm_pub_branch_level_info",
        "col_name": "adm_pub_branch_level_info_bank_code",
        "col_real_name": "bank_code",
        "col_name_cn": "no_comment",
        "col_desc": "no_comment",
        "col_type": "STRING",
        "col_data_example": None
    },
    "adm_lon_varoius_term_type": { # 添加的示例数据以匹配你的目标输出
        "table_name": "adm_lon_varoius",
        "col_real_name": "term_type",
        "col_name_cn": "自动生成的TERM_TYPE字段",
        "col_desc": "自动生成的TERM_TYPE字段",
        "col_type": "VARCHAR",
        "col_data_example": ["02", "01"]
    },
    "adm_lon_varoius_loan_amt_cny": { # 添加的示例数据以匹配你的目标输出
        "table_name": "adm_lon_varoius",
        "col_real_name": "loan_amt_cny",
        "col_name_cn": "自动生成的LOAN_AMT_CNY字段",
        "col_desc": "自动生成的LOAN_AMT_CNY字段",
        "col_type": "DOUBLE PRECISION",
        "col_data_example": [1680000.0, 2300000.0, 675000.0]
    },
    "adm_lon_varoius_digital_industry_type": { # 添加的示例数据以匹配你的目标输出
        "table_name": "adm_lon_varoius",
        "col_real_name": "digital_industry_type",
        "col_name_cn": "自动生成的DIGITAL_INDUSTRY_TYPE字段",
        "col_desc": "自动生成的DIGITAL_INDUSTRY_TYPE字段",
        "col_type": None, # 示例：无类型数据
        "col_data_example": ["02", "03", "01"]
    }
    }

    # 假设这是你输入的包含所选表和列的字典
    # 在实际使用中，你需要将这个变量替换成你实际的选择
    selected_tables_and_columns = {
        "1": ["adm_lon_varoius_loan_bal", "adm_lon_varoius_loan_bal_cny", "adm_lon_varoius_corp_hold_type", "adm_lon_varoius_term_type", "adm_lon_varoius_loan_amt_cny", "adm_lon_varoius_digital_industry_type"],
    }
    col_data_examples = {
        "adm_lon_varoius_data_dt": ["2024-01-01", "2024-01-02", "2024-01-03"],
        "adm_lon_varoius_cust_no": ["C10000001", "C10000002", "C10000003"],
        "adm_lon_varoius_busi_no": ["B202401010001", "B202401010002", "B202401010003"],
        "adm_lon_varoius_is_high_tech_industry": ["Y", "Y", "N"],
        "adm_lon_varoius_digital_industry_type": ["01", "02", "03"],
        "adm_lon_varoius_is_kownledge_industry": ["Y", "Y", "N"],
        "adm_lon_varoius_bussiness_type": ["19", "17", "020299"],
        "adm_lon_varoius_loan_bal": ["80000.0000", "150000.0000", "450000.0000"],
        "adm_lon_varoius_loan_bal_cny": ["80000.0000", "150000.0000", "450000.0000"],
        "adm_lon_varoius_loan_bal_usd": ["11034.4828", "20689.6552", "62068.9655"],
        "adm_lon_varoius_term_type": ["01", "02"],
        "adm_lon_varoius_is_cbirc_loan": ["Y", "N"],
        "adm_lon_varoius_loan_purpose": ["A", "B", "C"],
        "bdm_acc_creditcard_installment_acct_bal": ["100000.0000", "200000.0000", "500000.0000"],
        "bdm_acc_creditcard_installment_stageing_term": ["12", "24", "36"],
    }
    # --- 使用示例 ---
    # 调用函数生成 Schema
    # 确保将 merged_column_info 和 selected_tables_and_columns 替换为你的实际数据
    output_schema = generate_schema_string(selected_tables_and_columns, merged_column_info,col_data_examples) # 使用一个示例 DB_ID

    # 打印生成的 Schema
    print(output_schema)