def transform_sql_to_if_format(select_expressions, conditions):
    # 支持的聚合函数列表（大写）
    VALID_AGG_FUNCTIONS = {'SUM', 'COUNT', 'MIN', 'MAX', 'AVG'}

    # 递归收集 case_when 的别名字段
    def collect_alias_cols(conds, alias_cols=None):
        if alias_cols is None:
            alias_cols = set()

        if not isinstance(conds, list):
            return alias_cols

        for cond in conds:
            if 'groups' in cond:
                collect_alias_cols(cond['groups'], alias_cols)
            if 'filter' in cond and 'case_when' in cond['filter'] and cond['filter'].get('is_alias', False):
                alias_cols.add(cond['filter']['modelColId'])

        return alias_cols

    # 解析条件部分，生成条件字符串
    def parse_conditions(conds):
        if not conds:
            return ""

        # 如果只有一个条件节点，处理其 groups 并尊重 combine
        if len(conds) == 1 and 'combine' in conds[0] and 'groups' in conds[0]:
            combine = conds[0]['combine']
            children_conditions = [parse_conditions([child]) for child in conds[0]['groups']]
            children_conditions = [c for c in children_conditions if c]  # 过滤空条件
            if not children_conditions:
                return ""
            if len(children_conditions) == 1:
                return children_conditions[0]
            return f" {combine} ".join(children_conditions)

        result = []
        for cond in conds:
            if 'combine' in cond and 'groups' in cond:
                # 处理嵌套条件
                child_result = parse_conditions(cond['groups'])
                if child_result:
                    result.append(f"({child_result})")
            elif 'filter' in cond:
                f = cond['filter']
                if not f.get('val_list'):
                    f['val_list'] = ['NULL']

                # 检查是否有 case_when 并处理
                if 'case_when' in f and f.get('is_alias', False):
                    target_value = f['val_list'][0] if f['val_list'] else 'NULL'
                    for case in f['case_when']:
                        if case['value'] == target_value:
                            if case['where_parse'] == 'ELSE':
                                non_else_conditions = [c['where_parse'] for c in f['case_when'] if
                                                       c['where_parse'] != 'ELSE']
                                if non_else_conditions:
                                    condition = f"not ({' or '.join(non_else_conditions)})"
                                else:
                                    condition = ""
                            else:
                                condition = case['where_parse']
                            break
                    else:
                        condition = ""
                else:
                    # 处理普通条件
                    col = f['modelColId']
                    if f['op'] == '=':
                        condition = f"{col} = {f['val_list'][0]}"
                    elif f['op'] == 'like':
                        condition = f"{col} = '{f['val_list'][0]}'"
                    elif f['op'] == 'between':
                        condition = f"{col} between {f['start']} and {f['end']}"
                    elif f['op'] == '!=':
                        condition = f"{col} != {f['val_list'][0]}"
                    elif f['op'] == '>':
                        condition = f"{col} > {f['val_list'][0]}"
                    elif f['op'] == '<':
                        condition = f"{col} < {f['val_list'][0]}"
                    elif f['op'] == 'in':
                        values = [f"'{v}'" if isinstance(v, str) else str(v) for v in f['val_list']]
                        condition = f"{col} in ({', '.join(values)})"
                    else:
                        condition = f"{col} = {f['val_list'][0]}"

                    if f.get('if_not', False):
                        condition = f"not {condition}"

                if condition:
                    result.append(condition)

        if not result:
            return ""
        # 默认使用 AND 拼接（如果没有顶层 combine）
        return ' and '.join(result)

    # 获取条件字符串和别名字段
    condition_str = parse_conditions(conditions)
    alias_cols = collect_alias_cols(conditions)

    # 如果没有条件和别名，返回原始表达式
    if not condition_str and not alias_cols:
        return select_expressions, "", []

    # 处理 select 表达式
    result = []
    processed_agg_funcs = []  # 使用列表存储所有被处理的聚合函数
    for expr in select_expressions.split(','):
        expr = expr.strip()
        if '(' not in expr or ')' not in expr:
            if expr not in alias_cols:
                result.append(expr)
            continue

        agg_func = expr.split('(')[0].strip()
        col_name = expr.split('(')[1].rstrip(')').strip()

        if col_name in alias_cols:
            continue

        if agg_func.upper() in VALID_AGG_FUNCTIONS and condition_str:
            formatted = f"{agg_func.upper()}_IF('{condition_str}',{col_name})"
            # 记录所有被处理的聚合函数
            processed_agg_funcs.append(f"{agg_func.upper()}({col_name})")
        else:
            formatted = expr

        result.append(formatted)

    return ','.join(result), condition_str, processed_agg_funcs


def parse_where_conditions(conds):
    # 从transform_sql_to_if_format函数中提取的parse_conditions逻辑
    def parse_conditions(conds):
        if not conds:
            return ""

        # 如果只有一个条件节点，处理其 groups 并尊重 combine
        if len(conds) == 1 and 'combine' in conds[0] and 'groups' in conds[0]:
            combine = conds[0]['combine']
            children_conditions = [parse_conditions([child]) for child in conds[0]['groups']]
            children_conditions = [c for c in children_conditions if c]  # 过滤空条件
            if not children_conditions:
                return ""
            if len(children_conditions) == 1:
                return children_conditions[0]
            return f" {combine} ".join(children_conditions)

        result = []
        for cond in conds:
            if 'combine' in cond and 'groups' in cond:
                # 处理嵌套条件
                child_result = parse_conditions(cond['groups'])
                if child_result:
                    result.append(f"({child_result})")
            elif 'filter' in cond:
                f = cond['filter']
                if not f.get('val_list'):
                    f['val_list'] = ['NULL']

                # 检查是否有 case_when 并处理
                if 'case_when' in f and f.get('is_alias', False):
                    target_value = f['val_list'][0] if f['val_list'] else 'NULL'
                    for case in f['case_when']:
                        if case['value'] == target_value:
                            if case['where_parse'] == 'ELSE':
                                non_else_conditions = [c['where_parse'] for c in f['case_when'] if
                                                       c['where_parse'] != 'ELSE']
                                if non_else_conditions:
                                    condition = f"not ({' or '.join(non_else_conditions)})"
                                else:
                                    condition = ""
                            else:
                                condition = case['where_parse']
                            break
                    else:
                        condition = ""
                else:
                    # 处理普通条件
                    col = f['modelColId']
                    if f['op'] == '=':
                        condition = f"{col} = {f['val_list'][0]}"
                    elif f['op'] == 'like':
                        condition = f"{col} = '{f['val_list'][0]}'"
                    elif f['op'] == 'between':
                        condition = f"{col} between {f['start']} and {f['end']}"
                    elif f['op'] == '!=':
                        condition = f"{col} != {f['val_list'][0]}"
                    elif f['op'] == '>':
                        condition = f"{col} > {f['val_list'][0]}"
                    elif f['op'] == '<':
                        condition = f"{col} < {f['val_list'][0]}"
                    elif f['op'] == 'in':
                        values = [f"'{v}'" if isinstance(v, str) else str(v) for v in f['val_list']]
                        condition = f"{col} in ({', '.join(values)})"
                    else:
                        condition = f"{col} = {f['val_list'][0]}"

                    if f.get('if_not', False):
                        condition = f"not {condition}"

                if condition:
                    result.append(condition)

        if not result:
            return ""
        # 默认使用 AND 拼接（如果没有顶层 combine）
        return ' and '.join(result)
    
    return parse_conditions(conds)



