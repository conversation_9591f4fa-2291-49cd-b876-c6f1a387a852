from modules.pg_database.utils.sqlparser import *
from modules.pg_database.utils.conditional_correction import process_filter_conditions
from modules.pg_database.utils.sql_transform import transform_sql_to_if_format
from modules.pg_database.utils.sql_transform import parse_where_conditions
from modules.pg_database.utils.sql_regex_update import update_sql_with_regex
from modules.pg_database.utils.sql_ast_update import update_sql_with_ast
import re
import sqlglot
import copy
from utils.common.logger_util import logger
from typing import Dict, Any, List, Union
from utils.db.get_col_id import find_field_from_merged_info, get_col_id_from_mysql, get_col_id_robust

# Helper function to replace column names with ${col_id} in expression strings
def replace_cols_in_expr(expr_str: str, table_name: str, mysql_client: Any, pg_client: Any, model_id: str) -> str:
    """
    Parses an SQL expression string, finds column names, and replaces them with ${col_id}.
    using robust lookup (MySQL direct then vector fallback).

    Args:
        expr_str: The SQL expression string (e.g., "SUM(col_a + col_b)").
        table_name: The primary table name to use for looking up columns.
        mysql_client: MySQL client instance.
        pg_client: Pgvector client instance.
        model_id: Model ID for vector search fallback.

    Returns:
        The expression string with column names replaced by ${col_id}.
    """
    if not expr_str or not table_name:
        return expr_str
    if not mysql_client and not pg_client:
        logger.warning("Both MySQL and Pgvector clients missing in replace_cols_in_expr, cannot replace col names.")
        return expr_str
    try:
        parsed_expr = sqlglot.parse_one(expr_str)
        modified_expr_str = expr_str
        columns = {col.this.name for col in parsed_expr.find_all(sqlglot.exp.Column)}

        for col_name in columns:
            # Use robust lookup to get column details dictionary
            col_details = get_col_id_robust(mysql_client, pg_client, table_name, col_name, model_id)
            
            # Extract col_id from the dictionary
            if col_details and 'col_id' in col_details:
                col_id = col_details['col_id'] # Assumes col_id is already string or convertible
                pattern = r'\b' + re.escape(col_name) + r'\b'
                replacement = f'${{{str(col_id)}}}'
                modified_expr_str = re.sub(pattern, replacement, modified_expr_str)
            else:
                logger.warning(f"Robust lookup failed to return details with col_id for column '{col_name}' in table '{table_name}' within expression. Keeping original name.")

        return modified_expr_str
    except Exception as e:
        logger.warning(f"Failed to parse or replace columns in expression '{expr_str}': {e}")
        return expr_str # Return original on error

# Helper function to recursively replace column names with col_id in dicts/lists
def replace_col_names_recursive(item: Union[Dict, List, Any], table_name: str, mysql_client: Any, pg_client: Any, model_id: str) -> Union[Dict, List, Any]:
    """
    Recursively traverses dictionaries, replacing column names found
    ONLY in the key 'modelColId' using robust lookup.
    It does NOT process arbitrary strings in lists or other dict values.

    Args:
        item: The dictionary or list to process.
        table_name: The primary table name.
        mysql_client: MySQL client instance.
        pg_client: Pgvector client instance.
        model_id: Model ID for vector search fallback.

    Returns:
        The item with modelColId values potentially replaced by col_id.
    """
    if not table_name:
        return item
    if not mysql_client and not pg_client:
        # Log is handled inside get_col_id_robust if needed
        # logger.warning("Both MySQL and Pgvector clients missing...")
        return item

    if isinstance(item, dict):
        new_dict = {}
        for key, value in item.items():
            # ONLY check for 'modelColId' key for replacement
            if key == 'modelColId' and isinstance(value, str):
                col_identifier = value
                # Use robust lookup to get column details dictionary
                col_details = get_col_id_robust(mysql_client, pg_client, table_name, col_identifier, model_id)
                
                # Extract col_id and replace if found
                if col_details and 'col_id' in col_details:
                    col_id = col_details['col_id']
                    new_dict[key] = str(col_id) # Replace the value of modelColId
                else:
                    new_dict[key] = value # Keep original if lookup failed
                    logger.warning(f"Robust lookup failed for modelColId '{value}' in table '{table_name}'. Keeping original name.")
            # For other keys, just recurse on the value if it's a dict or list
            elif isinstance(value, (dict, list)):
                # Ensure the recursive call doesn't return None if we are not filtering
                processed_value = replace_col_names_recursive(value, table_name, mysql_client, pg_client, model_id)
                if processed_value is not None: # Should generally not be None here, but safe check
                     new_dict[key] = processed_value
                # If recursive call returned None (shouldn't happen with this function), skip the key
            # Keep other types of values as they are
            else:
                new_dict[key] = value 
        return new_dict
    elif isinstance(item, list):
         # Only recurse into list elements, do not attempt to replace strings directly here
         processed_list = [replace_col_names_recursive(elem, table_name, mysql_client, pg_client, model_id) for elem in item]
         # Filter out potential None results (though not expected from this func)
         return [elem for elem in processed_list if elem is not None]

    # Return item unchanged if not dict or list
    return item

# New helper function to filter conditions AND replace col names
def filter_and_replace_conditions_recursive(item: Union[Dict, List, Any], table_name: str, mysql_client: Any, pg_client: Any, model_id: str) -> Union[Dict, List, Any, None]:
    """
    Recursively traverses filter structures (dicts/lists).
    Replaces 'modelColId' with col_id using robust lookup.
    Removes entire conditions/groups if 'modelColId' lookup fails or a group becomes empty.

    Args:
        item: The dictionary or list structure to process.
        table_name: The primary table name for lookup.
        mysql_client: MySQL client instance.
        pg_client: Pgvector client instance.
        model_id: Model ID for vector search fallback.

    Returns:
        The modified item, or None if the item should be removed entirely.
    """
    if not table_name:
        # If no table name, cannot perform lookup, return original item? Or None?
        # Returning item seems safer to avoid unintended data loss if table name is missing.
        logger.warning("Table name missing in filter_and_replace_conditions_recursive, cannot process.")
        return item
    if not mysql_client and not pg_client:
        logger.warning("Both MySQL and Pgvector clients missing in filter_and_replace_conditions_recursive, cannot process.")
        return item # Return original if clients missing

    if isinstance(item, list):
        processed_list = [filter_and_replace_conditions_recursive(elem, table_name, mysql_client, pg_client, model_id) for elem in item]
        # Filter out elements that became None (removed conditions/groups)
        filtered_list = [elem for elem in processed_list if elem is not None]
        # Return the filtered list, or None if the list becomes empty
        return filtered_list if filtered_list else None

    elif isinstance(item, dict):
        # Case 1: It's a condition dictionary (heuristic: contains 'modelColId')
        if 'modelColId' in item and isinstance(item.get('modelColId'), str):
            col_identifier = item['modelColId']
            col_details = get_col_id_robust(mysql_client, pg_client, table_name, col_identifier, model_id)

            if col_details and 'col_id' in col_details:
                col_id = col_details['col_id']
                # Create a copy to modify
                new_item = item.copy()
                new_item['modelColId'] = str(col_id)
                # Optionally recurse on other values if they could contain nested structures
                # For now, assume condition dict values are simple or handled by list/group recursion
                return new_item
            else:
                logger.warning(f"Robust lookup failed for modelColId '{col_identifier}' in table '{table_name}'. Removing condition: {item}")
                return None # Remove this condition

        # Case 2: It's a group dictionary (contains 'combine' and 'groups')
        elif 'combine' in item and 'groups' in item and isinstance(item['groups'], list):
            processed_groups = filter_and_replace_conditions_recursive(item['groups'], table_name, mysql_client, pg_client, model_id)

            if processed_groups: # If list is not None (meaning not empty after filtering)
                 new_item = item.copy()
                 new_item['groups'] = processed_groups
                 return new_item
            else:
                 logger.warning(f"Group became empty after filtering, removing group: {item}")
                 return None # Remove the group if its list of conditions/subgroups is empty

        # Case 3: Generic dictionary - recurse on values
        else:
            new_dict = {}
            changed = False
            for key, value in item.items():
                if isinstance(value, (dict, list)):
                    processed_value = filter_and_replace_conditions_recursive(value, table_name, mysql_client, pg_client, model_id)
                    if processed_value is not None:
                        new_dict[key] = processed_value
                        if value is not processed_value: # Check if value actually changed
                             changed = True
                    else:
                         # Value was removed by recursion
                         changed = True
                else:
                    # Keep non-dict/list values as is
                    new_dict[key] = value
            # Return the new dict only if something changed or it wasn't empty originally
            # Avoid returning empty dicts for things that were originally non-condition/group dicts
            # However, if changed is True, it implies something was removed, so return potentially empty dict.
            # If not changed and original item wasn't empty, return original item? No, return new_dict.
            return new_dict if new_dict or changed else None # Return None if it ends up empty and wasn't modified

    # Return item unchanged if not dict or list
    return item

def process_sql_query(sql, pgvector_client, mysql_client, model_id:str=""):
    """
    处理SQL查询，解析并修正SQL中的条件值。
    先尝试使用AST方法修正SQL，如果失败则自动降级到正则表达式方法。
    
    参数:
        sql (str): 原始SQL查询
        pgvector_client: 用于语义搜索的客户端
        
    返回:
        tuple: (new_formula, technical_caliber, index_formula, corrected_sql)
    """
    # 初始化默认值
    select_formula = ""
    group_formula = []
    where_formula = {}
    table_names = []
    case_when = []
    corrected_sql = sql
    logger.info(f"原始SQL: {sql}")
    # 使用正则表达式将 FROM 后面的多个空白字符替换为单个空格
    # sql = re.sub(r'(FROM|from|From)(\s+)', r'\1 ', sql, flags=re.IGNORECASE)
    # logger.info(f"正则替换空白后SQL: {sql}")
    # # 继续执行之前的表名替换逻辑
    # # sql = sql.replace("FROM ","FROM table_").replace("from ","FROM table_").replace("From ","FROM table_"+model_id)
    # logger.info(f"添加 table_ 前缀后SQL: {sql}")
    # 解析 SQL 组件，添加保护机制
    try:
        select_formula = select_parse(sql)
    except Exception as e:
        logger.warning(f"解析 SELECT 子句时出错: {str(e)}")
    
    try:
        group_formula = group_parse(sql)
    except Exception as e:
        logger.warning(f"解析 GROUP BY 子句时出错: {str(e)}")
    
    try:
        where_formula = where_parse(sql)
    except Exception as e:
        logger.warning(f"解析 WHERE 子句时出错: {str(e)}")
    
    try:
        table_names = get_table_names(sql)
    except Exception as e:
        logger.warning(f"解析表名时出错: {str(e)}")
    
    try:
        case_when = parse_case_when(sql)
    except Exception as e:
        logger.warning(f"解析 CASE WHEN 子句时出错: {str(e)}")
    
    # 初始转换
    try:
        new_formula, dd, zz = transform_sql_to_if_format(select_formula, where_formula)
    except Exception as e:
        logger.warning(f"转换 SQL 为 IF 格式时出错: {str(e)}")
        new_formula, dd, zz = "", "", []

    # 根据 case_when 是否为空进行条件处理
    if case_when == []:
        # 如果 case_when 为空，只打印初始结果
        pass
    else:
        # 如果 case_when 不为空，执行额外的 SQL 构建和打印
        try:
            zz_sql = 'select ' + ','.join(zz) + ''' from test where ''' + dd

            # 重新解析新的 SQL
            select_formula = select_parse(zz_sql)
            where_formula = where_parse(zz_sql)
        except Exception as e:
            logger.warning(f"处理 CASE WHEN 子句时出错: {str(e)}")

    
    # 无论 if-else 如何，都执行过滤条件处理和最终转换
    logger.debug(f"原始WHERE条件: {where_formula}")
    # 保存原始where_formula用于后续修正SQL - 使用深拷贝
    original_where_formula = copy.deepcopy(where_formula)
    
    # 处理过滤条件
    if where_formula and table_names:  # 只有在有 where_formula 和 table_names 时才处理
        try:
            where_formula = process_filter_conditions(
                conditions=where_formula, 
                pgvector_client=pgvector_client, 
                mysql_client=mysql_client, 
                table_name=table_names[0], 
                distance=0.5,
                model_id=model_id)
            logger.debug(f"修正后WHERE条件: {where_formula}")
        except Exception as e:
            logger.warning(f"处理过滤条件时出错: {str(e)}")
    
    # 尝试使用AST方法修正SQL
    logger.info("尝试使用AST方法修正SQL...")
    try:
        corrected_sql = update_sql_with_ast(sql, original_where_formula, where_formula)
        # 验证AST方法是否成功修改SQL
        if corrected_sql == sql:
            logger.warning("AST方法未能修改SQL，尝试使用正则表达式方法...")
            corrected_sql = update_sql_with_regex(sql, original_where_formula, where_formula)
    except Exception as e:
        logger.warning(f"AST方法出错：{str(e)}，切换到正则表达式方法...")
        try:
            corrected_sql = update_sql_with_regex(sql, original_where_formula, where_formula)
        except Exception as regex_e:
            logger.warning(f"正则表达式方法也出错：{str(regex_e)}，将使用条件修正前的 SQL 或原始 SQL")
            # Decide fallback: Use sql (before AST/Regex) or original_sql? 
            # Using sql which might have corrected values seems better.
            corrected_sql = sql 
    
    logger.info(f"修正后SQL (after AST/Regex): {corrected_sql}")

    # Convert to Flat SQL
    try:
        new_formula, dd, zz = transform_sql_to_if_format(select_formula, where_formula)
    except Exception as e:
        logger.warning(f"最终转换 SQL 为 IF 格式时出错: {str(e)}")
        new_formula, dd, zz = "", "", []
    
    # 生成技术口径
    try:
        technical_caliber = format_sql_components_to_text(select_formula, group_formula, where_formula, table_names)
    except Exception as e:
        logger.warning(f"生成技术口径时出错: {str(e)}")
        technical_caliber = "[数据源]无[计算逻辑]无[过滤条件]无[维度]无"
    
    # 生成API输出格式需要的dict, Pass model_id
    try:
        index_formula = format_sql_to_api(
            sql=corrected_sql, 
            formula_expr=select_formula,
            filter_dict=where_formula,
            mysql_client=mysql_client, # Pass mysql client
            pg_client=pgvector_client, # Pass pg_client
            lookup_table_name=table_names[0] if table_names else None, # Pass original table name
            model_id=model_id # Pass model_id here
        )
    except Exception as e:
        logger.warning(f"生成 API 输出格式时出错: {str(e)}")
        index_formula = {
            "formulaType": "atom",
            "formulaExpr": "",
            "filter": {},
            "filterIndex": {}
        }
    
    return new_formula, technical_caliber, index_formula, corrected_sql

def format_sql_components_to_text(select_formula, group_formula, where_formula, table_names):
    """
    将SQL组件转换为格式化文本
    
    Args:
        select_formula: 选择表达式（计算逻辑）
        group_formula: 分组表达式（维度）
        where_formula: 条件表达式（过滤条件）
        table_names: 表名列表（数据源）
        
    Returns:
        str: 格式化的文本，包含数据源、计算逻辑、过滤条件和维度
    """
    # 转换组件
    data_source = "、".join(table_names) if table_names else "无"
    calculation_logic = select_formula if select_formula else "无"
    filter_condition = parse_where_conditions(where_formula) if where_formula else "无"
    dimensions = "、".join(group_formula) if group_formula else "无"
    
    # 返回格式化文本
    return f"[数据源]{data_source}[计算逻辑]{calculation_logic}[过滤条件]{filter_condition}[维度]{dimensions}"

def format_sql_to_api(sql,
                    formula_expr = None,
                    filter_dict=None,
                    mysql_client: Any = None,
                    pg_client: Any = None,
                    lookup_table_name: str = None,
                    model_id: str = None):
    """
    处理 SQL 查询，返回符合 API 需求的字典格式, 并将列名替换为 col_id。
    Uses robust lookup (MySQL direct then vector fallback).
    For filter_dict, conditions are REMOVED if col_id lookup fails.

    Args:
        sql (str): SQL 查询字符串 (corrected)
        formula_expr (str, optional): 已解析的选择表达式
        filter_dict (dict or list, optional): 已解析和修正的过滤条件
        mysql_client (Any, optional): MySQL 客户端实例
        pg_client (Any, optional): Pgvector 客户端实例
        lookup_table_name (str, optional): 用于查找 col_id 的原始表名
        model_id (str, optional): Model ID for vector search fallback.

    Returns:
        dict: 包含 formulaExpr, filter, filterIndex, indexDims 等字段的字典, 列名已替换为 ID。
    """
    # 初始化默认值
    formula_expr = formula_expr or ""
    filter_dict = filter_dict or {}
    filter_index = {}
    index_dims = []
    
    primary_table_name_for_lookup = lookup_table_name
    # Log warnings if clients or table name are missing
    if not primary_table_name_for_lookup:
        logger.warning("Lookup table name not provided, col_id replacement might fail.")
    if not mysql_client:
        logger.warning("MySQL client not provided, direct col_id lookup will fail.")
    if not pg_client:
        logger.warning("Pgvector client not provided, vector fallback for col_id lookup will fail.")

    # Parse components from corrected SQL if needed (e.g., for index/dims)
    try:
        filter_index = filter_index_parse(sql)
    except Exception as e:
        logger.warning(f"警告：解析 HAVING 子句时出错: {str(e)}")
        filter_index = {}
    try:
        index_dims = index_dims_parse(sql)
    except Exception as e:
        logger.warning(f"警告：解析 GROUP BY 子句时出错: {str(e)}")
        index_dims = []

    # -- 开始替换 col_name 为 col_id --
    if primary_table_name_for_lookup:
        # 1. 替换 formulaExpr
        try:
            formula_expr_id = replace_cols_in_expr(formula_expr, primary_table_name_for_lookup, mysql_client, pg_client, model_id)
        except Exception as e:
            logger.warning(f"替换 formulaExpr 中的列名时出错: {str(e)}")
            formula_expr_id = formula_expr

        # 2. **FILTER and Replace** filter_dict
        try:
            # Use the new function here! Make deepcopy just in case.
            filter_dict_id = filter_and_replace_conditions_recursive(copy.deepcopy(filter_dict), primary_table_name_for_lookup, mysql_client, pg_client, model_id)
            # The function returns None if the whole structure should be removed (e.g., root was a group that became empty)
            if filter_dict_id is None:
                filter_dict_id = {} # Default to empty dict if root is removed
        except Exception as e:
            logger.error(f"过滤和替换 filter_dict 中的列名时出错: {str(e)}") # Log as error
            filter_dict_id = filter_dict # Fallback to original on error

        # 3. Replace filter_index (using original recursive function)
        try:
            filter_index_id = replace_col_names_recursive(copy.deepcopy(filter_index), primary_table_name_for_lookup, mysql_client, pg_client, model_id)
        except Exception as e:
            logger.warning(f"替换 filter_index 中的列名时出错: {str(e)}")
            filter_index_id = filter_index

        # 4. Replace index_dims (no change here)
        index_dims_id = []
        try:
            for dim_identifier in index_dims: # Assuming index_dims is List[str]
                if isinstance(dim_identifier, str):
                    # Use robust lookup for each dimension string
                    col_details = get_col_id_robust(mysql_client, pg_client, primary_table_name_for_lookup, dim_identifier, model_id)
                    
                    if col_details and 'col_id' in col_details:
                        col_id = col_details['col_id']
                        index_dims_id.append(str(col_id)) 
                    else:
                        index_dims_id.append(dim_identifier) # Keep original if lookup failed
                        logger.warning(f"Robust lookup failed for index_dims string '{dim_identifier}' in table '{primary_table_name_for_lookup}'. Keeping original string.")
                elif isinstance(dim_identifier, dict) and 'modelColId' in dim_identifier:
                     # Handle dict case within index_dims as well (similar to recursive logic)
                     col_name_in_dict = dim_identifier['modelColId']
                     col_details = get_col_id_robust(mysql_client, pg_client, primary_table_name_for_lookup, col_name_in_dict, model_id)
                     new_dim_dict = dim_identifier.copy()
                     if col_details and 'col_id' in col_details:
                         new_dim_dict['modelColId'] = str(col_details['col_id'])
                         index_dims_id.append(new_dim_dict)
                     else:
                         logger.warning(f"Robust lookup failed for index_dims dict '{col_name_in_dict}' in table '{primary_table_name_for_lookup}'. Keeping original dict.")
                         index_dims_id.append(new_dim_dict) # Keep original dict
                else:
                    # Keep other types as is
                    index_dims_id.append(dim_identifier)
                    
        except Exception as e:
            logger.warning(f"替换 index_dims 中的列名时出错: {str(e)}")
            index_dims_id = index_dims # Fallback to original on error

    else:
        # If lookup table name is missing, skip replacement
        logger.warning("Skipping col_id replacement because lookup table name is missing.")
        formula_expr_id = formula_expr
        filter_dict_id = filter_dict # Keep original filter dict
        filter_index_id = filter_index
        index_dims_id = index_dims

    # 处理 filter_dict_id 格式 (与原逻辑相同, 但作用在替换/过滤后的变量上)
    if not filter_dict_id: # Handles None or empty dict/list from filtering
        filter_dict_id = {}
    elif isinstance(filter_dict_id, list):
        # This case might be less likely now if lists are filtered correctly, but keep for robustness
        if len(filter_dict_id) > 1:
             # Check if it's a list of conditions that needs wrapping
             is_condition_list = all(isinstance(item, dict) and ('field' in item or 'modelColId' in item) for item in filter_dict_id) # Adjusted check
             if is_condition_list:
                 filter_dict_id = {'combine': 'and', 'groups': filter_dict_id}
             # Avoid double-wrapping if it's already a single group dict that wasn't filtered out
             elif not (len(filter_dict_id) == 1 and isinstance(filter_dict_id[0],dict) and 'combine' in filter_dict_id[0]):
                 filter_dict_id = {'combine': 'and', 'groups': filter_dict_id}
        elif len(filter_dict_id) == 1:
             # If only one item left, it could be a condition or a group
             filter_dict_id = filter_dict_id[0]
        else: # Empty list
             filter_dict_id = {}
    # Ensure the final structure is a dictionary
    if not isinstance(filter_dict_id, dict):
         # If filtering resulted in something unexpected (e.g., a single non-dict item?)
         logger.warning(f"Final filter structure is not a dictionary after processing: {filter_dict_id}. Setting to empty dict.")
         filter_dict_id = {}

    # 构建 API 所需的字典格式 (使用替换后的变量)
    processed_formula_expr = re.split(r'\s+as\s+', formula_expr_id, maxsplit=1, flags=re.IGNORECASE)[0].strip()

    result = {
        "formulaType": "atom",
        "formulaExpr": processed_formula_expr, # Use processed expression
        "filter": filter_dict_id, # Use filtered and replaced dict
        "filterIndex": filter_index_id
    }

    # 如果有 indexDims，添加到结果中
    if index_dims_id: # Check the id version
        result["indexDims"] = index_dims_id

    return result

