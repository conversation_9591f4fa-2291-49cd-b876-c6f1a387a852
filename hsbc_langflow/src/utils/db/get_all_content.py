from modules.pg_database.llm.llm_chat import get_embeddings
from modules.pg_database.pgvector.rag import hybrid_search
from utils.db.get_partitionkey import get_or_create_partition_key
from utils.common.logger_util import logger

def get_all_content(pg_client, mysql_client, mysql_name, partition: "str",
                    serach_content, type: list[str], expr: "str" = None,topk=3):
    pg_client.set_table('hsbc_embedding_data')
    partition_key = get_or_create_partition_key(partition, mysql_client)
    serach_embedding = get_embeddings(serach_content)["data"][0]['embedding']
    indicator_vec_dict = {
        "embedding": serach_embedding
    }
    rank_type = {"type": "hybrid-weighted", "rank_rule": [{
        "embedding": 1.0
    }]}
    out_fields = ["content_id"]
    all_out = {}
    if type != []:
        for li_type in type:
            if expr:
                li_expr = f"{expr} and embedding_type = '{li_type}'"
            else:
                li_expr = f"embedding_type = '{li_type}'"
            results = hybrid_search(
                pgvector_client=pg_client,
                table_name="hsbc_embedding_data",
                vec_dict=indicator_vec_dict,
                rank_dict=rank_type,
                out_filed=out_fields,
                topk=topk,
                expr=li_expr,
                partition_name=partition_key,
                metric_type="cosine"
            )
            logger.info(f"hybrid_search results: {results}")
            results = [result['content_id'] for result in results if result['distance'] < 0.3]
            if not results:
                return {}
            if mysql_name == 'where_info':
                mysql_get_content = {
                    "op_type": "SELECT",
                    "table_name": mysql_name,
                    "data_dict": {
                        "logic": "AND",
                        "conditions": [
                            {"type": "in", "col_name": "where_id", "col_val": results}
                        ]
                    }
                }
            else:
                mysql_get_content = {
                    "op_type": "SELECT",
                    "table_name": mysql_name,
                    "data_dict": {
                        "logic": "AND",
                        "conditions": [
                            {"type": "in", "col_name": "col_code", "col_val": results}
                        ]
                    }
                }
            all_out[li_type] = mysql_client.batch_operation(mysql_get_content)[0]
    else:
        results = hybrid_search(
            pgvector_client=pg_client,
            table_name="hsbc_embedding_data",
            vec_dict=indicator_vec_dict,
            rank_dict=rank_type,
            out_filed=out_fields,
            topk=topk,
            expr="",
            partition_name=partition_key,
            metric_type="cosine"
        )
        logger.info(f"hybrid_search results: {results}")
        results = [result['content_id'] for result in results if result['distance'] < 0.3]
        if not results:
            return {}
        if mysql_name == 'where_info':
            mysql_get_content = {
                "op_type": "SELECT",
                "table_name": mysql_name,
                "data_dict": {
                    "logic": "AND",
                    "conditions": [
                        {"type": "in", "col_name": "where_id", "col_val": results}
                    ]
                }
            }
            all_out["no_type"] = mysql_client.batch_operation(mysql_get_content)[0]
    return all_out


if __name__ == '__main__':
    from utils.db.common.client_util import pgvector_client, mysql_client

    # print('fuwuhao;')
    zz = get_all_content(pg_client=pgvector_client, mysql_client=mysql_client, mysql_name='where_info',
                         partition='meta#ADM_LON_VAROIUS#BUSSINESS_TYPE', serach_content="0202", type=[])
    print(zz)
