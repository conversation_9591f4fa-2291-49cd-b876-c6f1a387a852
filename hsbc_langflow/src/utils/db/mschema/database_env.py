'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Thursday, 29th May 2025 9:06:15 am
'''

'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Thursday, 29th May 2025 9:03:53 am
'''

"""
数据库环境类 - 兼容新的客户端系统
"""

from typing import Optional
from sqlalchemy.engine import Engine
from utils.db.mschema.db_source import HITLSQLDatabase
from utils.db.common.client_util import mysql_client, pgvector_client
from loguru import logger


class DataBaseEnv:
    def __init__(self, database: Optional[HITLSQLDatabase] = None, client_type: str = 'mysql', db_name: str = ''):
        """
        初始化数据库环境
        
        Args:
            database: 可选的HITLSQLDatabase实例，如果不提供则从客户端系统创建
            client_type: 客户端类型 ('mysql' 或 'postgresql')
            db_name: 数据库名称
        """
        if database is not None:
            # 使用传入的database
            self.database = database
        else:
            # 从客户端系统创建database
            self.database = self._create_database_from_client(client_type, db_name)
        
        self.dialect = self.database.dialect
        self.mschema = self.database.mschema
        self.db_name = self.database.db_name
        self.mschema_str = self.mschema.to_mschema()
    
    def _create_database_from_client(self, client_type: str, db_name: str) -> HITLSQLDatabase:
        """从客户端系统创建HITLSQLDatabase"""
        try:
            if client_type.lower() == 'mysql':
                client = mysql_client
                if client is None:
                    raise RuntimeError("MySQL client not available")
                
                # 从MySQL客户端获取SQLAlchemy Engine
                engine = self._get_engine_from_mysql_client(client)
                
            elif client_type.lower() in ['postgresql', 'postgres', 'pgvector']:
                client = pgvector_client
                if client is None:
                    raise RuntimeError("PGVector client not available")
                
                # 从PGVector客户端获取SQLAlchemy Engine
                engine = self._get_engine_from_pgvector_client(client)
                
            else:
                raise ValueError(f"Unsupported client_type: {client_type}")
            
            # 创建HITLSQLDatabase实例
            return HITLSQLDatabase(
                engine=engine,
                db_name=db_name or client_type
            )
            
        except Exception as e:
            logger.error(f"Failed to create database from client {client_type}: {e}")
            raise
    
    def _get_engine_from_mysql_client(self, client) -> Engine:
        """从MySQL客户端获取SQLAlchemy Engine"""
        try:
            # 检查是否是我们的接口包装
            if hasattr(client, 'client') and client.client is not None:
                actual_client = client.client
            else:
                actual_client = client
            
            # 检查是否是MySQLSQLAlchemyClient
            if hasattr(actual_client, 'engine') and actual_client.engine is not None:
                logger.info("Found SQLAlchemy engine in MySQL client")
                return actual_client.engine
            
            # 如果是旧的MySQL客户端，可能需要其他方式获取engine
            elif hasattr(actual_client, '_engine'):
                logger.info("Found _engine in MySQL client")
                return actual_client._engine
            
            else:
                raise RuntimeError("Cannot find SQLAlchemy engine in MySQL client")
                
        except Exception as e:
            logger.error(f"Error extracting engine from MySQL client: {e}")
            raise
    
    def _get_engine_from_pgvector_client(self, client) -> Engine:
        """从PGVector客户端获取SQLAlchemy Engine"""
        try:
            # 检查是否是我们的接口包装
            if hasattr(client, 'client') and client.client is not None:
                actual_client = client.client
            else:
                actual_client = client
            
            # PGVector客户端使用的是psycopg2连接池，需要创建SQLAlchemy Engine
            if hasattr(actual_client, 'host') and hasattr(actual_client, 'port'):
                from sqlalchemy import create_engine
                from urllib.parse import quote_plus
                
                # 构建连接URL
                encoded_user = quote_plus(actual_client.user)
                encoded_password = quote_plus(actual_client.password)
                connection_url = f"postgresql+psycopg2://{encoded_user}:{encoded_password}@{actual_client.host}:{actual_client.port}/{actual_client.db_name}"
                
                # 创建SQLAlchemy Engine
                engine = create_engine(
                    connection_url,
                    pool_size=5,
                    max_overflow=10,
                    pool_pre_ping=True,
                    echo=False
                )
                
                logger.info("Created SQLAlchemy engine for PGVector client")
                return engine
            
            else:
                raise RuntimeError("Cannot extract connection info from PGVector client")
                
        except Exception as e:
            logger.error(f"Error creating engine for PGVector client: {e}")
            raise
    
    @classmethod
    def from_mysql_client(cls, db_name: str = 'mysql'):
        """从MySQL客户端创建数据库环境"""
        return cls(client_type='mysql', db_name=db_name)
    
    @classmethod
    def from_pgvector_client(cls, db_name: str = 'postgres'):
        """从PGVector客户端创建数据库环境"""
        return cls(client_type='pgvector', db_name=db_name)
    
    def get_engine(self) -> Engine:
        """获取SQLAlchemy Engine"""
        return self.database._engine
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            from sqlalchemy import text
            with self.database._engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False