'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 9:33:18 am
'''

"""
数据库客户端全局访问点 (The "Waiter")

本模块的职责:
1.  **提供统一的、全局的数据库客户端访问入口**。应用中任何需要与数据库交互的部分，
    都应该从本模块导入 `rdb_client` 和 `vdb_client`。
2.  **实现真正的懒加载 (Lazy Loading)**。通过 `LazyClient` 代理类，将客户端的
    实际初始化操作推迟到第一次访问其属性时。

与 `modules.db.clients` 的关系:
-   本模块是应用层代码的 **"服务员"**。它为代码提供了一个安全的、随时可用的客户端对象。
-   `modules.db.clients` 模块是 **"后厨"**，负责实际创建和管理客户端实例。
-   当服务员（本模块的 `LazyClient`）第一次被请求提供服务时（例如 `rdb_client.list_tables()`)，
    它才会去后厨（`modules.db.clients.get_rdb_client()`）下单，获取真正的客户端。

解决的问题:
-   **解决了"导入时配置未就绪"的问题**。由于真正的初始化被推迟，我们可以在应用启动的
    任何阶段安全地 `import rdb_client`，而不用担心因配置未加载而引发崩溃。
"""

from utils.common.logger_util import logger
from modules.db.clients import get_rdb_client, get_vdb_client



class LazyClient:
    """
    一个懒加载代理对象。
    它将实际客户端的实例化推迟到第一次访问其任何属性时。
    它通过重写 __getattr__ 来透明地将所有操作委托给实际的客户端实例。
    """
    def __init__(self, creator_func):
        # 使用特殊命名以避免与 __getattr__ 发生无限递归
        self._creator = creator_func
        self._instance = None
        self.logger = logger

    def _get_instance(self):
        """获取或创建实际的客户端实例。"""
        if self._instance is None:
            self.logger.info(f"Lazily creating instance using '{self._creator.__name__}'")
            try:
                self._instance = self._creator()
                self.logger.info("Instance created successfully.")
            except Exception as e:
                self.logger.error(f"Error during lazy creation: {e}", exc_info=True)
                raise
        return self._instance

    def __getattr__(self, name):
        """
        当访问代理对象的属性时，此方法会被调用。
        它会首先确保真实客户端已创建，然后将属性访问转发给真实客户端。
        """
        self.logger.debug(f"Delegating attribute '{name}' to lazy-loaded client.")
        instance = self._get_instance()
        return getattr(instance, name)

    @property
    def __class__(self):
        """代理对象的 class 属性，使其表现得像真实对象，方便类型检查。"""
        return self._get_instance().__class__

    def __repr__(self):
        """提供一个有用的表示，而不触发实例化。"""
        if self._instance is None:
            return f"<LazyClient for {self._creator.__name__} (not initialized)>"
        return repr(self._get_instance())


# ==================== 全局客户端引用 ====================
# 使用懒加载代理定义全局客户端。
# 这可以防止在导入模块时就实例化客户端，从而避免在配置初始化之前发生错误。

rdb_client = LazyClient(get_rdb_client)
vdb_client = LazyClient(get_vdb_client)

__all__ = [
    'rdb_client', 
    'vdb_client',
]
