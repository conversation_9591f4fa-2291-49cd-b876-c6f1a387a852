'''
File Created: Tuesday, 3rd June 2025 9:00:00 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 9:53:45 am
'''

"""
RAG测试示例
使用最新的client_util中的mysql_client和pgvector_client
基于embedding_type字段进行向量搜索测试
"""

import sys
import os
import json
from typing import List, Dict, Any, Optional
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append('/data/ideal/code/gsh_code/text2SQL/hsbc_langflow/src')

from utils.db.common.client_util import mysql_client, pgvector_client
from base.model_serve.model_runtime.model_providers.model_interface import UnifiedTextEmbeddingInterface


class RAGSampleTester:
    """RAG测试类，基于embedding_type进行向量搜索"""
    
    def __init__(self):
        """初始化测试器"""
        self.mysql_client = mysql_client
        self.pgvector_client = pgvector_client
        self.embedding_interface = UnifiedTextEmbeddingInterface()
        
        # embedding配置
        self.embedding_credentials = {
            "provider": "moka-m3e-base",
        }
        self.embedding_model = "moka-m3e-base"
        
        # 数据库表名（根据您的数据结构）
        self.table_name = "hsbc_embedding_data"  # 或者您实际的表名
        
        logger.info("RAG测试器初始化完成")
    
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        获取文本的embedding向量
        
        Args:
            texts: 文本列表
            
        Returns:
            embedding向量列表
        """
        try:
            result = self.embedding_interface.invoke(
                model=self.embedding_model,
                credentials=self.embedding_credentials,
                texts=texts
            )
            return result.embeddings
        except Exception as e:
            logger.error(f"获取embedding失败: {e}")
            return []
    
    async def get_embeddings_async(self, texts: List[str]) -> List[List[float]]:
        """
        异步获取文本的embedding向量
        
        Args:
            texts: 文本列表
            
        Returns:
            embedding向量列表
        """
        try:
            result = await self.embedding_interface.ainvoke(
                model=self.embedding_model,
                credentials=self.embedding_credentials,
                texts=texts
            )
            return result.embeddings
        except Exception as e:
            logger.error(f"异步获取embedding失败: {e}")
            return []
    
    def search_by_embedding_type(self, query_text: str, embedding_type_filter: str = None, 
                                top_k: int = 5) -> List[Dict[str, Any]]:
        """
        基于embedding_type进行向量搜索
        
        Args:
            query_text: 查询文本
            embedding_type_filter: embedding_type过滤条件（如'col_name_cn', 'col_desc'等）
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        logger.info(f"开始向量搜索，查询文本: {query_text}, embedding_type过滤: {embedding_type_filter}")
        
        try:
            # 获取查询文本的embedding
            embeddings = self.get_embeddings([query_text])
            if not embeddings:
                logger.error("无法获取查询文本的embedding")
                return []
            
            query_embedding = embeddings[0]
            
            # 构建过滤条件
            filter_condition = ""
            if embedding_type_filter:
                filter_condition = f"embedding_type = '{embedding_type_filter}'"
            
            # 搜索参数
            search_params = {
                "vector_field": "embedding",
                "output_fields": ["id", "content_id", "embedding_type", "partition_key"]
            }
            
            # 执行向量搜索
            results = self.pgvector_client.search(
                 collection_name=self.table_name,
                vector=query_embedding,
                top_k=top_k,
                metric_type="cosine",
                filter=filter_condition,
                partition_names=["p_1________________xMpCOKC5I4"],  # 根据您的数据，使用p_1分区
                search_params=search_params
            )
            
            logger.info(f"向量搜索完成，返回 {len(results)} 条结果")
            return results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return []
    
    def search_column_names(self, query_text: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        搜索列名相关的内容（embedding_type = 'col_name_cn'）
        
        Args:
            query_text: 查询文本
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        logger.info(f"搜索列名相关内容: {query_text}")
        return self.search_by_embedding_type(query_text, "col_name_cn", top_k)
    
    def search_column_descriptions(self, query_text: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        搜索列描述相关的内容（embedding_type = 'col_desc'）
        
        Args:
            query_text: 查询文本
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        logger.info(f"搜索列描述相关内容: {query_text}")
        return self.search_by_embedding_type(query_text, "col_desc", top_k)
    
    def search_all_types(self, query_text: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        搜索所有类型的内容（不过滤embedding_type）
        
        Args:
            query_text: 查询文本
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        logger.info(f"搜索所有类型内容: {query_text}")
        return self.search_by_embedding_type(query_text, None, top_k)
    
    def get_embedding_type_statistics(self) -> Dict[str, int]:
        """
        获取embedding_type的统计信息
        
        Returns:
            各embedding_type的数量统计
        """
        logger.info("获取embedding_type统计信息")
        
        try:
            query = f"""
            SELECT embedding_type, COUNT(*) as count 
            FROM {self.table_name} 
            GROUP BY embedding_type 
            ORDER BY count DESC
            """
            
            results = self.pgvector_client.execute_query(query)
            
            statistics = {}
            for row in results:
                statistics[row['embedding_type']] = row['count']
            
            logger.info(f"embedding_type统计: {statistics}")
            return statistics
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def search_by_content_pattern(self, content_pattern: str, embedding_type_filter: str = None, 
                                 top_k: int = 5) -> List[Dict[str, Any]]:
        """
        基于content_id模式进行搜索
        
        Args:
            content_pattern: content_id的模式（如'ADM_PUB_BRANCH_LEVEL_INFO'）
            embedding_type_filter: embedding_type过滤条件
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        logger.info(f"基于content模式搜索: {content_pattern}")
        
        try:
            # 构建过滤条件
            filter_conditions = [f"content_id LIKE '%{content_pattern}%'"]
            if embedding_type_filter:
                filter_conditions.append(f"embedding_type = '{embedding_type_filter}'")
            
            filter_condition = " AND ".join(filter_conditions)
            
            # 执行查询
            query = f"""
            SELECT id, content_id, embedding_type, partition_key
            FROM {self.table_name}
            WHERE {filter_condition}
            ORDER BY id
            LIMIT {top_k}
            """
            
            results = self.pgvector_client.execute_query(query)
            logger.info(f"模式搜索完成，返回 {len(results)} 条结果")
            return results
            
        except Exception as e:
            logger.error(f"模式搜索失败: {e}")
            return []
    
    def test_hybrid_search(self, query_text: str, content_pattern: str = None, 
                          embedding_type_filter: str = None, top_k: int = 3) -> Dict[str, Any]:
        """
        测试混合搜索（向量搜索 + 文本过滤）
        
        Args:
            query_text: 查询文本
            content_pattern: content_id模式
            embedding_type_filter: embedding_type过滤
            top_k: 返回结果数量
            
        Returns:
            混合搜索结果
        """
        logger.info(f"开始混合搜索测试")
        
        results = {
            "vector_search": [],
            "pattern_search": [],
            "combined_insights": {}
        }
        
        try:
            # 1. 向量搜索
            vector_results = self.search_by_embedding_type(query_text, embedding_type_filter, top_k)
            results["vector_search"] = vector_results
            
            # 2. 模式搜索
            if content_pattern:
                pattern_results = self.search_by_content_pattern(content_pattern, embedding_type_filter, top_k)
                results["pattern_search"] = pattern_results
            
            # 3. 组合分析
            results["combined_insights"] = {
                "vector_results_count": len(vector_results),
                "pattern_results_count": len(results["pattern_search"]),
                "query_text": query_text,
                "content_pattern": content_pattern,
                "embedding_type_filter": embedding_type_filter
            }
            
            logger.info("混合搜索测试完成")
            return results
            
        except Exception as e:
            logger.error(f"混合搜索测试失败: {e}")
            return results
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("=== 开始RAG综合测试（基于embedding_type） ===")
        
        test_results = {}
        
        try:
            # 1. 获取embedding_type统计
            logger.info("1. 获取embedding_type统计...")
            statistics = self.get_embedding_type_statistics()
            test_results["embedding_type_stats"] = statistics
            
            if statistics:
                logger.info("Embedding类型统计:")
                print(json.dumps(statistics, indent=2, ensure_ascii=False))
            
            # 2. 测试列名搜索
            logger.info("2. 测试列名搜索...")
            column_name_results = self.search_column_names("分支", 3)
            test_results["column_name_search"] = column_name_results
            
            if column_name_results:
                logger.info("列名搜索结果:")
                print(json.dumps(column_name_results, indent=2, ensure_ascii=False))
            
            # 3. 测试列描述搜索
            logger.info("3. 测试列描述搜索...")
            column_desc_results = self.search_column_descriptions("级别", 3)
            test_results["column_desc_search"] = column_desc_results
            
            if column_desc_results:
                logger.info("列描述搜索结果:")
                print(json.dumps(column_desc_results, indent=2, ensure_ascii=False))
            
            # 4. 测试全类型搜索
            logger.info("4. 测试全类型搜索...")
            all_type_results = self.search_all_types("分支级别", 5)
            test_results["all_type_search"] = all_type_results
            
            if all_type_results:
                logger.info("全类型搜索结果:")
                print(json.dumps(all_type_results, indent=2, ensure_ascii=False))
            
            # 5. 测试模式搜索
            logger.info("5. 测试模式搜索...")
            pattern_results = self.search_by_content_pattern("ADM_PUB_BRANCH_LEVEL_INFO", "col_name_cn", 3)
            test_results["pattern_search"] = pattern_results
            
            if pattern_results:
                logger.info("模式搜索结果:")
                print(json.dumps(pattern_results, indent=2, ensure_ascii=False))
            
            # 6. 测试混合搜索
            logger.info("6. 测试混合搜索...")
            hybrid_results = self.test_hybrid_search(
                query_text="分支信息",
                content_pattern="ADM_PUB_BRANCH",
                embedding_type_filter="col_name_cn",
                top_k=3
            )
            test_results["hybrid_search"] = hybrid_results
            
            if hybrid_results:
                logger.info("混合搜索结果:")
                print(json.dumps(hybrid_results, indent=2, ensure_ascii=False))
            
            # 7. 测试embedding性能
            logger.info("7. 测试embedding性能...")
            test_texts = ["分支级别", "公共信息", "元数据", "列名", "描述"]
            performance_results = self.test_embedding_performance(test_texts)
            test_results["embedding_performance"] = performance_results
            
            if performance_results:
                logger.info("Embedding性能测试结果:")
                print(json.dumps(performance_results, indent=2, ensure_ascii=False))
            
            logger.success("=== RAG综合测试完成 ===")
            return test_results
            
        except Exception as e:
            logger.error(f"综合测试过程中发生错误: {e}")
            return test_results
    
    def test_embedding_performance(self, texts: List[str]) -> Dict[str, Any]:
        """
        测试embedding性能
        
        Args:
            texts: 测试文本列表
            
        Returns:
            性能测试结果
        """
        import time
        
        logger.info(f"开始测试embedding性能，文本数量: {len(texts)}")
        
        try:
            # 同步测试
            start_time = time.time()
            sync_embeddings = self.get_embeddings(texts)
            sync_time = time.time() - start_time
            
            # 异步测试
            import asyncio
            start_time = time.time()
            async_embeddings = asyncio.run(self.get_embeddings_async(texts))
            async_time = time.time() - start_time
            
            performance_result = {
                "text_count": len(texts),
                "sync_time": sync_time,
                "async_time": async_time,
                "sync_embeddings_count": len(sync_embeddings),
                "async_embeddings_count": len(async_embeddings),
                "embedding_dimension": len(sync_embeddings[0]) if sync_embeddings else 0,
                "texts_tested": texts
            }
            
            logger.info(f"Embedding性能测试完成: {performance_result}")
            return performance_result
            
        except Exception as e:
            logger.error(f"Embedding性能测试失败: {e}")
            return {}


def main():
    """主函数"""
    # 设置日志级别
    logger.remove()
    logger.add(sys.stderr, level="INFO", format="{time} | {level} | {message}")
    
    # 创建测试器并运行测试
    tester = RAGSampleTester()
    
    try:
        # 运行综合测试
        results = tester.run_comprehensive_test()
        
        # 输出最终结果摘要
        logger.info("=== 测试结果摘要 ===")
        for test_name, result in results.items():
            if result:
                logger.success(f"✅ {test_name}: 成功")
            else:
                logger.error(f"❌ {test_name}: 失败")
        
        logger.success("🎉 RAG测试全部完成！")
        
    except KeyboardInterrupt:
        logger.warning("测试被用户中断")
    except Exception as e:
        logger.error(f"测试异常: {e}")


if __name__ == "__main__":
    main()