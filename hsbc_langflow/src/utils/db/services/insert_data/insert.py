'''
File Created: Tuesday, 3rd June 2025 6:54:47 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 7:53:48 am
'''

"""
真实的文件插入向量数据库用例
基于原始的vector_insert.py重构，使用新的客户端架构和统一的embedding接口
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append('/data/ideal/code/gsh_code/text2SQL/hsbc_langflow/src')

from loguru import logger
from utils.db.services.insert_data.func.insert_file_to_vdb import FileToVectorDB
from base.model_serve.model_runtime.model_providers.model_interface import UnifiedTextEmbeddingInterface


class EmbeddingService:
    """Embedding服务类，封装统一的embedding接口"""
    
    def __init__(self):
        self.embedding_interface = UnifiedTextEmbeddingInterface()
        self.credentials = {
            "provider": "moka-m3e-base",
        }
        self.model = "moka-m3e-base"
    
    def get_embedding(self, text: str) -> list:
        """
        获取单个文本的embedding向量
        
        Args:
            text: 要向量化的文本
            
        Returns:
            list: 768维的embedding向量
        """
        try:
            result = self.embedding_interface.invoke(
                model=self.model,
                credentials=self.credentials,
                texts=[text]
            )
            return result.embeddings[0]
        except Exception as e:
            logger.error(f"获取embedding失败: {e}")
            return None


def insert_sql_demo_data():
    """插入SQL演示数据"""
    logger.info("=== 开始插入SQL演示数据 ===")
    
    # 初始化embedding服务
    embedding_service = UnifiedTextEmbeddingInterface()
    
    # 创建文件插入器
    inserter = FileToVectorDB(
        vdb_type='pgvector',
        instance_name='pgvector',
        embedding_func=embedding_service.invoke
    )
    
    # 文件路径
    excel_file_path = '/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/sql_data_demo.csv'
    
    # 定义表结构
    table_create_fields = [
        {"name": "id", "type": "VARCHAR(10)", "primary_key": True, "comment": "主键"},
        {"name": "user_question", "type": "VARCHAR(2000)", "comment": "用户问题"},
        {"name": "knowledge_evidence", "type": "TEXT", "comment": "知识"},
        {"name": "sql", "type": "VARCHAR(2000)", "comment": "sql答案"},
        {"name": "embedding_user_question", "type": "vector(768)", "comment": "用户问题embedding"}
    ]
    
    # 定义插入映射
    insert_dict = [
        {'source_id': 'user_question', 'type': 'mapping', 'value': '', 'table_id': 'user_question'},
        {'source_id': 'knowledge_evidence', 'type': 'mapping', 'value': '', 'table_id': 'knowledge_evidence'},
        {'source_id': 'sql', 'type': 'mapping', 'value': '', 'table_id': 'sql'},
        {'source_id': 'user_question', 'type': 'embedding', 'value': '', 'table_id': 'embedding_user_question'},
        {'source_id': '', 'type': 'auto', 'value': '', 'table_id': 'id'}
    ]
    
    # 执行插入
    success = inserter.insert_file_to_vdb(
        file_path=excel_file_path,
        table_name="sql_demo",
        insert_dict=insert_dict,
        table_desc="用户问题表",
        create_table_fields=table_create_fields,
        batch_size=50
    )
    
    if success:
        logger.success("SQL演示数据插入成功！")
    else:
        logger.error("SQL演示数据插入失败！")
    
    return success


def insert_catalog_data():
    """插入目录数据"""
    logger.info("=== 开始插入目录数据 ===")
    
    # 初始化embedding服务
    embedding_service = EmbeddingService()
    
    # 创建文件插入器
    inserter = FileToVectorDB(
        vdb_type='pgvector',
        instance_name='pgvector',
        embedding_func=embedding_service.get_embedding
    )
    
    # 文件路径
    excel_file_path = '/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/测试表结构.csv'
    
    # 定义表结构
    catalog_fields = [
        {"name": "id", "type": "VARCHAR(10)", "primary_key": True, "comment": "主键"},
        {"name": "table_name", "type": "VARCHAR(255)", "comment": "表名"},
        {"name": "column_name", "type": "VARCHAR(255)", "comment": "字段名"},
        {"name": "original_column_name", "type": "VARCHAR(255)", "comment": "原始/修正字段名"},
        {"name": "column_description", "type": "VARCHAR(255)", "comment": "字段解释"},
        {"name": "value_description", "type": "TEXT", "comment": "值解释"},
        {"name": "embedding_column_name", "type": "vector(768)", "comment": "字段名embedding"},
        {"name": "embedding_column_description", "type": "vector(768)", "comment": "字段解释embedding"},
        {"name": "embedding_value_description", "type": "vector(768)", "comment": "值解释embedding"},
    ]
    
    # 定义插入映射
    insert_dict = [
        {'source_id': 'table_name', 'type': 'mapping', 'value': '', 'table_id': 'table_name'},
        {'source_id': 'column_name', 'type': 'mapping', 'value': '', 'table_id': 'column_name'},
        {'source_id': 'original_column_name', 'type': 'mapping', 'value': '', 'table_id': 'original_column_name'},
        {'source_id': 'column_description', 'type': 'mapping', 'value': '', 'table_id': 'column_description'},
        {'source_id': 'value_description', 'type': 'mapping', 'value': '', 'table_id': 'value_description'},
        {'source_id': 'column_name', 'type': 'embedding', 'value': '', 'table_id': 'embedding_column_name'},
        {'source_id': 'column_description', 'type': 'embedding', 'value': '', 'table_id': 'embedding_column_description'},
        {'source_id': 'value_description', 'type': 'embedding', 'value': '', 'table_id': 'embedding_value_description'},
        {'source_id': '', 'type': 'auto', 'value': '', 'table_id': 'id'}
    ]
    
    # 执行插入
    success = inserter.insert_file_to_vdb(
        file_path=excel_file_path,
        table_name="catalog",
        insert_dict=insert_dict,
        table_desc="数据结构表",
        create_table_fields=catalog_fields,
        batch_size=50
    )
    
    if success:
        logger.success("目录数据插入成功！")
    else:
        logger.error("目录数据插入失败！")
    
    return success


def insert_condition_data():
    """插入条件数据"""
    logger.info("=== 开始插入条件数据 ===")
    
    # 初始化embedding服务
    embedding_service = EmbeddingService()
    
    # 创建文件插入器
    inserter = FileToVectorDB(
        vdb_type='pgvector',
        instance_name='pgvector',
        embedding_func=embedding_service.get_embedding
    )
    
    # 文件路径
    excel_file_path = '/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/condition_data.csv'
    
    # 定义表结构
    table_create_fields = [
        {"name": "id", "type": "VARCHAR(10)", "primary_key": True, "comment": "主键"},
        {"name": "value_name", "type": "VARCHAR(2000)", "comment": "value条件值"},
        {"name": "value_description", "type": "TEXT", "comment": "value条件解释"},
        {"name": "column_name", "type": "VARCHAR(200)", "comment": "所属字段"},
        {"name": "table_name", "type": "VARCHAR(200)", "comment": "所属表"},
        {"name": "embedding_value_name", "type": "vector(768)", "comment": "value条件值embedding"},
        {"name": "embedding_value_description", "type": "vector(768)", "comment": "value条件解释embedding"}
    ]
    
    # 定义插入映射
    insert_dict = [
        {'source_id': 'description', 'type': 'mapping', 'value': '', 'table_id': 'value_description'},
        {'source_id': 'table', 'type': 'mapping', 'value': '', 'table_id': 'table_name'},
        {'source_id': 'column', 'type': 'mapping', 'value': '', 'table_id': 'column_name'},
        {'source_id': 'description', 'type': 'embedding', 'value': '', 'table_id': 'embedding_value_description'},
        {'source_id': 'value', 'type': 'mapping', 'value': '', 'table_id': 'value_name'},
        {'source_id': 'value', 'type': 'embedding', 'value': '', 'table_id': 'embedding_value_name'},
        {'source_id': '', 'type': 'auto', 'value': '', 'table_id': 'id'}
    ]
    
    # 执行插入
    success = inserter.insert_file_to_vdb(
        file_path=excel_file_path,
        table_name="condition",
        insert_dict=insert_dict,
        table_desc="条件表",
        create_table_fields=table_create_fields,
        batch_size=50
    )
    
    if success:
        logger.success("条件数据插入成功！")
    else:
        logger.error("条件数据插入失败！")
    
    return success


def check_file_exists(file_path: str) -> bool:
    """检查文件是否存在"""
    if not Path(file_path).exists():
        logger.warning(f"文件不存在: {file_path}")
        return False
    return True


def main():
    """主函数，执行所有数据插入任务"""
    logger.info("开始执行文件插入向量数据库任务...")
    
    # 要插入的数据文件列表
    data_files = [
        '/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/sql_data_demo.csv',
        '/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/测试表结构.csv',
        '/data/ideal/code/gsh_code/text2SQL/hsbc/src/database_utils/pg_database/data/poc_data/condition_data.csv'
    ]
    
    # 检查所有文件是否存在
    missing_files = [f for f in data_files if not check_file_exists(f)]
    if missing_files:
        logger.error(f"以下文件不存在，请检查路径: {missing_files}")
        return False
    
    # 执行插入任务
    tasks = [
        ("SQL演示数据", insert_sql_demo_data),
        ("目录数据", insert_catalog_data),
        ("条件数据", insert_condition_data)
    ]
    
    results = []
    for task_name, task_func in tasks:
        try:
            logger.info(f"开始执行任务: {task_name}")
            success = task_func()
            results.append((task_name, success))
            
            if success:
                logger.success(f"任务 {task_name} 执行成功")
            else:
                logger.error(f"任务 {task_name} 执行失败")
                
        except Exception as e:
            logger.error(f"任务 {task_name} 执行异常: {e}")
            results.append((task_name, False))
    
    # 总结结果
    logger.info("=== 任务执行总结 ===")
    success_count = 0
    for task_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        logger.info(f"{task_name}: {status}")
        if success:
            success_count += 1
    
    logger.info(f"总计: {success_count}/{len(results)} 个任务成功")
    
    if success_count == len(results):
        logger.success("🎉 所有任务执行成功！")
        return True
    else:
        logger.error("❌ 部分任务执行失败")
        return False


if __name__ == "__main__":
    # 设置日志级别
    logger.remove()
    logger.add(sys.stderr, level="INFO")
    
    # 执行主函数
    success = main()
    
    # 退出程序
    sys.exit(0 if success else 1)