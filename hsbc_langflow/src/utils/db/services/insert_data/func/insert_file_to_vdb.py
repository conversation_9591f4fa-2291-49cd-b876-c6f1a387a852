'''
File Created: Tuesday, 3rd June 2025 6:10:32 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 9:13:26 am
'''

"""
文件数据插入向量数据库工具
重构自原始的insert_data.py，使用新的客户端架构
"""

import pandas as pd
from typing import List, Dict, Optional, Union, Any
from pathlib import Path
from loguru import logger
from utils.db.common.client_util import pgvector_client


class FileToVectorDB:
    """文件数据插入向量数据库的工具类"""
    
    def __init__(self, 
                 vdb_type: str = 'pgvector', 
                 instance_name: str = 'pgvector',
                 embedding_func: Optional[callable] = None):
        """
        初始化文件插入工具
        
        Args:
            vdb_type: 向量数据库类型，默认为 'pgvector'
            instance_name: 数据库实例名称，默认为 'pgvector'
            embedding_func: 自定义的embedding函数，如果为None则需要在映射中指定
        """
        self.vdb_type = vdb_type
        self.instance_name = instance_name
        self.embedding_func = embedding_func
        self._client = None
    
    @property
    def client(self):
        """延迟获取向量数据库客户端"""
        if self._client is None:
            self._client = pgvector_client
        return self._client
    
    def read_file(self, 
                  file_path: Union[str, Path], 
                  sheet_name: Union[str, int] = 0,
                  encoding: str = 'utf-8') -> pd.DataFrame:
        """
        读取文件数据
        
        Args:
            file_path: 文件路径
            sheet_name: Excel工作表名称或索引
            encoding: 文件编码
            
        Returns:
            pandas.DataFrame: 读取的数据
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, encoding=encoding)
            elif file_path.suffix.lower() == '.xlsx':
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
            elif file_path.suffix.lower() == '.xls':
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
                
            logger.info(f"成功读取文件 {file_path}，共 {len(df)} 行数据")
            return df
            
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            raise
    
    def process_field_mapping(self, 
                             data_row: Dict[str, Any], 
                             mapping: Dict[str, Any], 
                             row_index: int) -> Any:
        """
        处理单个字段的映射
        
        Args:
            data_row: 数据行
            mapping: 映射配置
            row_index: 行索引（从1开始）
            
        Returns:
            处理后的字段值
        """
        source_id = mapping.get('source_id', '')
        mapping_type = mapping.get('type', '')
        value = mapping.get('value', '')
        table_id = mapping.get('table_id', '')
        
        try:
            if mapping_type == 'mapping':  # 直接映射
                return data_row.get(source_id, '')
                
            elif mapping_type == 'fixed':  # 固定值
                return value
                
            elif mapping_type == 'embedding':  # 向量化
                source_value = data_row.get(source_id, '') if source_id else value
                if not source_value:
                    logger.warning(f"第 {row_index} 行，字段 {table_id} 的源值为空")
                    return None
                
                # 使用自定义embedding函数或默认方法
                if self.embedding_func:
                    return self.embedding_func(source_value)
                else:
                    # 如果没有提供embedding函数，尝试使用配置中的方法
                    embedding_method = mapping.get('embedding_method')
                    if embedding_method:
                        return self._get_embedding_by_method(source_value, embedding_method)
                    else:
                        logger.error(f"第 {row_index} 行，字段 {table_id} 需要embedding但未提供embedding函数或方法")
                        return None
                        
            elif mapping_type == 'auto':  # 自增类型
                return str(row_index)
                
            else:
                logger.warning(f"未知的映射类型: {mapping_type}")
                return None
                
        except Exception as e:
            logger.error(f"处理字段映射失败 (第 {row_index} 行，字段 {table_id}): {e}")
            return None
    
    def _get_embedding_by_method(self, text: str, method: str) -> Optional[List[float]]:
        """
        根据指定方法获取embedding
        
        Args:
            text: 要向量化的文本
            method: embedding方法名称
            
        Returns:
            向量列表或None
        """
        try:
            if method == 'openai':
                # 这里可以集成OpenAI的embedding API
                logger.warning("OpenAI embedding方法尚未实现")
                return None
            elif method == 'local_model':
                # 这里可以集成本地模型
                logger.warning("本地模型embedding方法尚未实现")
                return None
            else:
                logger.error(f"未知的embedding方法: {method}")
                return None
        except Exception as e:
            logger.error(f"获取embedding失败: {e}")
            return None
    
    def insert_file_to_vdb(self,
                          file_path: Union[str, Path],
                          table_name: str,
                          insert_dict: List[Dict[str, Any]],
                          table_desc: Optional[str] = None,
                          sheet_name: Union[str, int] = 0,
                          fields: Optional[List[str]] = None,
                          create_table_fields: Optional[List[Dict]] = None,
                          batch_size: int = 50,
                          encoding: str = 'utf-8') -> bool:
        """
        从文件中读取数据并插入到向量数据库
        
        Args:
            file_path: 文件路径
            table_name: 数据库表名
            insert_dict: 字段映射规则列表
            table_desc: 表描述
            sheet_name: Excel工作表名称或索引
            fields: 要提取的字段列表，None表示提取所有列
            create_table_fields: 建表字段定义，如果提供则创建新表
            batch_size: 批次大小
            encoding: 文件编码
            
        Returns:
            bool: 插入是否成功
        """
        try:
            # 1. 读取文件
            df = self.read_file(file_path, sheet_name, encoding)
            
            # 2. 处理字段选择
            if fields is not None:
                missing_fields = [f for f in fields if f not in df.columns]
                if missing_fields:
                    logger.warning(f"以下字段在文件中不存在，将被忽略: {missing_fields}")
                    fields = [f for f in fields if f in df.columns]
                df = df[fields]
            
            # 3. 处理缺失值
            df = df.fillna('')
            data = df.to_dict(orient='records')
            
            # 4. 设置或创建表
            if create_table_fields:
                self.client.create_collection(table_name, create_table_fields, description=table_desc)
            else:
                self.client.set_collection(table_name)
            
            # 5. 分批处理和插入数据
            entities = []
            total_inserted = 0
            
            for i, row_data in enumerate(data, start=1):
                entity = {}
                
                # 根据映射规则处理每个字段
                for mapping in insert_dict:
                    table_id = mapping.get('table_id', '')
                    if table_id:
                        entity[table_id] = self.process_field_mapping(row_data, mapping, i)
                
                entities.append(entity)
                
                # 达到批次大小时执行插入
                if len(entities) >= batch_size:
                    success = self._batch_insert(entities, table_name, total_inserted)
                    if success:
                        total_inserted += len(entities)
                        logger.info(f"已插入 {total_inserted} 条记录到表 {table_name}")
                    else:
                        logger.error(f"批次插入失败，已插入 {total_inserted} 条记录")
                        return False
                    entities = []
            
            # 6. 处理剩余记录
            if entities:
                success = self._batch_insert(entities, table_name, total_inserted)
                if success:
                    total_inserted += len(entities)
                    logger.info(f"插入剩余 {len(entities)} 条记录，总计 {total_inserted} 条记录到表 {table_name}")
                else:
                    logger.error(f"剩余记录插入失败")
                    return False
            
            logger.success(f"完成！总共插入 {total_inserted} 条记录到表 {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"插入文件到向量数据库失败: {e}")
            return False
    
    def _batch_insert(self, entities: List[Dict], table_name: str, current_count: int) -> bool:
        """
        批量插入数据
        
        Args:
            entities: 要插入的实体列表
            table_name: 表名
            current_count: 当前已插入数量
            
        Returns:
            bool: 插入是否成功
        """
        try:
            # 过滤掉包含None值的实体（embedding失败的情况）
            valid_entities = []
            for entity in entities:
                if all(v is not None for v in entity.values()):
                    valid_entities.append(entity)
                else:
                    logger.warning(f"跳过包含None值的实体: {entity}")
            
            if not valid_entities:
                logger.warning("批次中没有有效的实体可插入")
                return True  # 虽然没有插入，但不算失败
            
            # 执行插入
            result = self.client.insert(valid_entities)
            return bool(result)
            
        except Exception as e:
            logger.error(f"批量插入失败 (记录 {current_count + 1} - {current_count + len(entities)}): {e}")
            return False


# 便捷函数
def insert_excel_to_pgvector(file_path: Union[str, Path],
                           table_name: str,
                           insert_dict: List[Dict[str, Any]],
                           instance_name: str = 'pgvector',
                           **kwargs) -> bool:
    """
    便捷函数：插入Excel文件到PGVector数据库
    
    Args:
        file_path: Excel文件路径
        table_name: 表名
        insert_dict: 字段映射规则
        instance_name: PGVector实例名称
        **kwargs: 其他参数传递给insert_file_to_vdb方法
        
    Returns:
        bool: 插入是否成功
    """
    inserter = FileToVectorDB('pgvector', instance_name)
    return inserter.insert_file_to_vdb(file_path, table_name, insert_dict, **kwargs)


def insert_csv_to_pgvector(file_path: Union[str, Path],
                          table_name: str,
                          insert_dict: List[Dict[str, Any]],
                          instance_name: str = 'pgvector',
                          **kwargs) -> bool:
    """
    便捷函数：插入CSV文件到PGVector数据库
    
    Args:
        file_path: CSV文件路径
        table_name: 表名
        insert_dict: 字段映射规则
        instance_name: PGVector实例名称
        **kwargs: 其他参数传递给insert_file_to_vdb方法
        
    Returns:
        bool: 插入是否成功
    """
    inserter = FileToVectorDB('pgvector', instance_name)
    return inserter.insert_file_to_vdb(file_path, table_name, insert_dict, **kwargs) 