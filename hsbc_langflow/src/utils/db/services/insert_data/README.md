# 文件插入向量数据库工具

这个工具包提供了将各种格式的文件（Excel、CSV等）数据插入到向量数据库的功能，重构自原始的 `modules/pg_database/data/insert_data.py`。

## 主要改进

### 🔄 **架构升级**
- **使用新的客户端系统**：基于 `modules/db` 中的单例客户端架构
- **更好的错误处理**：使用 loguru 进行日志记录
- **类型提示**：完整的类型注解支持
- **模块化设计**：更清晰的代码结构

### ✨ **功能增强**
- **支持多种文件格式**：Excel (.xlsx, .xls)、CSV
- **灵活的字段映射**：支持直接映射、固定值、自动生成、向量化
- **自定义embedding**：支持自定义embedding函数
- **批量处理**：可配置的批次大小
- **错误恢复**：更好的错误处理和恢复机制

## 使用方法

### 1. 基本使用

```python
from utils.db.insert_data.insert_file_to_vdb import FileToVectorDB

# 创建插入器
inserter = FileToVectorDB(
    vdb_type='pgvector',
    instance_name='pgvector',
    embedding_func=your_embedding_function  # 可选
)

# 执行插入
success = inserter.insert_file_to_vdb(
    file_path="data.xlsx",
    table_name="my_table",
    insert_dict=mapping_config,
    create_table_fields=table_schema  # 可选，如果表不存在
)
```

### 2. 便捷函数

```python
from utils.db.insert_data.insert_file_to_vdb import insert_excel_to_pgvector

# 直接插入Excel文件
success = insert_excel_to_pgvector(
    file_path="data.xlsx",
    table_name="my_table",
    insert_dict=mapping_config
)
```

## 字段映射配置

字段映射通过 `insert_dict` 参数配置，支持以下类型：

### 📋 **映射类型**

#### 1. 直接映射 (`mapping`)
```python
{
    "source_id": "excel_column_name",  # Excel中的列名
    "table_id": "db_field_name",       # 数据库中的字段名
    "type": "mapping"
}
```

#### 2. 固定值 (`fixed`)
```python
{
    "table_id": "source",
    "type": "fixed",
    "value": "excel_import"  # 固定值
}
```

#### 3. 自动生成 (`auto`)
```python
{
    "table_id": "id",
    "type": "auto"  # 使用行号作为ID
}
```

#### 4. 向量化 (`embedding`)
```python
{
    "source_id": "content",           # 要向量化的源字段
    "table_id": "embedding",          # 存储向量的字段
    "type": "embedding",
    "embedding_method": "openai"      # 可选：指定embedding方法
}
```

## 完整示例

```python
from utils.db.insert_data.insert_file_to_vdb import FileToVectorDB

def my_embedding_function(text: str) -> list:
    """自定义embedding函数"""
    # 调用你的embedding API
    return get_text_embedding(text)

# 字段映射配置
insert_dict = [
    {
        "source_id": "title",
        "table_id": "title",
        "type": "mapping"
    },
    {
        "source_id": "content",
        "table_id": "content", 
        "type": "mapping"
    },
    {
        "source_id": "content",
        "table_id": "embedding",
        "type": "embedding"
    },
    {
        "table_id": "id",
        "type": "auto"
    },
    {
        "table_id": "source",
        "type": "fixed",
        "value": "excel_import"
    }
]

# 建表字段定义（如果表不存在）
create_table_fields = [
    {"name": "id", "type": "VARCHAR(50)", "primary_key": True},
    {"name": "title", "type": "TEXT"},
    {"name": "content", "type": "TEXT"},
    {"name": "embedding", "type": "vector(768)"},
    {"name": "source", "type": "VARCHAR(100)"}
]

# 创建插入器并执行
inserter = FileToVectorDB(
    vdb_type='pgvector',
    instance_name='pgvector',
    embedding_func=my_embedding_function
)

success = inserter.insert_file_to_vdb(
    file_path="documents.xlsx",
    table_name="document_embeddings",
    insert_dict=insert_dict,
    table_desc="文档embedding表",
    create_table_fields=create_table_fields,
    batch_size=50
)

if success:
    print("插入成功！")
else:
    print("插入失败！")
```

## 配置参数

### FileToVectorDB 初始化参数
- `vdb_type`: 向量数据库类型，默认 'pgvector'
- `instance_name`: 数据库实例名称，默认 'pgvector'
- `embedding_func`: 自定义embedding函数，可选

### insert_file_to_vdb 参数
- `file_path`: 文件路径
- `table_name`: 数据库表名
- `insert_dict`: 字段映射规则列表
- `table_desc`: 表描述，可选
- `sheet_name`: Excel工作表名称或索引，默认 0
- `fields`: 要提取的字段列表，None表示全部
- `create_table_fields`: 建表字段定义，可选
- `batch_size`: 批次大小，默认 50
- `encoding`: 文件编码，默认 'utf-8'

## 测试

运行测试文件验证功能：

```bash
cd /data/ideal/code/gsh_code/text2SQL/hsbc_langflow/src
python utils/test/test_insert_file_to_vdb.py
```

## 与原版本的对比

| 特性 | 原版本 | 新版本 |
|------|--------|--------|
| 客户端系统 | 直接传入客户端对象 | 使用统一的单例客户端系统 |
| 错误处理 | 基本的print输出 | 完整的loguru日志系统 |
| 类型提示 | 无 | 完整的类型注解 |
| 代码结构 | 单一函数 | 面向对象设计 |
| 扩展性 | 有限 | 高度可扩展 |
| 测试支持 | 无 | 完整的测试套件 |

## 注意事项

1. **Embedding函数**：如果使用embedding类型的映射，必须提供embedding函数或在映射中指定方法
2. **数据库连接**：确保目标数据库实例已在配置文件中正确配置
3. **表结构**：如果表不存在，需要提供 `create_table_fields` 参数
4. **批次大小**：根据数据量和内存情况调整 `batch_size` 参数
5. **错误恢复**：如果批次插入失败，会停止整个过程，建议检查数据质量 