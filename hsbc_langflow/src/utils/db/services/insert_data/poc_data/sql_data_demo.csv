﻿user_question,knowledge_evidence,sql
"2.1.1 境内农、林、牧、渔业的短期人民币本金是多少？","境内农、林、牧、渔业指的是LOAN_PURPOSE以'A'开头的贷款投向行业，短期指的是TERM_TYPE='01'，计算指标人民币本金指的是(LOAN_BAL_CNY)总和，计算范围为是银保监各项贷款口径的部分，即IS_CBIRC_LOAN='Y'。",SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='01' AND IS_CBIRC_LOAN='Y' AND LOAN_PURPOSE LIKE 'A%';
"2.1.2 境内农、林、牧、渔业的长期人民币本金是多少？","境内农、林、牧、渔业指的是LOAN_PURPOSE以'A'开头的贷款投向行业，长期指的是TERM_TYPE='02'，计算指标人民币本金指的是(LOAN_BAL_CNY)总和，计算范围为是银保监各项贷款口径的部分，即IS_CBIRC_LOAN='Y'。",SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='02' AND IS_CBIRC_LOAN='Y' AND LOAN_PURPOSE LIKE 'A%';
"2.21.1.1 境内信用卡贷款的短期人民币本金是多少？","境内信用卡贷款指的是BUSSINESS_TYPE='19'的贷款商业类型, 短期指的是TERM_TYPE='01'，计算指标人民币本金指的是(LOAN_BAL_CNY)总和，计算范围为是银保监各项贷款口径的部分，即IS_CBIRC_LOAN='Y'。",SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='01' AND IS_CBIRC_LOAN='Y' AND BUSSINESS_TYPE='19';
"2.21.1.2 境内信用卡贷款的长期人民币本金是多少？","境内信用卡贷款指的是BUSSINESS_TYPE='19'的贷款商业类型, 长期指的是TERM_TYPE='02'，计算指标人民币本金指的是(LOAN_BAL_CNY)总和，计算范围为是银保监各项贷款口径的部分，即IS_CBIRC_LOAN='Y'。",SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='02' AND IS_CBIRC_LOAN='Y' AND BUSSINESS_TYPE='19';
"3.1.1 境外贷款的短期人民币本金是多少？","境外贷款指的是LOAN_PURPOSE以Z开头的贷款投向行业，短期指的是TERM_TYPE='01'，计算指标人民币本金指的是(LOAN_BAL_CNY)总和，计算范围为是银保监各项贷款口径的部分，即IS_CBIRC_LOAN='Y'。",SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='01' AND IS_CBIRC_LOAN='Y' AND LOAN_PURPOSE LIKE 'Z%';
"3.1.2 境外贷款的长期人民币本金是多少？","境外贷款指的是LOAN_PURPOSE以Z开头的贷款投向行业，长期指的是TERM_TYPE='02'，计算指标人民币本金指的是(LOAN_BAL_CNY)总和，计算范围为是银保监各项贷款口径的部分，即IS_CBIRC_LOAN='Y'。",SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='02' AND IS_CBIRC_LOAN='Y' AND LOAN_PURPOSE LIKE 'Z%';
"5.4.1 数字要素驱动业的短期人民币本金是多少？","数字要素驱动业指的是DIGITAL_INDUSTRY_TYPE='04'的贷款，短期指的是TERM_TYPE='01'，计算指标人民币本金指的是(LOAN_BAL_CNY)总和，计算范围为是银保监各项贷款口径的部分，即IS_CBIRC_LOAN='Y'。",SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='01' AND IS_CBIRC_LOAN='Y' AND DIGITAL_INDUSTRY_TYPE='04';
"5.4.2 数字要素驱动业的长期人民币本金是多少？","数字要素驱动业指的是DIGITAL_INDUSTRY_TYPE='04'的贷款，长期指的是TERM_TYPE='02'，计算指标人民币本金指的是(LOAN_BAL_CNY)总和，计算范围为是银保监各项贷款口径的部分，即IS_CBIRC_LOAN='Y'。",SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE TERM_TYPE='02' AND IS_CBIRC_LOAN='Y' AND DIGITAL_INDUSTRY_TYPE='04';