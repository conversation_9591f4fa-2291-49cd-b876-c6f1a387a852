# 1. 境内行业类型统计
industry_metrics_domestic = [
    # 1. 按行业类型统计贷款放款金额 - 境内
    {
        "user_question": "2.1 境内农、林、牧、渔业贷款放款金额是多少？",
        "knowledge_evidence": "境内农、林、牧、渔业贷款放款金额是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'A'开头的记录的贷款金额(LOAN_AMT)总和。",
        "sql": "SELECT SUM(LOAN_AMT) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'A%';"
    },
    {
        "user_question": "2.2 境内采矿业贷款放款金额折人民币是多少？",
        "knowledge_evidence": "境内采矿业贷款放款金额折人民币是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'B'开头的记录的贷款金额折人民币(LOAN_AMT_CNY)总和。",
        "sql": "SELECT SUM(LOAN_AMT_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'B%';"
    },
    {
        "user_question": "2.3 境内制造业贷款放款金额折美元是多少？",
        "knowledge_evidence": "境内制造业贷款放款金额折美元是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'C'开头的记录的贷款金额折美元(LOAN_AMT_USD)总和。",
        "sql": "SELECT SUM(LOAN_AMT_USD) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'C%';"
    },
    
    # 2. 按行业类型统计贷款余额 - 境内
    {
        "user_question": "2.4 境内电力、热力、燃气及水的生产和供应业贷款余额是多少？",
        "knowledge_evidence": "境内电力、热力、燃气及水的生产和供应业贷款余额是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'D'开头的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'D%';"
    },
    {
        "user_question": "2.5 境内建筑业贷款余额折人民币是多少？",
        "knowledge_evidence": "境内建筑业贷款余额折人民币是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'E'开头的记录的贷款余额折人民币(LOAN_BAL_CNY)总和。",
        "sql": "SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'E%';"
    },
    {
        "user_question": "2.6 境内批发和零售业贷款余额折美元是多少？",
        "knowledge_evidence": "境内批发和零售业贷款余额折美元是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'F'开头的记录的贷款余额折美元(LOAN_BAL_USD)总和。",
        "sql": "SELECT SUM(LOAN_BAL_USD) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'F%';"
    },
    
    # 3. 按行业类型统计减值准备 - 境内
    {
        "user_question": "2.7 境内交通运输、仓储和邮政业贷款减值准备折人民币是多少？",
        "knowledge_evidence": "境内交通运输、仓储和邮政业贷款减值准备折人民币是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'G'开头的记录的减值准备折人民币(RESERVE_CNY)总和。",
        "sql": "SELECT SUM(RESERVE_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'G%';"
    },
    
    # 4. 按行业和期限类别统计贷款放款金额 - 境内
    {
        "user_question": "2.8 境内住宿和餐饮业短期贷款放款金额是多少？",
        "knowledge_evidence": "境内住宿和餐饮业短期贷款放款金额是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'H'开头且TERM_TYPE='1'的记录的贷款金额(LOAN_AMT)总和。",
        "sql": "SELECT SUM(LOAN_AMT) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'H%' AND TERM_TYPE='1';"
    },
    {
        "user_question": "2.9 境内信息传输、计算机服务和软件业中长期贷款放款金额折人民币是多少？",
        "knowledge_evidence": "境内信息传输、计算机服务和软件业中长期贷款放款金额折人民币是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'I'开头且TERM_TYPE='2'的记录的贷款金额折人民币(LOAN_AMT_CNY)总和。",
        "sql": "SELECT SUM(LOAN_AMT_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'I%' AND TERM_TYPE='2';"
    },
    
    # 5. 按行业和期限类别统计贷款余额 - 境内
    {
        "user_question": "2.10 境内金融业短期贷款余额折美元是多少？",
        "knowledge_evidence": "境内金融业短期贷款余额折美元是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'J'开头且TERM_TYPE='1'的记录的贷款余额折美元(LOAN_BAL_USD)总和。",
        "sql": "SELECT SUM(LOAN_BAL_USD) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'J%' AND TERM_TYPE='1';"
    },
    {
        "user_question": "2.11 境内房地产业中长期贷款余额是多少？",
        "knowledge_evidence": "境内房地产业中长期贷款余额是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'K'开头且TERM_TYPE='2'的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'K%' AND TERM_TYPE='2';"
    },
    
    # 6. 按行业和期限类别统计减值准备 - 境内
    {
        "user_question": "2.12 境内租赁和商务服务业短期贷款减值准备折人民币是多少？",
        "knowledge_evidence": "境内租赁和商务服务业短期贷款减值准备折人民币是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'L'开头且TERM_TYPE='1'的记录的减值准备折人民币(RESERVE_CNY)总和。",
        "sql": "SELECT SUM(RESERVE_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'L%' AND TERM_TYPE='1';"
    },
    {
        "user_question": "2.13 境内科学研究和技术服务业中长期贷款减值准备折人民币是多少？",
        "knowledge_evidence": "境内科学研究和技术服务业中长期贷款减值准备折人民币是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'M'开头且TERM_TYPE='2'的记录的减值准备折人民币(RESERVE_CNY)总和。",
        "sql": "SELECT SUM(RESERVE_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'M%' AND TERM_TYPE='2';"
    },
    
    # 剩余行业 - 境内
    {
        "user_question": "2.14 境内水利、环境和公共设施管理业贷款余额是多少？",
        "knowledge_evidence": "境内水利、环境和公共设施管理业贷款余额是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'N'开头的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'N%';"
    },
    {
        "user_question": "2.15 境内居民服务、修理和其他服务业贷款余额折人民币是多少？",
        "knowledge_evidence": "境内居民服务、修理和其他服务业贷款余额折人民币是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'O'开头的记录的贷款余额折人民币(LOAN_BAL_CNY)总和。",
        "sql": "SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'O%';"
    },
    {
        "user_question": "2.16 境内教育短期贷款余额是多少？",
        "knowledge_evidence": "境内教育短期贷款余额是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'P'开头且TERM_TYPE='1'的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'P%' AND TERM_TYPE='1';"
    },
    {
        "user_question": "2.17 境内卫生、社会工作中长期贷款余额折美元是多少？",
        "knowledge_evidence": "境内卫生、社会工作中长期贷款余额折美元是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'Q'开头且TERM_TYPE='2'的记录的贷款余额折美元(LOAN_BAL_USD)总和。",
        "sql": "SELECT SUM(LOAN_BAL_USD) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'Q%' AND TERM_TYPE='2';"
    },
    {
        "user_question": "2.18 境内文化、体育和娱乐业贷款放款金额是多少？",
        "knowledge_evidence": "境内文化、体育和娱乐业贷款放款金额是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'R'开头的记录的贷款金额(LOAN_AMT)总和。",
        "sql": "SELECT SUM(LOAN_AMT) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'R%';"
    },
    {
        "user_question": "2.19 境内公共管理、社会保障和社会组织短期贷款减值准备折人民币是多少？",
        "knowledge_evidence": "境内公共管理、社会保障和社会组织短期贷款减值准备折人民币是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'S'开头且TERM_TYPE='1'的记录的减值准备折人民币(RESERVE_CNY)总和。",
        "sql": "SELECT SUM(RESERVE_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'S%' AND TERM_TYPE='1';"
    },
    {
        "user_question": "2.20 境内国际组织中长期贷款余额是多少？",
        "knowledge_evidence": "境内国际组织中长期贷款余额是DOMESTIC_FOREIGN_SIGNS='1'且INDU_TYPE以'T'开头且TERM_TYPE='2'的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND INDU_TYPE LIKE 'T%' AND TERM_TYPE='2';"
    },
    
    # 2.21-2.23 部分 - 境内
    # 个人贷款及明细类别 - 按BUSSINESS_TYPE判断 - 境内
    {
        "user_question": "2.21 境内个人贷款（不含个人经营性贷款）贷款余额是多少？",
        "knowledge_evidence": "境内个人贷款（不含个人经营性贷款）贷款余额是DOMESTIC_FOREIGN_SIGNS='1'且BUSSINESS_TYPE以'0202'开头的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND BUSSINESS_TYPE LIKE '0202%';"
    },
    {
        "user_question": "2.21 境内个人贷款（不含个人经营性贷款）短期贷款放款金额折人民币是多少？",
        "knowledge_evidence": "境内个人贷款（不含个人经营性贷款）短期贷款放款金额折人民币是DOMESTIC_FOREIGN_SIGNS='1'且BUSSINESS_TYPE以'0202'开头且TERM_TYPE='1'的记录的贷款金额折人民币(LOAN_AMT_CNY)总和。",
        "sql": "SELECT SUM(LOAN_AMT_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND BUSSINESS_TYPE LIKE '0202%' AND TERM_TYPE='1';"
    },
    
    # 个人贷款 - 信用卡 - 境内
    {
        "user_question": "2.21.1 境内信用卡贷款余额折美元是多少？",
        "knowledge_evidence": "境内信用卡贷款余额折美元是DOMESTIC_FOREIGN_SIGNS='1'且BUSSINESS_TYPE='0202'且INDU_TYPE为空的记录的贷款余额折美元(LOAN_BAL_USD)总和。",
        "sql": "SELECT SUM(LOAN_BAL_USD) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND BUSSINESS_TYPE='0202' AND (INDU_TYPE IS NULL OR INDU_TYPE='');"
    },
    
    # 个人贷款 - 汽车贷款 - 境内
    {
        "user_question": "2.21.2 境内汽车贷款余额折人民币是多少？",
        "knowledge_evidence": "境内汽车贷款余额折人民币是DOMESTIC_FOREIGN_SIGNS='1'且BUSSINESS_TYPE='020202'的记录的贷款余额折人民币(LOAN_BAL_CNY)总和。",
        "sql": "SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND BUSSINESS_TYPE='020202';"
    },
    
    # 个人贷款 - 住房按揭贷款 - 境内
    {
        "user_question": "2.21.3 境内住房按揭贷款放款金额是多少？",
        "knowledge_evidence": "境内住房按揭贷款放款金额是DOMESTIC_FOREIGN_SIGNS='1'且BUSSINESS_TYPE='020201'的记录的贷款金额(LOAN_AMT)总和。",
        "sql": "SELECT SUM(LOAN_AMT) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND BUSSINESS_TYPE='020201';"
    },
    
    # 个人贷款 - 其他 - 境内
    {
        "user_question": "2.21.4 境内其他个人贷款余额是多少？",
        "knowledge_evidence": "境内其他个人贷款余额是DOMESTIC_FOREIGN_SIGNS='1'且BUSSINESS_TYPE='020299'的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND BUSSINESS_TYPE='020299';"
    },
    
    # 买断式转贴现 - 境内
    {
        "user_question": "2.22 境内买断式转贴现贷款余额是多少？",
        "knowledge_evidence": "境内买断式转贴现贷款余额是DOMESTIC_FOREIGN_SIGNS='1'且BUSSINESS_TYPE='17'的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND BUSSINESS_TYPE='17';"
    },
    
    # 买断其他票据类资产 - 境内
    {
        "user_question": "2.23 境内买断其他票据类资产贷款余额是多少？",
        "knowledge_evidence": "境内买断其他票据类资产贷款余额是DOMESTIC_FOREIGN_SIGNS='1'且BUSSINESS_TYPE='18'的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='1' AND BUSSINESS_TYPE='18';"
    }
]

# 2. 境外贷款统计
foreign_loan_metrics = [
    # 境外贷款总量
    {
        "user_question": "3.1 境外贷款余额是多少？",
        "knowledge_evidence": "境外贷款余额是DOMESTIC_FOREIGN_SIGNS='2'的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='2';"
    },
    {
        "user_question": "3.2 境外贷款余额折人民币是多少？",
        "knowledge_evidence": "境外贷款余额折人民币是DOMESTIC_FOREIGN_SIGNS='2'的记录的贷款余额折人民币(LOAN_BAL_CNY)总和。",
        "sql": "SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='2';"
    },
    {
        "user_question": "3.3 境外贷款余额折美元是多少？",
        "knowledge_evidence": "境外贷款余额折美元是DOMESTIC_FOREIGN_SIGNS='2'的记录的贷款余额折美元(LOAN_BAL_USD)总和。",
        "sql": "SELECT SUM(LOAN_BAL_USD) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='2';"
    },
    
    # 境外贷款按期限分类
    {
        "user_question": "3.4 境外短期贷款放款金额是多少？",
        "knowledge_evidence": "境外短期贷款放款金额是DOMESTIC_FOREIGN_SIGNS='2'且TERM_TYPE='1'的记录的贷款金额(LOAN_AMT)总和。",
        "sql": "SELECT SUM(LOAN_AMT) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='2' AND TERM_TYPE='1';"
    },
    {
        "user_question": "3.5 境外中长期贷款放款金额是多少？",
        "knowledge_evidence": "境外中长期贷款放款金额是DOMESTIC_FOREIGN_SIGNS='2'且TERM_TYPE='2'的记录的贷款金额(LOAN_AMT)总和。",
        "sql": "SELECT SUM(LOAN_AMT) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='2' AND TERM_TYPE='2';"
    },
    
    # 境外贷款减值准备
    {
        "user_question": "3.6 境外贷款减值准备折人民币是多少？",
        "knowledge_evidence": "境外贷款减值准备折人民币是DOMESTIC_FOREIGN_SIGNS='2'的记录的减值准备折人民币(RESERVE_CNY)总和。",
        "sql": "SELECT SUM(RESERVE_CNY) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='2';"
    },
    
    # 境外个人贷款
    {
        "user_question": "3.7 境外个人贷款（不含个人经营性贷款）贷款余额是多少？",
        "knowledge_evidence": "境外个人贷款（不含个人经营性贷款）贷款余额是DOMESTIC_FOREIGN_SIGNS='2'且BUSSINESS_TYPE以'0202'开头的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='2' AND BUSSINESS_TYPE LIKE '0202%';"
    },
    
    # 境外票据业务
    {
        "user_question": "3.8 境外买断式转贴现和买断其他票据类资产贷款余额是多少？",
        "knowledge_evidence": "境外买断式转贴现和买断其他票据类资产贷款余额是DOMESTIC_FOREIGN_SIGNS='2'且BUSSINESS_TYPE IN ('17','18')的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DOMESTIC_FOREIGN_SIGNS='2' AND BUSSINESS_TYPE IN ('17','18');"
    }
]
other_metrics = [
    # 高技术产业
    {
        "user_question": "4. 高技术产业贷款放款金额是多少？",
        "knowledge_evidence": "高技术产业贷款放款金额是IS_HIGH_TECH_INDUSTRY='Y'的记录的贷款金额(LOAN_AMT)总和。",
        "sql": "SELECT SUM(LOAN_AMT) FROM ADM_LON_VAROIUS WHERE IS_HIGH_TECH_INDUSTRY='Y';"
    },
    {
        "user_question": "4. 高技术产业短期贷款余额折人民币是多少？",
        "knowledge_evidence": "高技术产业短期贷款余额折人民币是IS_HIGH_TECH_INDUSTRY='Y'且TERM_TYPE='1'的记录的贷款余额折人民币(LOAN_BAL_CNY)总和。",
        "sql": "SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE IS_HIGH_TECH_INDUSTRY='Y' AND TERM_TYPE='1';"
    },
    {
        "user_question": "4. 高技术产业中长期贷款减值准备折人民币是多少？",
        "knowledge_evidence": "高技术产业中长期贷款减值准备折人民币是IS_HIGH_TECH_INDUSTRY='Y'且TERM_TYPE='2'的记录的减值准备折人民币(RESERVE_CNY)总和。",
        "sql": "SELECT SUM(RESERVE_CNY) FROM ADM_LON_VAROIUS WHERE IS_HIGH_TECH_INDUSTRY='Y' AND TERM_TYPE='2';"
    },

    # 数字经济核心产业 - 数字产品制造业
    {
        "user_question": "5.1 数字产品制造业贷款余额折美元是多少？",
        "knowledge_evidence": "数字产品制造业贷款余额折美元是DIGITAL_INDUSTRY_TYPE='01'的记录的贷款余额折美元(LOAN_BAL_USD)总和。",
        "sql": "SELECT SUM(LOAN_BAL_USD) FROM ADM_LON_VAROIUS WHERE DIGITAL_INDUSTRY_TYPE='01';"
    },
    {
        "user_question": "5.1 数字产品制造业短期贷款放款金额是多少？",
        "knowledge_evidence": "数字产品制造业短期贷款放款金额是DIGITAL_INDUSTRY_TYPE='01'且TERM_TYPE='1'的记录的贷款金额(LOAN_AMT)总和。",
        "sql": "SELECT SUM(LOAN_AMT) FROM ADM_LON_VAROIUS WHERE DIGITAL_INDUSTRY_TYPE='01' AND TERM_TYPE='1';"
    },

    # 数字经济核心产业 - 数字产品服务业
    {
        "user_question": "5.2 数字产品服务业贷款放款金额折人民币是多少？",
        "knowledge_evidence": "数字产品服务业贷款放款金额折人民币是DIGITAL_INDUSTRY_TYPE='02'的记录的贷款金额折人民币(LOAN_AMT_CNY)总和。",
        "sql": "SELECT SUM(LOAN_AMT_CNY) FROM ADM_LON_VAROIUS WHERE DIGITAL_INDUSTRY_TYPE='02';"
    },
    {
        "user_question": "5.2 数字产品服务业中长期贷款余额是多少？",
        "knowledge_evidence": "数字产品服务业中长期贷款余额是DIGITAL_INDUSTRY_TYPE='02'且TERM_TYPE='2'的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE DIGITAL_INDUSTRY_TYPE='02' AND TERM_TYPE='2';"
    },

    # 数字经济核心产业 - 数字技术应用业
    {
        "user_question": "5.3 数字技术应用业贷款余额折人民币是多少？",
        "knowledge_evidence": "数字技术应用业贷款余额折人民币是DIGITAL_INDUSTRY_TYPE='03'的记录的贷款余额折人民币(LOAN_BAL_CNY)总和。",
        "sql": "SELECT SUM(LOAN_BAL_CNY) FROM ADM_LON_VAROIUS WHERE DIGITAL_INDUSTRY_TYPE='03';"
    },
    {
        "user_question": "5.3 数字技术应用业短期贷款减值准备折人民币是多少？",
        "knowledge_evidence": "数字技术应用业短期贷款减值准备折人民币是DIGITAL_INDUSTRY_TYPE='03'且TERM_TYPE='1'的记录的减值准备折人民币(RESERVE_CNY)总和。",
        "sql": "SELECT SUM(RESERVE_CNY) FROM ADM_LON_VAROIUS WHERE DIGITAL_INDUSTRY_TYPE='03' AND TERM_TYPE='1';"
    },

    # 数字经济核心产业 - 数字要素驱动业
    {
        "user_question": "5.4 数字要素驱动业贷款放款金额折美元是多少？",
        "knowledge_evidence": "数字要素驱动业贷款放款金额折美元是DIGITAL_INDUSTRY_TYPE='04'的记录的贷款金额折美元(LOAN_AMT_USD)总和。",
        "sql": "SELECT SUM(LOAN_AMT_USD) FROM ADM_LON_VAROIUS WHERE DIGITAL_INDUSTRY_TYPE='04';"
    },
    {
        "user_question": "5.4 数字要素驱动业中长期贷款余额折美元是多少？",
        "knowledge_evidence": "数字要素驱动业中长期贷款余额折美元是DIGITAL_INDUSTRY_TYPE='04'且TERM_TYPE='2'的记录的贷款余额折美元(LOAN_BAL_USD)总和。",
        "sql": "SELECT SUM(LOAN_BAL_USD) FROM ADM_LON_VAROIUS WHERE DIGITAL_INDUSTRY_TYPE='04' AND TERM_TYPE='2';"
    },

    # 知识产权密集型产业
    {
        "user_question": "6. 知识产权密集型产业贷款余额是多少？",
        "knowledge_evidence": "知识产权密集型产业贷款余额是IS_KNOWLEDGE_INDUSTRY='Y'的记录的贷款余额(LOAN_BAL)总和。",
        "sql": "SELECT SUM(LOAN_BAL) FROM ADM_LON_VAROIUS WHERE IS_KNOWLEDGE_INDUSTRY='Y';"
    },
    {
        "user_question": "6. 知识产权密集型产业短期贷款放款金额折人民币是多少？",
        "knowledge_evidence": "知识产权密集型产业短期贷款放款金额折人民币是IS_KNOWLEDGE_INDUSTRY='Y'且TERM_TYPE='1'的记录的贷款金额折人民币(LOAN_AMT_CNY)总和。",
        "sql": "SELECT SUM(LOAN_AMT_CNY) FROM ADM_LON_VAROIUS WHERE IS_KNOWLEDGE_INDUSTRY='Y' AND TERM_TYPE='1';"
    },
    {
        "user_question": "6. 知识产权密集型产业中长期贷款减值准备折人民币是多少？",
        "knowledge_evidence": "知识产权密集型产业中长期贷款减值准备折人民币是IS_KNOWLEDGE_INDUSTRY='Y'且TERM_TYPE='2'的记录的减值准备折人民币(RESERVE_CNY)总和。",
        "sql": "SELECT SUM(RESERVE_CNY) FROM ADM_LON_VAROIUS WHERE IS_KNOWLEDGE_INDUSTRY='Y' AND TERM_TYPE='2';"
    }
]
