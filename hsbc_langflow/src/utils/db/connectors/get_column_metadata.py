'''
File Created: Thursday, 5th June 2025 9:13:26 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 9:39:30 am
'''

'''
File Created: Tuesday, 3rd June 2025 8:00:00 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 8:41:14 am
'''

"""
PGVector客户端测试脚本
使用client_util的pgvector_client连接到adm数据库进行测试
"""

from typing import Dict, Any, Optional, List
import json
import hydra
from omegaconf import DictConfig

from utils.common.config_util import config
from utils.common.logger_util import logger
# 统一从 client_util 导入懒加载客户端，确保安全
from utils.db.common import client_util
from modules.pg_database.llm.llm_chat import get_embeddings
from modules.pg_database.pgvector.rag import hybrid_search
from utils.db.get_partitionkey import get_or_create_partition_key


class ColumnMetadataResolver:
    """
    列元数据解析器 - 负责从MySQL和PGVector中查找列信息
    支持直接查询和向量搜索回退机制
    """
    
    def __init__(self, 
                 mysql_table_name: str = 'model_info',
                 embedding_table_name: str = 'hsbc_embedding_data',
                 distance_threshold: float = 0.3,
                 rdb_client=None,
                 vdb_client=None):
        """
        初始化列元数据解析器
        
        Args:
            mysql_table_name: MySQL中存储列元数据的表名
            embedding_table_name: PGVector中存储向量的表名
            distance_threshold: 向量搜索的距离阈值
            rdb_client: 可选，用于覆盖默认的RDB客户端 (主要用于测试)
            vdb_client: 可选，用于覆盖默认的VDB客户端 (主要用于测试)
        """
        self.mysql_table_name = mysql_table_name
        self.embedding_table_name = embedding_table_name
        self.distance_threshold = distance_threshold
        
        # 优先使用覆盖的客户端（主要用于测试），否则使用全局的懒加载客户端
        self.rdb_client = rdb_client if rdb_client is not None else client_util.rdb_client
        self.vdb_client = vdb_client if vdb_client is not None else client_util.vdb_client
        
        # 可配置的查询字段
        self.mysql_query_columns = [
            "col_id", "col_code", "table_code", "table_name", 
            "col_name", "col_real_name", "col_name_cn", 
            "col_desc", "col_type"
        ]
        
        # 可配置的表匹配规则
        self.table_match_patterns = [
            lambda table_name, identifier: table_name.lower() == identifier.lower(),
            lambda table_code, identifier: table_code.lower() == identifier.lower(),
            lambda table_real_name, identifier: table_real_name.lower() == identifier.lower(),
            lambda table_name, identifier: f'table_{table_name}'.lower() == identifier.lower(),
            lambda table_name, identifier: table_name.lower() == f'table_{identifier}'.lower()
        ]
        
        # 可配置的列匹配规则
        self.column_match_patterns = [
            lambda col_name, identifier: col_name.lower() == identifier.lower(),
            lambda col_real_name, identifier: col_real_name.lower() == identifier.lower()
        ]
    
    def find_field_from_merged_info(self,
                                   table_identifier: str,
                                   column_identifier: str,
                                   merged_info: Dict[str, Dict[str, Any]],
                                   output_field: str) -> Optional[Any]:
        """
        在 merged_column_info 中查找匹配的列并返回其指定的字段值
        
        Args:
            table_identifier: 表的标识符，可以是 table_name 或 table_code
            column_identifier: 列的标识符，可以是 col_name 或 col_real_name
            merged_info: 包含所有列详细信息的大字典
            output_field: 指定需要从匹配的条目中返回的字段名称
            
        Returns:
            匹配条目中指定 output_field 的值，如果未找到或指定字段不存在则返回 None
        """
        for column_details in merged_info.values():
            # 检查表标识符是否匹配
            table_match = self._check_table_match(column_details, table_identifier)
            
            # 检查列标识符是否匹配
            column_match = self._check_column_match(column_details, column_identifier)
            
            # 如果表和列都匹配，则返回指定 output_field 的值
            if table_match and column_match:
                return column_details.get(output_field, None)
        
        return None

    def _check_table_match(self, column_details: Dict[str, Any], table_identifier: str) -> bool:
        """检查表标识符是否匹配"""
        table_name = str(column_details.get('table_name', ''))
        table_code = str(column_details.get('table_code', ''))
        table_real_name = str(column_details.get('table_real_name', ''))
        
        # 使用配置的匹配规则
        for pattern in self.table_match_patterns:
            try:
                if pattern(table_name, table_identifier) or \
                   pattern(table_code, table_identifier) or \
                   pattern(table_real_name, table_identifier):
                    return True
            except Exception as e:
                logger.debug(f"Table match pattern failed: {e}")
                continue
        
        return False

    def _check_column_match(self, column_details: Dict[str, Any], column_identifier: str) -> bool:
        """检查列标识符是否匹配"""
        col_name = str(column_details.get('col_name', ''))
        col_real_name = str(column_details.get('col_real_name', ''))
        
        # 使用配置的匹配规则
        for pattern in self.column_match_patterns:
            try:
                if pattern(col_name, column_identifier) or \
                   pattern(col_real_name, column_identifier):
                    return True
            except Exception as e:
                logger.debug(f"Column match pattern failed: {e}")
                continue
        
        return False

    def get_col_info_from_mysql(self, 
                               column_identifier: str,
                               additional_conditions: Optional[List[Dict[str, Any]]] = None) -> Optional[Dict[str, Any]]:
        """
        从MySQL查询列信息
        
        Args:
            column_identifier: 列标识符
            additional_conditions: 额外的查询条件
            
        Returns:
            包含列详细信息的字典，如果未找到则返回 None
        """
        if not column_identifier:
            logger.warning("column_identifier missing for get_col_info_from_mysql")
            return None

        try:
            # 获取RDB客户端
            client = self.rdb_client
            if not client:
                logger.warning("RDB client not available")
                return None
                
            # 构建查询条件
            conditions = [{"col_name": "col_name", "col_val": column_identifier}]
            if additional_conditions:
                conditions.extend(additional_conditions)

            # 使用新的SQLAlchemy客户端进行查询
            results = client.select(
                table=self.mysql_table_name,
                columns=self.mysql_query_columns,
                condition={"col_name": column_identifier},
                limit=1
            )
            
            if results:
                col_details = results[0]
                
                # 处理col_id字段
                col_details = self._process_col_id(col_details, column_identifier)
                
                logger.info(f"MySQL查询成功找到列信息: {column_identifier}")
                return col_details
            else:
                logger.info(f"MySQL中未找到匹配的列: {column_identifier}")
                return None
                
        except Exception as e:
            logger.error(f"MySQL查询列信息时出错 (col_name: '{column_identifier}'): {e}", exc_info=True)
            return None

    def _process_col_id(self, col_details: Dict[str, Any], column_identifier: str) -> Dict[str, Any]:
        """处理col_id字段，确保其为字符串格式"""
        # 检查是否有直接的col_id
        if 'col_id' in col_details and col_details['col_id'] is not None:
            col_details['col_id'] = str(col_details['col_id'])
        # 如果没有直接的col_id，尝试从col_code中提取
        elif 'col_code' in col_details and isinstance(col_details['col_code'], str) and '#' in col_details['col_code']:
            try:
                extracted_id = col_details['col_code'].split('#')[-1]
                col_details['col_id'] = str(extracted_id)
                logger.info(f"从col_code中提取col_id '{extracted_id}': {col_details['col_code']}")
            except IndexError:
                logger.warning(f"无法从col_code中提取col_id: {col_details['col_code']}")
                col_details.pop('col_id', None)
        else:
            logger.warning(f"列 {column_identifier} 缺少col_id和有效的col_code")
            col_details.pop('col_id', None)
        
        return col_details

    def vector_search_column_metadata(self,
                                    table_identifier: str,
                                    column_identifier: str,
                                    model_id: str,
                                    custom_distance_threshold: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """
        使用向量搜索查找列元数据
        
        Args:
            table_identifier: 表标识符
            column_identifier: 列标识符
            model_id: 模型ID，用于分区
            custom_distance_threshold: 自定义距离阈值
            
        Returns:
            包含列详细信息的字典，如果未找到则返回 None
        """
        logger.info(f"开始向量搜索回退: 列'{column_identifier}' 表'{table_identifier}' 模型'{model_id}'")
        
        if not model_id:
            logger.warning("缺少model_id")
            return None
            
        # 获取客户端实例
        vdb_client = self.vdb_client
        rdb_client = self.rdb_client
        if not vdb_client or not rdb_client:
            logger.warning("缺少vdb_client或rdb_client")
            return None

        distance_threshold = custom_distance_threshold or self.distance_threshold

        try:
            # 1. 获取分区键
            partition_key = get_or_create_partition_key(model_id, rdb_client)
            if not partition_key:
                logger.error(f"获取分区键失败，model_id: {model_id}")
                return None
            
            # 2. 获取列标识符的向量
            embedding_response = get_embeddings(column_identifier)
            if not self._validate_embedding_response(embedding_response):
                logger.error(f"获取列标识符向量失败: {column_identifier}")
                return None
            
            search_embedding = embedding_response["data"][0]['embedding']

            # 3. 准备向量搜索参数
            vec_dict = {"embedding": search_embedding}
            rank_dict = {"type": "vector", "rank_rule": [{"embedding": 1.0}]}
            out_fields = ["col_id", "distance"]
            expr = f"table_key = '{table_identifier}'"
            
            # 4. 执行搜索
            results = hybrid_search(
                pgvector_client=vdb_client,
                table_name=self.embedding_table_name,
                vec_dict=vec_dict,
                rank_dict=rank_dict,
                out_filed=out_fields,
                topk=1,
                expr=expr,
                partition_name=partition_key,
                metric_type="cosine"
            )
            
            logger.info(f"向量搜索结果: {results}")

            # 5. 处理结果
            return self._process_vector_search_results(results, column_identifier, distance_threshold)

        except Exception as e:
            logger.error(f"向量搜索过程中出错: {e}", exc_info=True)
            return None

    def _validate_embedding_response(self, embedding_response: Any) -> bool:
        """验证embedding响应的有效性"""
        return (embedding_response and 
                "data" in embedding_response and 
                embedding_response["data"] and 
                'embedding' in embedding_response["data"][0])

    def _process_vector_search_results(self, 
                                     results: List[Dict[str, Any]], 
                                     column_identifier: str, 
                                     distance_threshold: float) -> Optional[Dict[str, Any]]:
        """处理向量搜索结果"""
        if not results or not isinstance(results, list) or len(results) == 0:
            logger.info(f"向量搜索未找到结果: {column_identifier}")
            return None

        top_result = results[0]
        distance = top_result.get('distance')
        
        if distance is not None and distance < distance_threshold:
            logger.info(f"向量搜索找到匹配项，距离: {distance} (阈值: {distance_threshold})")
            
            # 确保col_id是字符串
            if 'col_id' in top_result and top_result['col_id'] is not None:
                top_result['col_id'] = str(top_result['col_id'])
            
            # 移除距离字段
            top_result.pop('distance', None)
            return top_result
        else:
            logger.warning(f"向量搜索结果未达到阈值，距离: {distance}")
            return None

    def get_col_info_robust(self,
                           table_identifier: str,
                           column_identifier: str,
                           model_id: str,
                           enable_vector_fallback: bool = True) -> Optional[Dict[str, Any]]:
        """
        健壮的列信息获取方法，先尝试MySQL直接查询，失败后回退到向量搜索
        
        Args:
            table_identifier: 表标识符
            column_identifier: 列标识符
            model_id: 模型ID
            enable_vector_fallback: 是否启用向量搜索回退
            
        Returns:
            包含列详细信息的字典，如果未找到则返回 None
        """
        # 1. 尝试MySQL直接查询
        col_details = self.get_col_info_from_mysql(column_identifier)
        
        if col_details:
            logger.info(f"MySQL直接查询成功: {column_identifier}")
            return col_details
        
        # 2. 如果启用了向量搜索回退
        if enable_vector_fallback:
            logger.info(f"MySQL查询失败，尝试向量搜索回退: {column_identifier}")
            col_details = self.vector_search_column_metadata(
                table_identifier, column_identifier, model_id
            )
            
            if col_details:
                logger.info(f"向量搜索回退成功: {column_identifier}")
                return col_details
        
        logger.warning(f"所有查询方法都失败了: {column_identifier}")
        return None


# 创建默认实例，保持向后兼容
# 这里的实例化现在是安全的，因为它只创建了 ColumnMetadataResolver 对象，
# 并未立即触发数据库连接。连接将在第一次调用其方法时由 LazyClient 代理触发。
_default_resolver = ColumnMetadataResolver()

# 保持原有函数接口的兼容性
def find_field_from_merged_info(table_identifier: str,
                               column_identifier: str,
                               merged_info: Dict[str, Dict[str, Any]],
                               output_field: str) -> Optional[Any]:
    """保持向后兼容的函数接口"""
    return _default_resolver.find_field_from_merged_info(
        table_identifier, column_identifier, merged_info, output_field
    )

def get_col_id_from_mysql(column_identifier: str,
                         mysql_table_name: str = 'model_info') -> Optional[Dict[str, Any]]:
    """保持向后兼容的函数接口"""
    resolver = ColumnMetadataResolver(mysql_table_name=mysql_table_name)
    return resolver.get_col_info_from_mysql(column_identifier)

def vector_search_column_metadata(table_identifier: str,
                                column_identifier: str,
                                model_id: str,
                                distance_threshold: float = 0.3) -> Optional[Dict[str, Any]]:
    """保持向后兼容的函数接口"""
    resolver = ColumnMetadataResolver(distance_threshold=distance_threshold)
    return resolver.vector_search_column_metadata(
        table_identifier, column_identifier, model_id
    )

def get_col_id_robust(table_identifier: str,
                     column_identifier: str,
                     model_id: str) -> Optional[Dict[str, Any]]:
    """保持向后兼容的函数接口"""
    return _default_resolver.get_col_info_robust(
        table_identifier, column_identifier, model_id
    )

if __name__ == "__main__":
    def run_tests():
        """包含所有测试逻辑的函数"""
        # 测试数据
        merged_column_info = {
            "adm_lon_varoius_loan_bal": {
                "col_id": "40",
                "col_code": "meta#1.ADM_LON_VAROIUS#40",
                "table_code": "1.ADM_LON_VAROIUS",
                "table_name": "1",
                "table_real_name": "ADM_LON_VAROIUS",
                "col_name": "ADM_LON_VAROIUS_LOAN_BAL",
                "col_real_name": "LOAN_BAL",
                "col_name_cn": "本金余额",
                "col_desc": "本金余额",
                "col_type": "NUMBER",
                "col_data_example": None
            },
            "adm_lon_varoius_corp_hold_type": {
                "col_id": "1419",
                "col_code": "meta#13.adm_lon_varoius#1419",
                "table_code": "13.adm_lon_varoius",
                "table_name": "1001",
                "table_real_name": "adm_lon_varoius",
                "col_name": "adm_lon_varoius_corp_hold_type",
                "col_real_name": "corp_hold_type",
                "col_name_cn": "企业控股类型",
                "col_desc": "企业控股类型",
                "col_type": "STRING",
                "col_data_example": None
            },
            "adm_lon_varoius_loan_bal_cny": {
                "col_id": "1446",
                "col_code": "meta#13.adm_lon_varoius#1446",
                "table_code": "13.adm_lon_varoius",
                "table_name": "1001",
                "table_real_name": "adm_lon_varoius",
                "col_name": "adm_lon_varoius_loan_bal_cny",
                "col_real_name": "loan_bal_cny",
                "col_name_cn": "本金余额_折人民币",
                "col_desc": "本金余额_折人民币",
                "col_type": "NUMBER",
                "col_data_example": None
            },
            "adm_pub_branch_level_info_bank_code": {
                "col_id": "1524",
                "col_code": "meta#13.adm_pub_branch_level_info#1524",
                "table_code": "13.adm_pub_branch_level_info",
                "table_name": "1001",
                "table_real_name": "adm_pub_branch_level_info",
                "col_name": "adm_pub_branch_level_info_bank_code",
                "col_real_name": "bank_code",
                "col_name_cn": "no_comment",
                "col_desc": "no_comment",
                "col_type": "STRING",
                "col_data_example": None
            }
        }

        print("=== 测试 ColumnMetadataResolver 类 ===")
        
        # 创建解析器实例
        # 此处创建的 resolver 将使用由 Hydra 配置好的全局懒加载客户端
        resolver = ColumnMetadataResolver()
        
        # 测试1: 从merged_info中查找字段
        print("\n1. 测试从merged_info中查找字段:")
        col_code_result = resolver.find_field_from_merged_info(
            "1001", "loan_bal", merged_column_info, "col_code"
        )
        print(f"获取 col_code: {col_code_result}")
        
        col_id_result = resolver.find_field_from_merged_info(
            "1001", "adm_lon_varoius_corp_hold_type", merged_column_info, "col_id"
        )
        print(f"获取 col_id: {col_id_result}")
        
        col_cn_result = resolver.find_field_from_merged_info(
            "1001", "bank_code", merged_column_info, "col_name_cn"
        )
        print(f"获取 col_name_cn: {col_cn_result}")

        # 测试2: MySQL查询（需要实际的数据库连接）
        print("\n2. 测试MySQL查询:")
        try:
            mysql_result = resolver.get_col_info_from_mysql("loan_bal")
            print(f"MySQL查询结果: {mysql_result}")
        except Exception as e:
            print(f"MySQL查询失败: {e}")

        # 测试3: 健壮查询（需要实际的数据库连接）
        print("\n3. 测试健壮查询:")
        try:
            robust_result = resolver.get_col_info_robust(
                "adm_lon_varoius", "loan_bal", "test_model_001"
            )
            print(f"健壮查询结果: {robust_result}")
            
            # 测试拼写错误的情况
            typo_result = resolver.get_col_info_robust(
                "adm_lon_varoius", "loan balance", "test_model_001"
            )
            print(f"拼写错误查询结果: {typo_result}")
        except Exception as e:
            print(f"健壮查询失败: {e}")

        print("\n=== 测试向后兼容的函数接口 ===")
        
        # 测试向后兼容的函数
        compat_result = find_field_from_merged_info(
            "1001", "loan_bal", merged_column_info, "col_code"
        )
        print(f"兼容函数测试: {compat_result}")

        print("\n=== 测试完成 ===")


    @hydra.main(config_path="../../config", config_name="config", version_base=None)
    def hydra_test_runner(cfg: DictConfig):
        """
        使用Hydra加载配置并运行测试。
        这是运行此文件进行测试的入口点。
        """
        # 关键：用加载的配置初始化全局config单例
        config.__init__(cfg)
        logger.info("全局配置已通过Hydra初始化。开始测试...")
        run_tests()



    hydra_test_runner()