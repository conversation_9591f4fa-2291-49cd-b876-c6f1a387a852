from typing import Dict, Any, List, Callable, Optional
from workflow.base.state import WorkflowState
from workflow.workflow_manager import WorkflowManager

# 导入所有步骤类
from workflow.step.rag_retriever.information_retriever import InformationRetrieverStep
from workflow.step.rag_retriever.column_retriever import ColumnRetrieverStep
from workflow.step.schema_linking.column_selector import ColumnSelectorStep
from workflow.step.schema_linking.schema_generator import SchemaGeneratorStep
from workflow.step.candidate_generation.sql_generator import SQLGeneratorStep
from workflow.step.candidate_generation.sql_refiner import SQLRefinerStep
from workflow.step.sql_selection.sql_selector import SQLSelectorStep
from workflow.step.sql_selection.sql_modifier import SQLModifierStep
from utils.common.logger_util import logger
from utils.common.time_utils import timer
from utils.common.config_util import config

class Text2SQLService:
    """Text2SQL服务，对外提供API接口"""
    
    def __init__(self):
        self.workflow_manager = WorkflowManager()
        self._init_workflow()
        self.logger = logger
        
    def _init_workflow(self):
        """初始化工作流组件"""
        # 注册步骤
        self.workflow_manager.register_step("information_retriever", InformationRetrieverStep)
        self.workflow_manager.register_step("column_retriever", ColumnRetrieverStep)
        self.workflow_manager.register_step("column_selector", ColumnSelectorStep)
        self.workflow_manager.register_step("schema_generator", SchemaGeneratorStep)
        self.workflow_manager.register_step("sql_generator", SQLGeneratorStep)
        self.workflow_manager.register_step("sql_refiner", SQLRefinerStep)
        self.workflow_manager.register_step("sql_selector", SQLSelectorStep)
        self.workflow_manager.register_step("sql_modifier", SQLModifierStep)
        
        # 从配置文件获取工作流配置
        workflow_config = config.workflow
        steps = workflow_config.get('steps', [])
        params = workflow_config.get('params', {})
        
        # 如果配置文件中没有步骤配置，使用默认工作流
        if not steps:
            steps = [
                "information_retriever",
                "column_retriever",
                "column_selector",
                "schema_generator",
                "sql_generator",
                "sql_refiner",
                "sql_selector",
                "sql_modifier"
            ]
        
        # 配置工作流
        self.workflow_manager.configure_workflow(steps, **params)
    
    
    async def process_query(self, 
                        user_question: str, 
                        callback: Optional[Callable] = None,
                        workflow_kwargs: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理用户查询
        
        Args:
            user_question: 用户问题
            callback: 回调函数，用于向前端发送中间结果
            custom_result_fields: 自定义返回字段，可以是固定值或以"final_state."开头的属性引用
            workflow_kwargs = {
                "": {
                    "model_name": "qwen_coder"
                },
                "column_selector": {
                    "model_name": "qwen_coder"  
                }
            }
            
        Returns:
            处理结果
        """
        # 创建初始状态
        with timer(logger=self.logger, message="Text2SQL服务处理时间"):
            # 合并基本参数和自定义参数
            state_params = {
                "user_question": user_question
            }
            # 添加额外的自定义参数
            if workflow_kwargs:
                state_params.update(workflow_kwargs)
            
            initial_state = WorkflowState(**state_params)
        
            # 执行工作流
            final_state = await self.workflow_manager.execute_workflow(
                initial_state=initial_state,
                result_callback=callback
            )
            
        # 格式化并返回结果
        return final_state
    
    def update_workflow_config(self, step_names: List[str]) -> None:
        """
        更新工作流配置
        
        Args:
            step_names: 新的步骤名称列表
        """
        self.workflow_manager.configure_workflow(step_names) 