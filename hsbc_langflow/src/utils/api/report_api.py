# src/utils/api/report_api.py
import json
from datetime import datetime
from fastapi import FastAPI, WebSocket, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn
from typing import List, Dict, Any, AsyncGenerator, Tuple
from utils.llm.history.dialogue import DialogProcessor
from utils.common.logger_util import logger
from modules.pg_database.pgvector.pgvector_class import PgVectorClass
from modules.pg_database.configs.config import pgvector_config, mysql_config
from modules.pg_database.pgvector.rag import hybrid_search
from utils.llm.generate_sql_ddl import generate_sql_for_table, sql_prompts, INDICATOR_PROMPT, DIMENSION_PROMPT
from utils.db.sse_report_utils import combined_stream
from utils.llm.llm_util import call_openai_sdk, async_call_openai_sdk,async_call_opentrek
from modules.pg_database.llm.llm_chat import get_embeddings
from utils.db.vdb_util import get_default_client
from modules.pg_database.mysql.mysql_memory import MySQLClientExtended
from utils.common.config_util import config
from utils.db.get_partitionkey import get_or_create_partition_key


model_config = config.qwen_vllm

# VLLM 配置
VLLM_CFG = {
    "key": model_config.get('key'),
    "url": model_config.get('url'),
    "model": model_config.get('name'),
    "max_tokens": model_config.get('max_tokens')
}


class SSERequest(BaseModel):
    dialogue: Dict[str, Any] = {}
    report: dict
    stream_mode: bool = True


async def process_report_data(request: SSERequest, pgvector_client: PgVectorClass,
                              mysql_client,vllm_config_base: dict = VLLM_CFG,) -> Tuple[List[str], Any, List[Dict[str, Any]]]:
    """
    处理报告请求的核心逻辑：提取指标、查询列、生成DDL和SQL。

    Args:
        request: 包含对话历史和流模式标志的请求对象。
        vllm_config_base: 基础 VLLM 配置字典。
        pgvector_client: PgVectorClass 客户端实例。

    Returns:
        一个包含指标列表、LLM SQL生成结果和最终列信息的元组。
    """
    start_time = datetime.now()
    logger.info(f"收到请求：{request.dict()}")

    # 初始化对话处理器
    dialog_processor = DialogProcessor(history_rounds=3)
    chat_history = request.dialogue.get("rounds", [])
    logger.info(f"用户提问：{chat_history}")

    # 处理用户问题
    user_question = dialog_processor.process_dialog_from_frontend(chat_history)

    # 1. 提取指标列表
    prompt = INDICATOR_PROMPT.replace("{{history_questions}}", user_question)
    messages = [{"role": "user", "content": prompt}]
    vllm_config_indicators = vllm_config_base.copy()
    vllm_config_indicators.update({
        "messages": messages,
        "stream": True, # 指标提取不需要流式处理
    })
    try:
        logger.info(f"调用大模型参数: {vllm_config_indicators}")
        result_indicators = await async_call_opentrek(**vllm_config_indicators)
        all_result = ''
        async for x in result_indicators:
            logger.info(f"大模型输出，返回类型: {x.choices[0].delta.content}")

            all_result += x.choices[0].delta.content

        logger.info(f"指标提取LLM调用成功，返回类型: {all_result}")
    except Exception as e:
        logger.error(f"指标提取LLM调用失败: {str(e)}", exc_info=True)
        raise

    # 提取条件维度：
    prompt = DIMENSION_PROMPT.replace("{{history_questions}}", user_question)
    messages = [{"role": "user", "content": prompt}]
    vllm_config_indicators = vllm_config_base.copy()
    vllm_config_indicators.update({
        "messages": messages,
        "stream": True,  # 指标提取不需要流式处理
    })
    try:
        logger.info(f"调用大模型参数: {vllm_config_indicators}")
        result_indicators = await async_call_opentrek(**vllm_config_indicators)
        dimension_all_result = ''
        async for x in result_indicators:
            logger.info(f"大模型输出，返回类型: {x.choices[0].delta.content}")

            dimension_all_result += x.choices[0].delta.content

        logger.info(f"指标提取LLM调用成功，返回类型: {all_result}")
    except Exception as e:
        logger.error(f"指标提取LLM调用失败: {str(e)}", exc_info=True)
        raise

    indicator_list = []
    try:
        # 注意：这里假设LLM的输出总是包含 <indicator_list> 标记
        indicator_content = all_result

        logger.debug(f"LLM原始输出内容: {indicator_content}")
        if "<indicator_list>" in indicator_content:
             indicator_list = json.loads(indicator_content.split("<indicator_list>")[1])
             logger.info(f"成功解析指标列表，包含 {len(indicator_list)} 个指标")
        else:
             logger.warning(f"LLM输出中未找到 <indicator_list> 标记: {indicator_content}")
             # 根据需要进行错误处理或设置默认值
    except (IndexError, json.JSONDecodeError, AttributeError) as e:
        logger.error(f"解析指标列表失败: {str(e)}, LLM原始输出: {getattr(result_indicators, 'choices', 'N/A')}", exc_info=True)
        # 考虑返回错误或使用空列表继续
    logger.info(f"指标列表：{indicator_list}")

    # 2. 获取所有的指标数据 - 使用MySQL查询
    mysql_get_indicator = {
        "op_type": "SELECT",
        "table_name": "model_info",
        "data_dict": {
            "logic": "AND",
            "conditions": [
                {"type": "=", "col_name": "LEFT(col_code, 5)", "col_val": "index"}
            ]
        }
    }
    try:
        mysql_indicator = mysql_client.batch_operation(mysql_get_indicator)[0]
        logger.info(f"数据库查询成功，返回 {len(mysql_indicator)} 条记录")
    except Exception as e:
        logger.error(f"数据库查询失败: {str(e)}", exc_info=True)
        raise
    
    logger.info(f"mysql_indicator {mysql_indicator}")
    #获取对应的维度信息

    dimens_lists = json.loads(dimension_all_result.split("<dimension_list>")[1].split("<dimension_list/>")[0])
    # 维度字段不做搜索，这是所有的维度content_id
    # dimension_content_id = [dimension['col_code'] for dimension in dimension_lists]
    # dimension_content_id = str(dimension_content_id).replace('[', '(').replace(']', ')')


    # 3. 搜索指标列
    all_indicators = []
    dimension_lists = []
    indicators = []
    try:
        # 将所有有指标表数据的模型作为使用
        partition_keys = request.report.get("models")
        partition_names = [get_or_create_partition_key('index'+partition_key, mysql_client) for partition_key in partition_keys]
        # 获取维度字段(除去INDEX_DATE)
        dimensi_names = [get_or_create_partition_key(partition_key,mysql_client) for partition_key in partition_keys]
        # 获取INDEX——DATE字段
        mysql_get_index_date = {
            "op_type": "SELECT",
            "table_name": "model_info",
            "data_dict": {
                "logic": "AND",
                "conditions": [
                    {"type": "=", "col_name": "col_type", "col_val": "INDEX_DATE"}
                ]
            }
        }
        mysql_index_date = mysql_client.batch_operation(mysql_get_index_date)[0]
    except Exception as e:
        logger.error(f"获取分区键失败: {str(e)}", exc_info=True)
        raise
        
    embedding_table_name = 'hsbc_embedding_data'
    for indicator in indicator_list:
        try:
            logger.debug(f"正在为指标 '{indicator}' 获取嵌入向量")
            embedding_result = get_embeddings(indicator)
            if not embedding_result or "data" not in embedding_result or not embedding_result["data"]:
                logger.warning(f"为指标 '{indicator}' 获取嵌入向量返回空结果")
                continue
                
            col_embedding = embedding_result["data"][0]['embedding']
            indicator_vec_dict = {
                "embedding": col_embedding
            }
            rank_type = {"type": "hybrid-weighted", "rank_rule": [{
                "embedding": 1.0
            }]}
            out_fields = ["content_id"]
            logger.debug(f"为指标 '{indicator}' 执行混合搜索，表名: {embedding_table_name}, 分区: {partition_names}")
            results = hybrid_search(
                pgvector_client=pgvector_client,
                table_name=embedding_table_name,
                vec_dict=indicator_vec_dict,
                rank_dict=rank_type,
                out_filed=out_fields,
                topk=3,
                expr="embedding_type = 'col_name_cn'",
                partition_name=partition_names,
                metric_type="cosine"
            )
            logger.info(f"指标 '{indicator}' 混合搜索成功，返回结果 {results}")
            indicators.extend(results)
        except Exception as e:
            logger.error(f"为指标 '{indicator}' 进行 hybrid_search 时出错: {str(e)}", exc_info=True)
            # 我们选择跳过失败的指标而不是中止整个处理

    #向量搜索出来最近的条件
    for dimen in dimens_lists:
        try:
            logger.debug(f"正在为条件 '{dimen}' 获取嵌入向量")
            embedding_result = get_embeddings(dimen)
            if not embedding_result or "data" not in embedding_result or not embedding_result["data"]:
                logger.warning(f"为条件 '{dimen}' 获取嵌入向量返回空结果")
                continue

            col_embedding = embedding_result["data"][0]['embedding']
            dimen_vec_dict = {
                "embedding": col_embedding
            }
            rank_type = {"type": "hybrid-weighted", "rank_rule": [{
                "embedding": 1.0
            }]}
            out_fields = ["content_id"]
            logger.debug(f"为条件 '{dimen}' 执行混合搜索，表名: {embedding_table_name}, 分区: {dimensi_names}")
            results = hybrid_search(
                pgvector_client=pgvector_client,
                table_name=embedding_table_name,
                vec_dict=dimen_vec_dict,
                rank_dict=rank_type,
                out_filed=out_fields,
                topk=3,
                expr="embedding_type = 'col_name_cn'",
                partition_name=dimensi_names,
                metric_type="cosine"
            )
            logger.info(f"条件 '{dimen}' 混合搜索成功，返回 {len(results)} 条结果")
            logger.info(f"条件 '{dimen}' 混合搜索成功，返回 {results} 结果")
            dimension_lists.extend(results)
        except Exception as e:
            logger.error(f"为条件 '{dimen}' 进行 hybrid_search 时出错: {str(e)}", exc_info=True)

    # 对指标数组进行去重与数据调整
    indicators = [indicator for indicator in indicators if indicator['distance'] < 0.20]
    indicators = list({(d['content_id']): d for d in indicators}.values())
    indicators = [indicator['content_id'] for indicator in indicators]
    # 对条件数组进行去重与数据调整
    dimension_lists = [dimension for dimension in dimension_lists if dimension['distance'] < 0.15]
    dimension_lists = list({(d['content_id']): d for d in dimension_lists}.values())
    dimension_lists = [indicator['content_id'] for indicator in dimension_lists]
    #当没有指标时，防止下面mysql查询出问题
    if not indicators:
        indicators=[" "]
    logger.info(f"过滤后的指标IDs: {indicators}，共 {len(indicators)} 个")

    if not dimension_lists:
        logger.warning("没有找到匹配的条件，将使用空列表继续处理")
        dimension_lists = [" "]
    logger.info(f"过滤后的条件IDs: {dimension_lists}，共 {len(dimension_lists)} 个")
    
    # 处理条件
    if not indicators:
        logger.warning("没有找到匹配的指标，将使用空列表继续处理")
    else:
        try:
            mysql_get_indicator = {
                "op_type": "SELECT",
                "table_name": "model_info",
                "data_dict": {
                    "logic": "AND",
                    "conditions": [
                        {"type": "in", "col_name": "col_code", "col_val": indicators}
                    ]
                }
            }
            indicators = mysql_client.batch_operation(mysql_get_indicator)[0]
            logger.info(f"成功获取指标详情，返回 {indicators} 条内容")
            # 当查询不到指标时，我们就要一个空列表
            if indicators == ():
                indicators = []
            logger.info(f"成功获取指标详情，返回 {len(indicators)} 条记录")
        except Exception as e:
            logger.error(f"获取指标详情失败: {str(e)}", exc_info=True)
            raise
    mysql_get_dimension = {
        "op_type": "SELECT",
        "table_name": "model_info",
        "data_dict": {
            "logic": "AND",
            "conditions": [
                {"type": "in", "col_name": "col_code", "col_val": dimension_lists}
            ]
        }
    }
    dimension_lists = mysql_client.batch_operation(mysql_get_dimension)[0]
    # 防止一些字段描述中文无法sql反解析
    dimension_lists = [{"col_code":dimens["col_code"],
                        "table_code":dimens["table_code"],
                        "col_name":dimens["col_name_cn"],
                       "col_name_cn": dimens["col_name"],
                        "col_desc": dimens["col_name_cn"],
                        "col_type": dimens["col_type"],
                        "create_at":dimens["create_at"],
                        "update_at": dimens["update_at"],
                        "col_data_example": dimens["col_data_example"]
                        }for dimens in dimension_lists]

    # 当查询不到指标时，我们就要一个空列表
    if dimension_lists == ():
        dimension_lists = []
    
    all_indicators.extend(dimension_lists)
    all_indicators.extend(mysql_index_date)
    all_indicators.extend(indicators)
    logger.info(f"最终指标列表(含维度)共{all_indicators}")
    logger.info(f"最终指标列表(含维度)共 {len(all_indicators)} 个")

    # 4. 生成 SQL DDL
    try:
        sql_ddl = generate_sql_for_table(all_indicators)
        logger.info(f"DDL生成成功，长度: {len(sql_ddl)}")
    except Exception as e:
        logger.error(f"生成SQL DDL失败: {str(e)}", exc_info=True)
        raise
        
    logger.debug(f"sql_ddl: {sql_ddl}")
    
    # 5. 生成 SQL 查询
    try:
        generate_sql_prompt = sql_prompts.replace("{now_time}", str(start_time)) \
                                         .replace("{user_query}", user_question) \
                                         .replace("{sql_ddl_state}", sql_ddl)
        generate_sql_messages = [{"role": "user", "content": generate_sql_prompt}]
        logger.debug(f"SQL生成提示长度: {len(generate_sql_prompt)}")
    except Exception as e:
        logger.error(f"构建SQL生成提示失败: {str(e)}", exc_info=True)
        raise
        
    vllm_config_sql = vllm_config_base.copy()
    vllm_config_sql.update({
        "messages": generate_sql_messages,
        "stream": request.stream_mode,
    })

    sql_result = None
    try:
        logger.info(f"开始LLM SQL生成，stream_mode: {request.stream_mode}")
        if request.stream_mode:
            sql_result = await async_call_opentrek(**vllm_config_sql)
        else:
            sql_result = await async_call_opentrek(**vllm_config_sql)
        logger.info(f"LLM SQL 生成成功，响应类型：{type(sql_result)}")
    except Exception as e:
        logger.error(f"调用 LLM 生成 SQL 时出错: {str(e)}", exc_info=True)
        # 这里我们选择不抛出异常，而是返回None，让调用方决定如何处理

    return indicator_list, sql_result, all_indicators