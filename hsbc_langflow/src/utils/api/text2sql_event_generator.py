import asyncio
import json
import time
from typing import Dict, Any, Callable, Optional
from utils.common.logger_util import logger
from utils.common.time_utils import timer
from utils.api.text2sql_api import Text2SQLService

def format_result(final_state, **kwargs):
    return {
        "modelId": kwargs.get("modelId", "1"),
        "name": "",  # 接口不生成
        "caliberBiz": final_state.biz_caliber,
        "caliberCal": final_state.technical_caliber,
        "indexType": kwargs.get("indexType", "atom"),
        "indexGrain": final_state.time_period,  # 默认日粒度 month year 
        "indexFormula": final_state.index_formula,
        "info": {  # 占不提供
            "index_tags": "系统标签",
            "index_scope": "全网通用"
        }
    }

async def create_event_generator(
    service: Text2SQLService,
    user_question: str,
    input_dict: Dict[str, Any],
    output_dict: Dict[str, Any],
    stream_mode: bool = True,
    api_start_time: float = None
):
    """
    创建事件生成器用于SSE流式输出
    
    参数:
        service: Text2SQL服务实例
        user_question: 处理后的用户问题
        input_dict: 输入参数字典，包含indexType等信息
        output_dict: 输出参数字典，用于格式化最终结果
        stream_mode: 是否使用流式(打字机)模式
        api_start_time: API处理开始时间
    
    返回:
        异步生成器，用于生成SSE事件
    """
    if api_start_time is None:
        api_start_time = time.perf_counter()
    
    # 创建队列存储步骤结果
    queue = asyncio.Queue()
    
    async def sse_callback(step_info):
        """将步骤结果放入队列"""
        await queue.put(step_info)
    
    # 启动处理任务但不等待它完成
    with timer(logger, "处理查询任务时间"):
        process_task = asyncio.create_task(
            service.process_query(
                user_question=user_question,
                callback=sse_callback,
                workflow_kwargs={
                    "index_type": input_dict.get("indexType"),
                    "model_id": input_dict.get("modelId")
                }
            )
        )
    
    try:
        # 持续从队列获取结果并发送
        while True:
            try:
                # 从队列获取结果，设置1秒超时
                step_info = await asyncio.wait_for(queue.get(), timeout=1.0)
                
                # 检查是否为流式内容
                is_stream = step_info.get("is_stream", False)
                
                if is_stream:
                    # 流式内容直接输出，不添加步骤标题，不使用打字机效果
                    yield f"data: {json.dumps({
                        'event': step_info['step_name'],
                        'content': step_info['display'],
                    },ensure_ascii=False)}\n\n"
                else:
                    # 常规步骤结果处理 (保持原有逻辑)
                    yield f"data: {json.dumps({
                        'event': step_info['step_name'],
                        'content': "#" + str(int(step_info['step_index']) + 1) + "." + step_info['step_name']
                    },ensure_ascii=False)}\n\n"
                    
                    if stream_mode:
                        # 打字机模式：逐字符/小块发送
                        # 获取要显示的文本
                        display_text = step_info["display"]
                        
                        # 每次发送少量字符
                        chunk_size = 3  # 每次发送3个字符
                        for i in range(0, len(display_text), chunk_size):
                            chunk = display_text[i:i+chunk_size]
                            yield f"data: {json.dumps({
                                'event': step_info['step_name'],
                                'content': chunk
                            },ensure_ascii=False)}\n\n"
                            await asyncio.sleep(0.02)  # 控制速度，模拟打字效果
                        
                        # # 发送步骤完成事件
                        # yield f"event: data\ndata: {json.dumps({
                        #     'event': step_info['step_name'],
                        #     'content': "Task Finished"
                        # })}\n\n"
                    else:
                        # 标准模式：发送完整步骤结果
                        yield f"data: {json.dumps({
                            'event': step_info['step_name'],
                            'content': step_info['display']
                        },ensure_ascii=False)}\n\n"
                
            except asyncio.TimeoutError:
                # 检查处理任务是否完成
                if process_task.done():
                    # 获取最终结果
                    try:
                        final_result = process_task.result()
                        # 计算总处理时间
                        api_total_time = time.perf_counter() - api_start_time
                        # 在最终结果中添加总处理时间
                        if isinstance(final_result, dict):
                            final_result["api_total_time"] = f"{api_total_time:.4f}秒"
                        logger.info(f"API总处理时间：{api_total_time:.4f}秒")
                        data_dict = {
                            "event":"stop",
                            "content":format_result(final_result,**output_dict),
                        }
                        yield f"data: {json.dumps(data_dict,ensure_ascii=False)}\n\n"
                    except Exception as e:
                        # 处理任务出错
                        yield f"data: {json.dumps({
                            'event': 'error',
                            'content': '无法在数据库中找到相关数据，请重新输入问题'
                        },ensure_ascii=False)}\n\n"
                    # 结束事件流
                    break
                
                # 发送保持连接的消息
                # yield f":keepalive\n\n"
                
    except asyncio.CancelledError:
        # 客户端断开连接
        if not process_task.done():
            process_task.cancel()
        # 即使取消也记录处理时间
        api_cancel_time = time.perf_counter() - api_start_time
        logger.info(f"API处理被取消，已处理时间：{api_cancel_time:.4f}秒")
        yield "data: {\"event\":\"error\",\"content\":\"API处理被取消\"}\n\n"
