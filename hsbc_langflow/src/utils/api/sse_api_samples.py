import json
from datetime import datetime
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn
from typing import Dict, Any
from utils.llm.history.dialogue import DialogProcessor
from utils.common.logger_util import logger
from utils.common.config_util import config

# 初始化 FastAPI 应用
app = FastAPI(
    title="Text2SQL API",
    description="将自然语言问题转换为SQL查询",
    version="1.0.0"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# VLLM 配置
VLLM_CFG = {
    "key": "EMPTY",
    "url": "http://**************:30167/v1",
    "model": "qwen_vllm",
    "max_tokens": 30000
}

from modules.pg_database.pgvector.pgvector_class import PgVectorClass
from modules.pg_database.configs.config import pgvector_config, mysql_config
from modules.pg_database.pgvector.rag import hybrid_search
from utils.llm.generate_sql_ddl import generate_sql_for_table, sql_prompts, INDICATOR_PROMPT
from utils.db.sse_report_utils import combined_stream
from utils.llm.llm_util import call_openai_sdk, async_call_openai_sdk
from modules.pg_database.llm.llm_chat import get_embeddings
from modules.pg_database.mysql.mysql_memory import MySQLClientExtended

pgvector_client = PgVectorClass(**config.pgvector)
mysql_client = MySQLClientExtended(**config.mysql)


# 请求参数
class SSERequest(BaseModel):
    dialogue: Dict[str, Any] = {}
    stream_mode: bool = True


@app.post("/sse/report")
async def sse_process(request: SSERequest):
    start_time = datetime.now()
    logger.info(f"收到请求：{request.dict()}")

    # 初始化对话处理器
    dialog_processor = DialogProcessor(history_rounds=3)
    chat_history = request.dialogue.get("rounds", [])
    logger.info(f"用户提问：{chat_history}")

    # 处理用户问题
    user_question = dialog_processor.process_dialog_from_frontend(chat_history)

    # 生成指标提取提示
    prompt = INDICATOR_PROMPT.replace("{{history_questions}}", user_question)
    messages = [{"role": "user", "content": prompt}]

    # 更新 VLLM 配置
    vllm_config = VLLM_CFG.copy()
    vllm_config.update({
        "messages": messages,
        "stream": False,
    })

    # 调用 LLM 获取指标列表
    result = call_openai_sdk(**vllm_config)
    indicator_list = json.loads(result.choices[0].message.content.split("<indicator_list>")[1])
    logger.info(f"指标列表：{indicator_list}")

    # 获取所有的指标数据
    mysql_get_indicator = {
        "op_type": "SELECT",
        "table_name": "model_info",
        "data_dict": {
            "logic": "AND",
            "conditions": [
                {"type": "=", "col_name": "table_code", "col_val": "${1001}"}
            ]
        }
    }
    mysql_indicator = mysql_client.batch_operation(mysql_get_indicator)[0]
    # print(mysql_indicator)
    # 获取对应的维度信息
    dimension_lists = [indicator for indicator in mysql_indicator if indicator['col_type'] in ['Dimension', 'DATE']]
    # 维度字段不做搜索，这是所有的维度content_id
    dimension_content_id = [dimension['col_code'] for dimension in dimension_lists]
    dimension_content_id = str(dimension_content_id).replace('[', '(').replace(']', ')')
    #
    # table_name = 'col_tables'
    # # 获取指标的维度字段
    # pgvector_client.set_table(table_name)
    # dimension_query = {
    #     "output_fields": ["id", "col_name", "col_type", "col_name_cn"],
    #     "expr": "col_type in ('Dimension','date') and table_id = '1001'",
    #     "partition_name": "9888"
    # }
    # dimension_lists = pgvector_client.query(dimension_query)
    indicators = []
    embedding_table_name = 'hsbc_embedding_data'
    for indicator in indicator_list:
        col_embedding = get_embeddings(indicator)["data"][0]['embedding']
        indicator_vec_dict = {
            "embedding": col_embedding
        }
        rank_type = {"type": "hybrid-weighted", "rank_rule": [{
            "embedding": 1.0
        }]}
        out_fields = ["content_id"]
        results = hybrid_search(
            pgvector_client=pgvector_client,
            table_name=embedding_table_name,
            vec_dict=indicator_vec_dict,
            rank_dict=rank_type,
            out_filed=out_fields,
            topk=3,
            expr=f"embedding_type = 'col_name_cn' and content_id not in {dimension_content_id}",
            partition_name="${1001}",
            metric_type="cosine"
        )
        indicators.extend(results)

    # 对指标数组进行去重与数据调整
    indicators = [indicator for indicator in indicators if indicator['distance'] < 0.15]
    indicators = list({(d['content_id']): d for d in indicators}.values())
    indicators = [indicator['content_id'] for indicator in indicators]
    # print(indicators)
    # 处理条件
    mysql_get_indicator = {
        "op_type": "SELECT",
        "table_name": "model_info",
        "data_dict": {
            "logic": "AND",
            "conditions": [
                {"type": "in", "col_name": "col_code", "col_val": indicators}
            ]
        }
    }
    indicators = mysql_client.batch_operation(mysql_get_indicator)[0]
    # print("mysql产生的指标", indicators)
    indicators.extend(dimension_lists)
    sql_ddl = generate_sql_for_table(indicators)

    # 构建 SQL 查询提示
    generate_sql_prompt = sql_prompts.replace("{now_time}", str(start_time)).replace("{user_query}",
                                                                                     user_question).replace(
        "{sql_ddl_state}", sql_ddl)
    generate_sql_messages = [{"role": "user", "content": generate_sql_prompt}]
    vllm_config.update({
        "messages": generate_sql_messages,
        "stream": request.stream_mode,  # 强制流式处理以符合 SSE
    })
    if request.stream_mode:
        result = await async_call_openai_sdk(**vllm_config)
    else:
        result = call_openai_sdk(**vllm_config)
    # logger.info(f"LLM 响应类型：{type(result)}")

    # 始终返回 SSE 流式响应
    return StreamingResponse(
        content=combined_stream(indicator_list, result, indicators),
        media_type="text/event-stream"
    )


# 运行服务
if __name__ == "__main__":
    uvicorn.run(
        "api:app",  # 假设文件名为 main.py
        host="0.0.0.0",
        port=9882
    )
