用户查询为：
{QUERY}
提示信息为：
{HINT}
请提供一个完整的结构化JSON输出，包含状态、时间信息、关键词列表、业务口径及报错的提示信息。
请注意：
1. 如果未提供计算金额类型，默认为"人民币本金"并使用"warning"状态；
2. 如果未提供时间信息，默认不考虑时间维度，使用"warning"状态；
3. 如果缺少行业类别、贷款期限，请使用"error"状态并在message中提示原因，优先级高于缺少计算金额类型
输出格式：
{{
  "status": "success|warning|error",
  "time": "提取的时间信息",
  "time_period": "提取的时间粒度",
  "keywords": ["行业类别", "期限类型", "计算金额类型"],
  "keywords_en": ["Business Type/Loan Purpose/IS_HIGH_TECH_INDUSTRY/DIGITAL_INDUSTRY_TYPE/IS_KNOWLEDGE_INDUSTRY", "Loan Term", "Currency Type"],
  "caliberBiz": "业务口径：查询【日期】【行业】【期限】【金额】",
  "message": "提示信息（如有）"
}}

