Objective: 分析用户的查询请求和提示信息，提取关键信息并以结构化方式输出，包括时间信息、行业类别、贷款期限类型和计算金额类型。

Instructions:

1. 时间提取：
   - 分析用户查询中提到的时间信息（如：上个月、本季度、今年以来等）
   - 如果用户未提供时间信息，默认不考虑时间维度
   - 提取时间粒度，以("day","month","quarter","year")为粒度，如果没有提供时间，默认时间粒度为"day"

2. 关键词信息提取
2.1 重点关注如下字段信息提取和整合：
   - 行业类别：例如"农、林、畜牧业"、"采矿业"、"制造业"等
   - 贷款期限类型：长期或短期, 英文为short_term, long_term
   - 计算金额类型：人民币、美元的本金和放贷金额等, 英文包括loan_bal_cny, loan_bal_usd, loan_amt_cny, loan_amt_usd

2.2 对于语序混乱的信息，如：
问题：请帮我查找农林畜牧业上个月人民币的贷款的本金是多少，哦对了需要短期的
提取信息整合思路：
行业类别：农、林、畜牧业
贷款期限类型：短期
计算金额类型：人民币本金
2.3 对于其他信息，以语义颗粒度适中的方式进行提取，需要各个关键词尽量独立。
2.4 仔细阅读提示信息，补充用户问题中未提供，但潜在的或者必要的信息，如业务逻辑，提取到keywords里。


3. 根据信息完整性判断状态：
   - 完整信息：返回"success"状态
   - 缺少计算金额类型：返回"warning"状态，默认为"人民币本金"
   - 缺少行业、期限或时间：返回"error"状态，并提示用户补充信息

4. 业务口径生成
- 根据提取的关键词信息，日期信息，进行总结，使得语义通达完整，生成提示信息

5. 结构化输出格式：
  ```json
  {{
    "status": "success|warning|error",
    "time": "提取的时间信息",
    "time_period": "提取的时间粒度",
    "keywords": ["行业类别", "期限类型", "计算金额类型","业务逻辑",...],
    "keywords_en": ["Business Type/Loan Purpose/IS_HIGH_TECH_INDUSTRY/DIGITAL_INDUSTRY_TYPE/IS_KNOWLEDGE_INDUSTRY", "Loan Term", "Currency Type","Business Logic",... ],
    "caliberBiz": "业务口径：查询【日期】【行业】【期限】【金额】",
    "message": "提示信息（如有）"
  }}
  ```

示例1:
用户查询: "请帮我查找上个月农林畜牧业的短期贷款的人民币本金是多少"
提示信息: "银保监会要求，贷款余额的计算必须基于银保监会要求的口径，即 `adm_lon_varoius_is_cbirc_loan = 'Y'`"
输出:
```json
{{
  "status": "success",
  "time": "上个月",
  "time_period": "month",
  "keywords": ["农、林、畜牧业", "短期", "人民币本金","是银保监会要求"],
  "keywords_en": ["Agriculture, Forestry and Farming", "short_term", "loan_bal_cny","is cbirc loan"],
  "caliberBiz": "查询【日期】上个月【行业】农、林、畜牧业【期限】短期【金额】人民币本金",
  "message": ""
}}
```

示例2:
用户查询: "查一下制造业的短期贷款金额"
提示信息: "银保监会要求，贷款余额的计算必须基于银保监会要求的口径，即 `adm_lon_varoius_is_cbirc_loan = 'Y'`"
输出:
```json
{{
  "status": "warning",
  "time": "",
  "time_period": "day",
  "keywords": ["制造业", "短期", "人民币本金","是银保监会要求"],
  "keywords_en": ["Manufacturing", "short_term", "loan_bal_cny","is cbirc loan"],
  "caliberBiz": "查询【行业】制造业【期限】短期【金额】人民币本金",
  "message": "用户未提供贷款期限类型和计算金额类型，默认为人民币本金.用户未提供时间信息，默认不考虑时间维度"
}}
```

示例3:
用户查询: "贷款情况怎么样"
提示信息: "银保监会要求，贷款余额的计算必须基于银保监会要求的口径，即 `adm_lon_varoius_is_cbirc_loan = 'Y'`"
输出:
```json
{{
  "status": "error",
  "time": "",
  "time_period": "day",
  "keywords": ["", "", "","是银保监会要求"],
  "keywords_en": ["", "", "",""],
  "caliberBiz": "",
  "message": "用户未提供行业类别、贷款期限类型、时间信息，请补充这些信息"
}}
```

Task:
分析以下用户查询，提取关键信息并以规定的JSON格式输出。

用户查询: {QUERY}

请注意：
1. 如果未提供计算金额类型，默认为"人民币本金"并使用"warning"状态；
2. 如果未提供时间信息，默认不考虑时间维度，使用"warning"状态；
3. 如果缺少行业类别、贷款期限，请使用"error"状态并在message中提示原因，优先级高于缺少计算金额类型