Objective: 分析用户的计算指标查询请求，识别涉及的指标名称和计算操作，并以结构化方式输出。

Instructions:

1. 指标识别：
   - 分析用户查询中明确提到的指标编号（例如：“124”、“124513”）。
   - 提取查询中涉及的所有指标编号。这些指标的具体定义存储在外部（如数据库），不在提示词中提供。

2. 操作识别：
   - 识别用户查询中指定的数学运算（例如：“+”、“-”、“*”、“/”、“求和”、“相减”、“乘以”、“除以”、“差值”、“总和”、“平均值”等）。
   - 如果用户未明确使用运算符，但意图清晰（如：“指标a和指标b一共是多少”），则推断为相应的运算（此例中为“+”）。
   - 考虑和识别SQL的5种聚合操作：sum, avg, max, min, count。

3. 状态判断：
   - 完整信息：如果成功识别了至少一个指标名称和明确的计算操作，则返回"success"状态。
   - 信息缺失：如果无法识别任何指标名称，或无法确定计算操作，则返回"error"状态，并在message中提示用户补充信息。

4. 业务口径生成：
   - 根据识别的指标名称和计算操作，生成描述计算任务的语句。例如：“计算【指标a】与【指标b】的和”、“计算【指标c】的数值” (如果只有一个指标，无明确运算)。

5. 结构化输出格式：
   - 输出的JSON结构必须与原子指标提取模板保持一致。
   - `time` 和 `time_period` 字段：对于计算指标查询，通常不直接处理时间信息（时间信息内嵌于原子指标定义中），因此默认值为空字符串 `""` 和 `"day"`。
   - `keywords` 字段：包含识别出的指标名称列表，最后附加识别出的操作符或操作描述。
   - `keywords_en` 字段：包含对应的英文或内部标识符（如果系统能映射），最后附加操作的英文标识（如 'add', 'subtract', 'multiply', 'divide', 'calculate'）。如果无法映射指标名称，可直接使用中文名。
   - `caliberBiz` 字段：包含生成的业务口径描述语句。
   - `message` 字段：包含提示信息，主要用于 `error` 状态或需要用户澄清的情况。

  ```json
  {{
    "status": "success|error",
    "time": "", // 计算指标查询通常默认为空
    "time_period": "day", // 计算指标查询通常默认为day
    "keywords": ["指标名1", "指标名2", ...],
    "keywords_en": ["indicator_name1", "indicator_name2", ...],
    "caliberBiz": "业务口径：计算【指标名1】与【指标名2】的【查询/计算操作/SQL聚合操作描述】",
    "message": "提示信息（如有）"
  }}
  ```

示例1:
用户查询: "a和b的总金额是多少"
输出:
```json
{{
  "status": "success",
  "time": "",
  "time_period": "day",
  "keywords": ["a", "b"],
  "keywords_en": ["a", "b"],
  "caliberBiz": "计算指标【a】与指标【b】的和【sum】",
  "message": ""
}}
```

示例2:
用户查询: "计算一下指标4123和指标1241的平均值"
输出:
```json
{{
  "status": "success",
  "time": "",
  "time_period": "day",
  "keywords": ["4123", "1241"],
  "keywords_en": ["4123", "1241"],
  "caliberBiz": "计算指标【4123】与指标【1241】的平均值【avg】",
  "message": ""
}}
```

示例3:
用户查询: "计算一下"
输出:
```json
{{
  "status": "error",
  "time": "",
  "time_period": "day",
  "keywords": [],
  "keywords_en": [],
  "caliberBiz": "",
  "message": "无法识别需要计算的指标或计算操作，请明确说明，例如 '计算指标a与指标b的和'"
}}
```

示例4:
用户查询: "指标1234是多少？" 
输出:
```json
{{
  "status": "success",
  "time": "",
  "time_period": "day",
  "keywords": ["1234"], // 使用'查询'或类似词表示获取单个指标值
  "keywords_en": ["1234"],
  "caliberBiz": "查询指标【1234】的值",
  "message": ""
}}
```

Task:
分析以下用户查询，识别指标名称和计算操作，并以规定的JSON格式输出。

用户查询: {QUERY}

请提供一个完整的结构化JSON输出。