Objective: 分析用户的查询请求，提取关键信息并以结构化方式输出，包括时间信息、行业类别、贷款期限类型和计算金额类型。

Instructions:

1. 时间提取：
   - 分析用户查询中提到的时间信息（如：上个月、本季度、今年以来等）
   - 提取时间信息，并判断时间粒度（如：day、month、year）

2. 字段信息提取：
   - 行业类别：例如"农、林、畜牧业"、"采矿业"、"制造业"等
   - 贷款期限类型：长期或短期, 英文为short_term, long_term
   - 计算金额类型：人民币、美元的本金和放贷金额等, 英文包括loan_bal_cny, loan_bal_usd, loan_amt_cny, loan_amt_usd

3. 根据信息完整性判断状态：
   - 完整信息：返回"success"状态
   - 缺少计算金额类型：返回"warning"状态，默认为"人民币本金"
   - 缺少行业、期限或时间：返回"error"状态，并提示用户补充信息

4. 结构化输出格式：
   ```json
   {{
     "status": "success|warning|error",
     "time": "提取的时间信息",
     "time_period": "提取的时间粒度",
     "columns": ["行业类别", "期限类型", "计算金额类型"],
     "columns_en": ["Business Type/Loan Purpose/IS_HIGH_TECH_INDUSTRY/DIGITAL_INDUSTRY_TYPE/IS_KNOWLEDGE_INDUSTRY", "Loan Term", "Currency Type"],
     "message": "提示信息（如有）"
   }}
   ```

示例1:
用户查询: "请帮我查找上个月农林畜牧业的短期贷款的人民币本金是多少"
输出:
```json
{{
  "status": "success",
  "time": "上个月",
  "columns": ["农、林、畜牧业", "短期", "人民币本金"],
  "columns_en": ["Agriculture, Forestry and Farming", "short_term", "loan_bal_cny"],
  "message": ""
}}
```

示例2:
用户查询: "查一下今年制造业的短期贷款金额"
输出:
```json
{{
  "status": "warning",
  "time": "今年",
  "columns": ["制造业", "短期", "人民币本金"],
  "columns_en": ["Manufacturing", "short_term", "loan_bal_cny"],
  "message": "用户未提供贷款期限类型和计算金额类型，默认为人民币本金"
}}
```

示例3:
用户查询: "贷款情况怎么样"
输出:
```json
{{
  "status": "error",
  "time": "",
  "columns": ["", "", ""],
  "columns_en": ["", "", ""],
  "message": "用户未提供行业类别、贷款期限类型、时间信息，请补充这些信息"
}}
```

Task:
分析以下用户查询，提取关键信息并以规定的JSON格式输出。

用户查询: {QUERY}

请提供一个完整的结构化JSON输出，包含状态、时间信息、字段列表和提示信息。
请注意：
1. 如果未提供计算金额类型，默认为"人民币本金"并使用"warning"状态
2. 如果缺少行业类别、贷款期限或时间信息，请使用"error"状态并在message中提示原因，优先级高于缺少计算金额类型