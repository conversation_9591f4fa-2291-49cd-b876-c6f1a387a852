'''
File Created: Wednesday, 28th May 2025 1:59:21 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 8:21:01 am
'''

import hydra
from omegaconf import DictConfig
import uvicorn
from utils.common.logger_util import configure_logger, logger
from utils.common.config_util import config, AppConfig
import sys

# 将app_path移到全局，因为它现在是静态的
app_path = "api.fastapi_app:app"

def start_service(host="0.0.0.0", port=31316, reload=False, **kwargs):
    """启动服务器并记录日志 (现在只负责启动)"""
    try:
        logger.info("="*50)
        logger.info("正在启动Text2SQL API服务...")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"服务器配置:")
        logger.info(f"- 监听地址: {host}")
        logger.info(f"- 端口: {port}")
        logger.info(f"- 热重载: {'启用' if reload else '禁用'}")
        logger.info("="*50)

        uvicorn.run(
            app=app_path,
            host=host,
            port=port,
            reload=reload,
            **kwargs # 传递其他uvicorn参数
        )
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        raise e
    finally:
        logger.info("服务已停止")

@hydra.main(config_path="config", config_name="config", version_base=None)
def launch(cfg: AppConfig):
    """
    应用程序的Hydra入口点。
    负责初始化配置并启动服务。
    """
    # 1. 初始化日志系统 (在所有其他操作之前)
    # 使用从 hydra 加载的配置来设置日志
    configure_logger(**cfg.logging)
    # 将 sys.excepthook 指向 logger.catch 来捕获所有未处理的异常
    sys.excepthook = logger.catch
    logger.info("日志系统配置完成。")

    # 2. 初始化全局config单例
    config.__init__(cfg)
    logger.info("全局配置已通过Hydra和结构化配置(Dataclass)初始化。")
    logger.info(f"主RDB主机: {config.primary_rdb.connection.host}")

    # 3. 从配置中获取服务参数
    server_cfg = config.api_server
    
    # 4. 调用服务启动函数
    start_service(**server_cfg.__dict__)

if __name__ == "__main__":
    launch()