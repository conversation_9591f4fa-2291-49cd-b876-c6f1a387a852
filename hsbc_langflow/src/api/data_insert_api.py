import sys
import os
new_directory = "/data/ideal/code/gsh_code/text2SQL/hsbc/src"
sys.path.append(new_directory)
print(sys.path)
import uvicorn
from fastapi import FastAPI
from modules.pg_database.configs.config import pgvector_config,mysql_config
from modules.pg_database.pgvector.pgvector_class import PgVectorClass
from modules.pg_database.mysql.mysql_memory import MySQLClientExtended
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from datetime import datetime
from utils.llm.model_client import get_embeddings
import hashlib
import argparse
from contextlib import asynccontextmanager
from utils.common.config_util import config
from utils.db.get_partitionkey import get_or_create_partition_key

global pgvector_client
global mysql_client


@asynccontextmanager
async def lifespan(app: FastAPI):
    global pgvector_client, mysql_client
    pgvector_client = PgVectorClass(**pgvector_config)
    print("PgVectorClient initialized")
    mysql_client = MySQLClientExtended(**mysql_config)
    print("MySQLClient initialized")
    yield
    #mysql_client.close()
    print('Closing connections...')


app = FastAPI(
    title="data_insert API",
    description="知识库数据插入API接口文档",
    version="1.0.0",
    lifespan=lifespan
)


class IndicatorInsertRequest(BaseModel):
    col_code: str
    table_code: str
    col_name: str
    col_name_cn: str
    col_desc: str
    col_type: str  # 应该限制为特定枚举值: NUMBER,STRING,DATE,INDEX_DATE
    create_at: datetime = None
    update_at: datetime = None
    embedding_type: Optional[List[str]] = None  # 修改为 Optional[List[str]]
    col_data_example: Optional[str] = None


class dataResponse(BaseModel):
    status: str
    error_messages: Optional[str] = None


@app.post("/indicator_insert", response_model=dataResponse)
async def indicator_insert(request: IndicatorInsertRequest):
    try:
        # 设置对应的向量表
        pgvector_client.set_table('hsbc_embedding_data')

        model_id = request.table_code.split('.')[0]
        if request.col_code[:5] == "index":
            model_id = 'index' + model_id
        # 获取或创建固定 63 字节的 partition_key
        partition_key = get_or_create_partition_key(model_id, mysql_client)

        # MySQL 删除已有数据以防重复
        mysql_delete_data = {
            "op_type": "DELETE",
            "table_name": "model_info",
            "data_dict": [{"col_code": request.col_code}]
        }
        mysql_client.batch_operation(mysql_delete_data)

        # 向量库删除已有数据
        pgvector_client.delete(expr=f"content_id = '{request.col_code}'", partition_name=partition_key)

        # 1. 插入 MySQL
        mysql_insert_data = {
            "op_type": "INSERT",
            "table_name": "model_info",
            "data_dict": [{
                "col_code": request.col_code,
                "table_code": request.table_code,
                "col_name": request.col_name,
                "col_name_cn": request.col_name_cn,
                "col_desc": request.col_desc,
                "col_type": request.col_type,
                "col_data_example": request.col_data_example
            }]
        }
        message = mysql_client.batch_operation(mysql_insert_data)
        if message != 'INSERT SUCCESS 1 data':
            raise Exception("MySQL 插入失败")
        # print(message)
        # 2. 准备向量库数据
        pgvector_data = [
            {
                "embedding": get_embeddings(request.col_name)["data"][0]['embedding'],
                "embedding_type": "col_name",
                "content_id": request.col_code
            },
            {
                "embedding": get_embeddings(request.col_name_cn)["data"][0]['embedding'],
                "embedding_type": "col_name_cn",
                "content_id": request.col_code
            },
            {
                "embedding": get_embeddings(request.col_desc)["data"][0]['embedding'],
                "embedding_type": "col_desc",
                "content_id": request.col_code
            }
        ]

        # 根据 embedding_type 过滤数据
        if request.embedding_type is not None:
            pgvector_data = [
                data for data in pgvector_data
                if data["embedding_type"] in request.embedding_type
            ]
        # print('到这里了')
        # 插入向量库
        if pgvector_data:
            if not pgvector_client.insert(pgvector_data, partition_name=partition_key):
                raise Exception("向量库插入失败")

        return dataResponse(
            status="success",
            error_messages=None
        )

    except Exception as e:
        return dataResponse(
            status="error",
            error_messages=str(e)
        )


class ColWhereInsertRequest(BaseModel):
    col_code: str
    col_where_val: str
    col_where_desc: str


@app.post("/col_where_insert", response_model=dataResponse)
async def col_where_insert(request: ColWhereInsertRequest):
    try:
        # 设置对应的向量表
        pgvector_client.set_table('hsbc_embedding_data')

        # 获取或创建固定 63 字节的 partition_key
        partition_key = get_or_create_partition_key(request.col_code, mysql_client)

        # 计算 col_where_val 的 MD5 值
        col_where_val_md5 = hashlib.md5(request.col_where_val.encode()).hexdigest()
        content_id = f"{request.col_code}#{col_where_val_md5}"

        # MySQL 删除已有数据以防重复
        mysql_delete_data = {
            "op_type": "DELETE",
            "table_name": "where_info",
            "data_dict": [{"where_id": content_id}]
        }
        mysql_client.batch_operation(mysql_delete_data)

        # 向量库删除已有数据
        pgvector_client.delete(expr=f"content_id = '{content_id}'", partition_name=partition_key)

        # 1. 插入 MySQL
        mysql_insert_data = {
            "op_type": "INSERT",
            "table_name": "where_info",
            "data_dict": [{
                "where_value": request.col_where_val,
                "where_id": content_id
            }]
        }
        message = mysql_client.batch_operation(mysql_insert_data)
        if message != 'INSERT SUCCESS 1 data':
            raise Exception("MySQL 插入失败")

        # 2. 准备向量库数据
        pgvector_data = [
            {
                "embedding": get_embeddings(request.col_where_val)["data"][0]['embedding'],
                "embedding_type": "",
                "content_id": content_id
            },
            {
                "embedding": get_embeddings(request.col_where_desc)["data"][0]['embedding'],
                "embedding_type": "",
                "content_id": content_id
            }
        ]

        # 插入向量库
        if not pgvector_client.insert(pgvector_data, partition_name=partition_key):
            raise Exception("向量库插入失败")

        return dataResponse(
            status="success",
            error_messages=None
        )

    except Exception as e:
        return dataResponse(
            status="error",
            error_messages=str(e)
        )


class IndicatorDelRequest(BaseModel):
    col_code: str


@app.post("/indicator_del", response_model=dataResponse)
async def indicator_del(request: IndicatorDelRequest):
    try:
        # 设置对应的向量表
        pgvector_client.set_table('hsbc_embedding_data')

        # 从 MySQL 查询 table_code
        query_data = {
            "op_type": "SELECT",
            "table_name": "model_info",
            "data_dict": {
                "logic": "AND",
                "conditions": [
                    {"type": "=", "col_name": "col_code", "col_val": request.col_code}
                ]
            }
        }
        result = mysql_client.batch_operation(query_data)[0]
        if not result or not isinstance(result, list) or len(result) == 0:
            raise Exception("未找到对应的 table_code")

        table_code = result[0].get("table_code")
        model_id = table_code.split('.')[0]
        if request.col_code[:5] == "index":
            model_id = 'index' + model_id
            # 获取或创建固定 63 字节的 partition_key
        partition_key = get_or_create_partition_key(model_id, mysql_client)
        if not table_code:
            raise Exception("table_code 为空")

        # 1. 删除向量库数据
        pgvector_client.delete(expr=f"content_id = '{request.col_code}'", partition_name=partition_key)

        # 2. 删除 MySQL 数据
        mysql_delete_data = {
            "op_type": "DELETE",
            "table_name": "model_info",
            "data_dict": [{"col_code": request.col_code}]
        }
        message = mysql_client.batch_operation(mysql_delete_data)
        if message != 'SUCCESS DELETE  1 data':
            raise Exception("MySQL 删除失败")

        return dataResponse(
            status="success",
            error_messages=None
        )

    except Exception as e:
        return dataResponse(
            status="error",
            error_messages=str(e)
        )


class ColWhereDelRequest(BaseModel):
    col_where_key: str


@app.post("/col_where_del", response_model=dataResponse)
async def col_where_del(request: ColWhereDelRequest):
    try:
        # 设置对应的向量表
        pgvector_client.set_table('hsbc_embedding_data')

        # 提取 col_code
        try:
            col_code = request.col_where_key.split('#')
            col_code.pop()
            col_code = '#'.join(col_code)
            # print(col_code)
        except IndexError:
            raise Exception("col_where_key 格式错误，无法解析 col_code")

        # 查询 partition_key
        partition_key = get_or_create_partition_key(col_code, mysql_client)
        # print('partition_key', partition_key)
        # 1. 删除向量库数据
        pgvector_client.delete(expr=f"content_id = '{request.col_where_key}'", partition_name=partition_key)

        # 2. 删除 MySQL 数据
        mysql_delete_data = {
            "op_type": "DELETE",
            "table_name": "where_info",
            "data_dict": [{"where_id": request.col_where_key}]
        }
        message = mysql_client.batch_operation(mysql_delete_data)
        if message != 'SUCCESS DELETE  1 data':
            raise Exception("MySQL 删除失败")

        return dataResponse(
            status="success",
            error_messages=None
        )

    except Exception as e:
        return dataResponse(
            status="error",
            error_messages=str(e)
        )


def run_api(host, port):
    uvicorn.run("data_insert_api:app", host=host, port=port)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='ideal-kb_chat')
    parser.add_argument("--host", type=str, default="0.0.0.0")
    parser.add_argument("--port", type=int, default=31318)
    args = parser.parse_args()
    run_api(host=args.host, port=args.port)
