'''
File Created: Monday, 19th May 2025 3:09:14 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 8:07:56 am
'''

from fastapi import FastAPI, WebSocket, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import asyncio
import json
from typing import Dict, Any, List, Optional
from api.service_factory import ServiceFactory
from fastapi.responses import StreamingResponse
from datetime import datetime
from utils.llm.history.dialogue import DialogProcessor
from utils.common.logger_util import logger,configure_logger
from utils.common.time_utils import timer
import time
from fastapi import HTTPException
from utils.api.text2sql_event_generator import create_event_generator
from utils.api.report_api import process_report_data
from utils.db.sse_report_utils import combined_stream
from utils.common.config_util import config
from utils.db.vdb_util import PgVectorClass
from modules.pg_database.mysql.mysql_memory import MySQLClientExtended
from contextlib import asynccontextmanager
from utils.db.common.client_util import pgvector_client, mysql_client
from utils.api.text2sql_event_generator import format_schema_result
import uuid # 导入uuid库

# 数据库客户端初始化
pgvector_client = pgvector_client
mysql_client = mysql_client

    
app = FastAPI(title="Text2SQL API", 
            description="将自然语言问题转换为SQL查询")

# --- 日志上下文中间件 ---
@app.middleware("http")
async def log_context_middleware(request: Request, call_next):
    """
    这个中间件为每个请求注入日志上下文。
    它会根据请求的URL路径，自动为日志添加 'service' 和 'request_id' 标记。
    """
    request_id = str(uuid.uuid4())
    path = request.url.path

    # 根据 URL 路径精细化分配服务名称
    # 确保这里的名称与配置文件中的sinks键匹配
    if "/sse/report" in path:
        service_name = "report"
    elif "/sse/process" in path:
        service_name = "process"
    elif "/sse/schema" in path:
        service_name = "schema"
    else:
        # 其他所有请求（如 /docs, /openapi.json）归类为 'main'
        service_name = "main"

    # 使用 contextualize 为这个请求的处理过程打上标记
    # 在这个 with 块内（以及它调用的所有函数里），
    # logger 的 "extra" 字典中都会自动包含 service 和 request_id
    with logger.contextualize(service=service_name, request_id=request_id):
        logger.info(f"Request started: {request.method} {request.url.path}")
        
        response = await call_next(request)
        
        logger.info(f"Request finished: status_code={response.status_code}")
        return response
# --- 中间件结束 ---

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求参数
class SSERequest(BaseModel):
    dialogue: Dict[str, Any] = {}
    report: dict
    stream_mode: bool = True

class QueryRequest(BaseModel):
    question: str
    hint: Optional[str] = ""

class WorkflowConfig(BaseModel):
    steps: List[str]

@app.post("/sse/process")
async def sse_process(request: Request, stream_mode: bool = True):
    # 记录API开始时间
    api_start_time = time.perf_counter()
    # 获取请求数据
    requests = await request.json()
    logger.info(f"收到请求：{requests}")
    
    # 从请求中提取自定义步骤配置
    custom_steps = requests.get("workflow_steps", None)
    
    # 创建Text2SQL服务实例
    service = ServiceFactory.create_text2sql_service()
    
    # 如果提供了自定义步骤，更新工作流配置
    if custom_steps:
        logger.info(f"使用自定义工作流步骤: {custom_steps}")
        service.update_workflow_config(custom_steps)
    else:
        # 从全局配置中获取'process'工作流的步骤
        process_steps = config.workflows.process.steps
        logger.info(f"使用配置的process工作流步骤: {process_steps}")
        service.update_workflow_config(process_steps)
    
    dialog_processor = DialogProcessor(history_rounds=3)
    chat_history = requests.get("dialogue", {}).get("rounds", [])
    logger.info(f"用户提问：{chat_history}")
    user_question = dialog_processor.process_dialog_from_frontend(chat_history)
    logger.info(f"处理后的用户提问：{user_question}")
    hint=chat_history[-1].get("hint", "")
    logger.info(f"hint: {hint}")
    
    # 准备输入输出参数
    input_dict = requests.get("index", {})
    output_dict = {
        "modelId": input_dict.get("modelId"), 
        "indexType": input_dict.get("indexType"),
    }
    
    input_dict["hint"] = hint
    # 返回流式响应
    return StreamingResponse(
        create_event_generator(
            service=service,
            user_question=user_question,
            input_dict=input_dict,
            output_dict=output_dict,
            stream_mode=stream_mode,
            api_start_time=api_start_time
        ),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )
    
@app.post("/sse/report")
async def sse_report(request: SSERequest):
    """
    处理 SSE 请求，调用核心逻辑并返回流式响应。
    """
    try:
        # 调用核心处理函数
        indicator_list, llm_result, indicators = await process_report_data(request,pgvector_client,mysql_client)

        # 检查 llm_result 是否有效（例如，在发生错误时可能为 None）
        if llm_result is None:
            # 可以选择返回一个错误流或标准的HTTP错误响应
            # 这里我们仍然尝试流式传输，但可能只包含错误信息
            async def error_stream():
                yield json.dumps({"error": "Failed to generate SQL."}) + "\n\n"
            return StreamingResponse(content=error_stream(), media_type="text/event-stream", status_code=500)


        # 创建并返回 SSE 流式响应
        # 确保 combined_stream 能正确处理可能的 None 或错误状态的 llm_result
        return StreamingResponse(
            content=combined_stream(indicator_list, llm_result, indicators),
            media_type="text/event-stream"
        )
    except Exception as e:
        logger.error(f"处理 /sse/report 请求时发生未捕获异常: {e}", exc_info=True)
        # 返回一个通用的错误流或HTTP错误
        async def critical_error_stream():
            yield json.dumps({"error": "An internal server error occurred."}) + "\n\n"
        return StreamingResponse(content=critical_error_stream(), media_type="text/event-stream", status_code=500)

@app.post("/sse/schema")
async def sse_schema(request: Request, stream_mode: bool = True):
    """
    提供数据库模式和列选择的API端点
    处理用户问题并执行到schema_generator步骤，输出所选列和数据库模式信息
    """
    # 记录API开始时间
    api_start_time = time.perf_counter()
    # 获取请求数据
    requests = await request.json()
    logger.info(f"收到schema请求：{requests}")
    
    # 创建Text2SQL服务实例
    service = ServiceFactory.create_text2sql_service()
    
    # 从请求中提取自定义步骤配置
    custom_steps = requests.get("workflow_steps", None)
    
    if custom_steps:
        logger.info(f"使用自定义工作流步骤: {custom_steps}")
        service.update_workflow_config(custom_steps)
    else:
        # 从全局配置中获取 'schema' 工作流的步骤
        schema_steps = config.workflows.schema.steps
        logger.info(f"使用配置的schema工作流步骤: {schema_steps}")
        service.update_workflow_config(schema_steps)
    
    dialog_processor = DialogProcessor(history_rounds=3)
    chat_history = requests.get("dialogue", {}).get("rounds", [])
    logger.info(f"用户提问：{chat_history}")
    user_question = dialog_processor.process_dialog_from_frontend(chat_history)
    logger.info(f"处理后的用户提问：{user_question}")
    
    # 准备输入输出参数
    input_dict = requests.get("index", {})
    output_dict = {
        "modelId": input_dict.get("modelId"), 
        "indexType": input_dict.get("indexType"),
    }
    
    # 返回流式响应，使用自定义的结果格式化函数
    return StreamingResponse(
        create_event_generator(
            service=service,
            user_question=user_question,
            input_dict=input_dict,
            output_dict=output_dict,
            stream_mode=stream_mode,
            api_start_time=api_start_time,
            format_result_func=format_schema_result  # 使用自定义格式化函数
        ),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

# @app.get("/model/{model_id}/info", 
#          response_model=List[Dict[str, Any]], 
#          summary="获取指定模型ID的全量信息",
#          description="根据提供的 model_id，查询并返回该模型在数据库中记录的所有字段详细信息，如字段代码、名称、描述、类型等。")
# async def get_model_information(model_id: str):
#     """
#     根据模型ID获取其在数据库中的全量元数据信息。

#     Args:
#         model_id: 要查询的模型ID (从路径参数获取)

#     Returns:
#         包含该模型所有字段信息的字典列表。

#     Raises:
#         HTTPException(404): 如果根据提供的 model_id 未找到任何信息。
#         HTTPException(500): 如果在查询过程中发生内部错误。
#     """
#     try:
#         logger.info(f"收到获取模型信息的请求, model_id: {model_id}")
#         model_info = get_model_info_by_id(model_id) # 调用数据库查询函数
        
#         if not model_info:
#             logger.warning(f"未找到 model_id: {model_id} 的模型信息")
#             raise HTTPException(status_code=404, detail=f"未找到 Model ID 为 '{model_id}' 的信息")
            
#         logger.info(f"成功获取 model_id: {model_id} 的信息，共 {len(model_info)} 条字段记录")
#         return model_info
#     except HTTPException as http_exc:
#         # 重新抛出已知的 HTTP 异常
#         raise http_exc
#     except Exception as e:
#         # 捕捉其他可能的错误（例如数据库连接问题）
#         logger.error(f"获取 model_id: {model_id} 信息时发生内部错误: {e}", exc_info=True)
#         raise HTTPException(status_code=500, detail=f"获取模型信息时发生内部错误: {str(e)}")

