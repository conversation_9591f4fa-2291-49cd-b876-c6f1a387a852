from workflow.base.base_tool_step import Tool
from workflow.base.state import WorkflowState
from typing import Dict, Any, List, Union, Optional
from utils.db.vdb_util import parse_sql

class SQLModifierStep(Tool):
    def __init__(self):
        super().__init__(
            name="sql_modifier",
            description="对sql语句进行修正,输出修正where后以及转化格式后的sql语句",
        )
    
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        预处理步骤
        """
        return state
    
    async def use_tool(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        使用工具
        """
        query = state.selected_sql if state.selected_sql else state.sql_candidates[0]
        try:
            # 使用parse_sql函数处理SQL，它返回四个值
            flat_sql, technical_caliber, index_formula, modified_sql = parse_sql(query,state.pgvector_client,state.mysql_client,state.model_id)
            # 根据index_type设置formulaType
            # atom--model, computer--index
            index_formula["formulaType"] = state.index_type
            return {
                "flat_sql": flat_sql,
                "technical_caliber": technical_caliber,
                "index_formula": index_formula,
                "modified_sql": modified_sql
            }
        except Exception as e:
            self.logger.error(f"sql_modifier error: {e}")
            return None
        
    async def parse_results(self, raw_results: Any, state: WorkflowState) -> WorkflowState:
        """
        解析结果
        """
        step_result = state.step_results[self.name]
        state.results = raw_results
        if isinstance(raw_results, dict) and raw_results is not None:
            self.logger.success(f"sql_modifier success: {raw_results}")
            step_result.raw_response = raw_results
            step_result.parsed_result = {
                "status":"success",
                "data":{
                    "flat_sql": raw_results["flat_sql"],
                    "technical_caliber": raw_results["technical_caliber"],
                    "index_formula": raw_results["index_formula"],
                    "modified_sql": raw_results["modified_sql"]
                },
                "error":None
            }
        else:
            self.logger.error(f"sql_modifier error: {raw_results}")
            step_result.parsed_result = {
                "status":"error",
                "data":None,
                "error":raw_results
            }
        return state
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        """
        if step_outputs.parsed_result["status"] == "success":
            data = step_outputs.parsed_result["data"]
            flat_sql = data["flat_sql"]
            technical_caliber = data["technical_caliber"]
            index_formula = data["index_formula"]
            modified_sql = data["modified_sql"]
            self.logger.info(f"index_formula: {index_formula}")
            return {
                "flat_sql": flat_sql,
                "technical_caliber": technical_caliber,
                "index_formula": index_formula,
                "modified_sql": modified_sql
            }
        return {}
        
    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        显示 SQL 修正结果，包括修正后的 SQL、技术口径等
        
        Args:
            results: 包含修正信息的字典
            
        Returns:
            格式化后的字符串
        """
        if not results or not isinstance(results, dict):
             # 兼容旧的直接返回 SQL 字符串的格式
             if isinstance(results, str) and results:
                 display_parts = ["## SQL 修正结果：\n"]
                 display_parts.append("### 原始/修正 SQL:\n```sql")
                 display_parts.append(results)
                 display_parts.append("```")
                 return "\n".join(display_parts)
             return "未获得有效的 SQL 修正结果或结果格式不正确。"
        
        display_parts = ["## SQL 修正结果：\n"]
        has_content = False

        modified_sql = results.get("modified_sql")
        if modified_sql:
            display_parts.append("### 修正后的 SQL:\n```sql")
            display_parts.append(modified_sql)
            display_parts.append("```")
            has_content = True

        technical_caliber = results.get("technical_caliber")
        if technical_caliber:
            display_parts.append("### 技术口径:")
            display_parts.append(f"> {technical_caliber}") # 使用引用块
            has_content = True
        
        # index_formula = results.get("index_formula")
        # if index_formula:
        #     display_parts.append("### 指标公式:")
        #     display_parts.append(f"> {index_formula}") # 使用引用块
        #     has_content = True
            
        flat_sql = results.get("flat_sql")
        if flat_sql:
            display_parts.append("### 扁平化 SQL (内部使用):\n```sql")
            display_parts.append(flat_sql)
            display_parts.append("```")
            has_content = True
            
        if not has_content:
             display_parts.append("- 未找到有效的修正信息。")

        return "\n".join(display_parts)
        
        
