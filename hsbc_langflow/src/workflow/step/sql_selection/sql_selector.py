'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 7:14:21 am
'''

from typing import Dict, Any, List, Union, Optional
from workflow.base.base_llm_step import Agent
from workflow.base.state import WorkflowState
from copy import deepcopy
from utils.llm.processor.prompts import get_prompt

class SQLSelectorStep(Agent):
    """
    选择最优SQL查询的步骤
    """
    
    def __init__(self, 
                model_name: str = 'qwen_vllm', 
                num_calls: int = 1,
                max_retry_calls: int = 1,
                parser_name: str = "generic_json",
                is_callback: bool = True):
        super().__init__(
            name="sql_selector",
            description="选择最优SQL查询",
            num_calls=num_calls,
            max_retry_calls=max_retry_calls,
            model_name=model_name,
            is_callback=is_callback
        )
        self.parser_name = parser_name
        
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        构建提示词以生成SQL查询
        
        Args:
            state: 工作流状态
            
        Returns:
            更新后的工作流状态
        """
        # 设置提示模板名称
        state.cache["template_name"] = "candidate_selector"
        
        # 调用父类的preprocess方法完成通用的预处理逻辑
        return await super().preprocess(state)

    def get_prompt_variables(self, state: WorkflowState) -> Dict[str, Any]:
        """
        提供格式化提示所需的变量
        
        Args:
            state: 工作流状态
            
        Returns:
            变量字典
        """
        def format_execution_results(execution_results):
            """
            将执行结果字典转换为易读的文本格式
            
            Args:
                execution_results: 包含SQL执行结果的字典
                
            Returns:
                格式化后的文本
            """
            formatted_results = ""
    
            for key, result in execution_results.items():
                formatted_results += f"Candidate {key}\n"
                formatted_results += "【SQL】\n"
                formatted_results += f"{result['sql']}\n"
                formatted_results += "【执行结果】\n"
                
                if result.get("error"):
                    formatted_results += f"Error: {result['error']}\n"
                
                if result.get("result"):
                    formatted_results += f"Result: {result['result']}\n"
                
                formatted_results += "\n"
            
            return formatted_results.strip()
        
        return {
            "system":{
                "CANDIDATE_NUM": len(state.refined_execution_results),
                "DATABASE_TYPE": state.db_type,
                "QUESTION": state.user_question,
                "DATABASE_SCHEMA": state.db_schema,
                "HINT": state.hint,
            },
            "user":{
                "CANDIDATE_SQL_LIST": format_execution_results(state.refined_execution_results)
            }
        }
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        
        Args:
            state: 工作流状态对象
            step_outputs: 解析后的结果
            
        Returns:
            需要更新的键值对字典
        """
        sql_result = deepcopy(step_outputs.parsed_result['data'])
        try:
            sql_result.pop('chain_of_thought', None)
            self.logger.success(f"get_update_values: 筛选出的列: {sql_result}")
        except:
            self.logger.warning(f"get_update_values: 筛选出的列出问题: {sql_result}")
        selected_sql_id = sql_result.get("selected_sql", "")
        # TODO: 需要处理selected_sql_id为None的情况
        if selected_sql_id or selected_sql_id == "None":
            selected_sql = state.refined_execution_results[selected_sql_id]['sql']
            state.results['selected_sql'] = selected_sql
            return {"selected_sql": selected_sql}
        else:
            self.logger.warning(f"get_update_values: 未能选择有效的SQL查询")
            return {"selected_sql": None}
        
    
    def combine_results(self, results: List[Any]) -> Any:
        """
        合并多个解析结果为单一结果
        
        Args:
            results: 解析结果列表
        
        Returns:
            合并后的结果
        """
        # 解析list[str],str为json，需要将json转为dict
        import json
        # 无论啥情况下其实只有一个结果，安全写法
        result = results[0]
        if isinstance(result, str):
            try:
                result = json.loads(result)
            except json.JSONDecodeError:
                import ast
                try:
                    # 作为备选解析方法，只解析Python字面量，比json.loads更安全
                    result = ast.literal_eval(result)
                except (SyntaxError, ValueError):
                    self.logger.critical(f"combine_results: 无法解析内容: {result[:200]}...")
        # TODO: 简单处理，实际上如果解析失败还是会影响到后面的流程，使得update_values失败
        # TODO: 未对tables:[]的空集情况进行处理
        return result
    
    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        格式化最终选择的 SQL 及其选择理由（如果存在）
        
        Args:
            results: 包含 'selected_sql' 和可选 'chain_of_thought_reasoning' 的字典
            
        Returns:
            格式化后的字符串
        """
        if not results or not isinstance(results, dict):
            # 兼容旧的直接返回 SQL 字符串的格式
            if isinstance(results, str) and results:
                display_parts = ["## 已选择的 SQL 查询：\n"]
                display_parts.append("```sql")
                display_parts.append(results)
                display_parts.append("```")
                return "\n".join(display_parts)
            return "未获得有效的 SQL 选择结果或结果格式不正确。"
            
        display_parts = ["## 已选择的 SQL 查询：\n"]
        
        selected_sql_id = results.get("selected_sql", "") # LLM 返回的是选择的 ID
        selected_sql_content = results.get("selected_sql_content") # 实际的 SQL 内容，假设已在 get_update_values 或其他地方加入
        reasoning = results.get("chain_of_thought", "") # 重命名 key 以保持一致
        
        # 优先使用 selected_sql_content
        sql_to_display = selected_sql_content if selected_sql_content else selected_sql_id 

        if sql_to_display:
            display_parts.append(f"**选择的 SQL (ID: {selected_sql_id if selected_sql_id else 'N/A'})**:\n```sql")
            display_parts.append(sql_to_display)
            display_parts.append("```")
            
            if reasoning:
                display_parts.append("\n### 选择理由:")
                display_parts.append(f"> {reasoning}")
        else:
            display_parts.append("- 未能选择有效的 SQL 查询。")
            if reasoning:
                 display_parts.append("\n### 思考过程 (未选定 SQL): ")
                 display_parts.append(f"> {reasoning}")
        
        return "\n".join(display_parts)
