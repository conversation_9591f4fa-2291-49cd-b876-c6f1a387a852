from workflow.base.base_tool_step import Tool
from workflow.base.state import WorkflowState
from typing import Dict, Any, List, Union, Optional
from database_utils.retrieval.db_values.search import find_db_values
from pathlib import Path

class LSHRetrieverStep(Tool):
    def __init__(self):
        super().__init__(
            name="lsh_retriever",
            description="根据提取到的关键词，从哈希数据库中检索出最相关的列和字段",
        )
    
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        预处理步骤
        """
        return state
    
    async def use_tool(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        使用工具
        """
        lsh_search_results=await self.lsh_search(state,**kwargs)
        return {
            "lsh_search_results":lsh_search_results
            }
    async def parse_results(self, raw_results: Any, state: WorkflowState) -> WorkflowState:
        """
        解析结果
        """
        step_result = state.step_results[self.name]
        if isinstance(raw_results, dict):
            step_result.raw_response = raw_results
        
        lsh_search_results = {}
        error_list = []
        
        try:
            # 获取LSH搜索结果
            if "lsh_search_results" in raw_results:
                lsh_search_results = raw_results["lsh_search_results"]
            else:
                lsh_search_results = raw_results  # 直接使用原始结果
            
            # 生成候选列
            lsh_candidate_columns = {}
            for table_name, columns in lsh_search_results.items():
                # 如果表不存在，初始化为空集合
                if table_name not in lsh_candidate_columns:
                    lsh_candidate_columns[table_name] = set()
                
                # 直接将所有列名添加到集合中，集合会自动去重
                lsh_candidate_columns[table_name].update(columns.keys())

            # 将集合转回列表
            for table_name in lsh_candidate_columns:
                lsh_candidate_columns[table_name] = list(lsh_candidate_columns[table_name])
            
            self.logger.info(f"LSH搜索完成，找到候选列: {lsh_candidate_columns}")
            
            # 设置解析结果
            step_result.parsed_result = {
                "status": "success",
                "data": {
                    "lsh_search_results": lsh_search_results,
                    "lsh_candidate_columns": lsh_candidate_columns
                },
                "error": None
            }
        except Exception as e:
            self.logger.error(f"解析LSH搜索结果失败: {e}")
            error_list.append(f"解析LSH搜索结果失败: {e}")
            
            step_result.parsed_result = {
                "status": "error",
                "data": {
                    "lsh_search_results": {},
                    "lsh_candidate_columns": {}
                },
                "error": error_list
            }
        
        state.results = step_result.parsed_result["data"]
        return state
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        """
        
        return {
            "lsh_candidate_columns":step_outputs.parsed_result["data"]["lsh_candidate_columns"]
        }
        
    async def lsh_search(self, state: WorkflowState, **kwargs: Any) -> Any:
        """
        根据用户问题和提取的关键词，使用LSH从数据库中查找相似的值
        """
        
        # 获取关键词列表
        keywords = state.keywords
        if not keywords:
            self.logger.warning("无关键词可用于LSH搜索")
            return {}

        # 使用config中的数据库配置
        db_type = state.db_type
        db_id = state.global_db_config.get("database", "adm")
        # TODO: 需要修改为从state中获取
        db_directory_path = Path(__file__).resolve().parents[4] / "database" / db_id
        
        # 存储所有关键词的搜索结果
        lsh_search_results = {}
        
        # 为每个关键词搜索相似值
        for keyword in keywords:
            try:
                search_result = find_db_values(
                    db_directory_path=db_directory_path,
                    keyword=keyword,
                    db_type=db_type,
                    db_id=db_id,
                    signature_size=128,
                    n_gram=3,
                    top_n=10
                )
                
                # 将结果合并到总结果中
                for table_name, columns in search_result.items():
                    if table_name not in lsh_search_results:
                        lsh_search_results[table_name] = {}
                    
                    for column_name, values in columns.items():
                        if column_name not in lsh_search_results[table_name]:
                            lsh_search_results[table_name][column_name] = []
                        
                        # 合并值列表，避免重复
                        for value in values:
                            if value not in lsh_search_results[table_name][column_name]:
                                lsh_search_results[table_name][column_name].append(value)
                
                self.logger.info(f"关键词 '{keyword}' 的LSH搜索完成")
            except Exception as e:
                self.logger.error(f"关键词 '{keyword}' 的LSH搜索失败: {e}")
        
        return lsh_search_results

    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        显示 LSH 搜索结果，主要是候选列
        
        Args:
            results: 包含 'lsh_candidate_columns' 的字典
            
        Returns:
            格式化后的字符串
        """
        if not results or not isinstance(results, dict) or "lsh_candidate_columns" not in results:
            return "未获得有效的 LSH 检索结果或结果格式不正确。"

        display_parts = ["## LSH 检索结果：\\n"]
        
        candidate_columns = results.get("lsh_candidate_columns")
        
        if not candidate_columns or not isinstance(candidate_columns, dict):
             display_parts.append("- 未通过 LSH 检索到候选列。")
        else:
            display_parts.append("### LSH 候选列：\\n")
            if not candidate_columns: # 检查字典是否为空
                 display_parts.append("- 未检索到任何候选列。")
            else:
                for table_name, columns in candidate_columns.items():
                    if columns: # 确保列列表不为空
                        display_parts.append(f"- **表名:** `{table_name}`")
                        display_parts.append(f"  - **列名:** {', '.join(f'`{col}`' for col in columns)}")
                    else:
                        display_parts.append(f"- **表名:** `{table_name}` (无相关列)")

        # 可以选择性地添加显示 lsh_search_results 中的部分值作为示例，但默认不显示以保持简洁
        # lsh_values = results.get("lsh_search_results")
        # if lsh_values:
        #     display_parts.append("\\n### LSH 检索到的值 (示例):\\n")
        #     # 添加显示少量示例值的逻辑...

        return "\\n".join(display_parts)

