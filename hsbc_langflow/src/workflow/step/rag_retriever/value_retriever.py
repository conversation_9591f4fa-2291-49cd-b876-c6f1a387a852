'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 7:15:16 am
'''

from typing import Dict, Any, List, Union, Optional
from workflow.base.base_llm_step import Agent
from workflow.base.state import WorkflowState
from utils.llm.processor.prompts import get_prompt
from utils.llm.processor.parsers import get_parser
import json

class ValueRetrieverStep(Agent):
    """
    从用户问题中提取有价值的信息的步骤
    """
    
    def __init__(self, 
                model_name: str = 'qwen_vllm', 
                num_calls: int = 1,
                max_retry_calls: int = 1,
                parser_name: str = "python_list_output_parser"):
        super().__init__(
            name="value_retriever",
            description="从问题中提取关键信息",
            num_calls=num_calls,
            max_retry_calls=max_retry_calls,
            model_name=model_name
        )
        self.parser_name = parser_name
        
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        构建提示词以提取问题中的关键信息
        
        Args:
            state: 工作流状态
            
        Returns:
            更新后的工作流状态
        """
        # 设置提示模板名称
        state.cache['template_name'] = "extract_keywords"
        
        # 调用父类的preprocess方法完成通用的预处理逻辑
        return await super().preprocess(state)

    def get_prompt_variables(self, state: WorkflowState) -> Dict[str, Any]:
        """
        提供格式化提示所需的变量
        
        Args:
            state: 工作流状态
            
        Returns:
            变量字典
        """
        return {
            "system":{
                "QUESTION": state.user_question,
                "HINT": state.hint
            },
            "user":{
                "QUESTION": state.user_question,
            }
        }
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        
        Args:
            state: 工作流状态对象
            step_outputs: 步骤输出
            
        Returns:
            需要更新的键值对字典
        """
        results = step_outputs.parsed_result['data']
        return {"keywords": results if results and results != {} else []}
        
    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        格式化提取的关键词用于显示
        
        Args:
            results: 解析后的结果，预期是包含关键词的列表
            
        Returns:
            格式化后的字符串
        """
        if not results or not isinstance(results, list):
             # 尝试从字典中提取 'data'，兼容旧格式
             if isinstance(results, dict) and 'data' in results and isinstance(results['data'], list):
                 results = results['data']
             else:
                 return "未获得有效的关键词提取结果或结果格式不正确。"
            
        display_parts = ["## 提取的关键词和短语：\n"]
        
        if not results:
            display_parts.append("- 未提取到任何关键词或短语。")
        else:
            # 使用 Markdown 列表格式化
            display_parts.append("\n".join(f"- `{keyword}`" for keyword in results))
        
        return "\n".join(display_parts)