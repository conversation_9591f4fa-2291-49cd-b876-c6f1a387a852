from workflow.base.base_tool_step import Tool
from workflow.base.state import WorkflowState
from typing import Dict, Any, List, Union, Optional
from utils.db.mschema.database_env import DataBaseEnv
from utils.db.mschema.db_source import HITLSQLDatabase
from utils.db.mschema.db_util import init_db_conn
from utils.db.vdb_util import *
from utils.db.get_all_content import get_all_content
from utils.db.schema_util import generate_schema_string
class ColumnRetrieverStep(Tool):
    def __init__(self):
        super().__init__(
            name="column_retriever",
            description="根据提取到的关键词和用户问题，从向量数据库中检索出最相关的列",
        )
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        预处理步骤
        """
        return state
    
    async def use_tool(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        使用工具
        """
        catalog_search_results=await self.catalog_search(state,**kwargs)
        sql_sample_search_result=await self.sql_sample_search(state,**kwargs)
        return {
            "catalog_search_results":catalog_search_results,
            "sql_sample_search_result":sql_sample_search_result
            }
        
    async def parse_results(self, raw_results: Any, state: WorkflowState) -> WorkflowState:
        """
        解析结果
        """
        step_result = state.step_results[self.name]
        if isinstance(raw_results, dict):
            step_result.raw_response = raw_results
        
        candidate_columns = {}
        fewshot_examples = ''
        error_list = []
        
        # 处理catalog_search_results
        try:
            if "catalog_search_results" in raw_results:
                if not raw_results["catalog_search_results"]:
                    self.logger.warning("catalog_search_results为空")
                    error_list.append("catalog_search_results为空")
                else:
                    # 处理合并后的结果
                    merged_results = raw_results["catalog_search_results"]
                    state.merged_column_info = merged_results
                    for col_name, column_info in merged_results.items():
                        # 从col_code解析表名和列名
                        table_name = 'table_'+state.model_id
                        column_name = column_info.get("col_name", "")
                        
                        # 初始化表对应的集合
                        if table_name not in candidate_columns:
                            candidate_columns[table_name] = set()
                        
                        # 直接添加到集合，自动去重
                        candidate_columns[table_name].add(column_name)
                    
                    # 将集合转回列表
                    for table_name in candidate_columns:
                        candidate_columns[table_name] = list(candidate_columns[table_name])
                    
                    
                    self.logger.success(f"记录candidate_columns: {candidate_columns}")
        except Exception as e:
            self.logger.error(f"解析catalog_search_results失败: {e}")
            error_list.append(f"解析catalog_search_results失败: {e}")
        
        try:
            state.temp_db_schema = generate_schema_string(candidate_columns,state.merged_column_info,state.col_data_examples)
            self.logger.success(f"记录temp_db_schema: {state.temp_db_schema}")
        except Exception as e:
            self.logger.error(f"生成temp_db_schema失败: {e}")
            error_list.append(f"生成temp_db_schema失败: {e}")
        # 处理sql_sample_search_result
        # try:
        #     if "sql_sample_search_result" in raw_results:
        #         results = raw_results["sql_sample_search_result"]
        #         if not results:
        #             self.logger.warning("sql_sample_search_result为空")
        #             error_list.append("sql_sample_search_result为空")
        #         else:
        #             # 使用列表推导式构建示例字符串
        #             examples = []
        #             for i, result in enumerate(results, 1):
        #                 example = (
        #                     f"[Example {i}]\n"
        #                     f"[用户问题]\n{result['user_question']}\n"
        #                     f"[SQL]\n{result['sql']}\n"
        #                     f"[知识证据]\n{result['knowledge_evidence']}\n"
        #                 )
        #                 examples.append(example)
                    
        #             # 使用join连接所有示例，性能更好
        #             fewshot_examples = "\n".join(examples)
        #             self.logger.success("记录fewshot_examples完成")
        #     else:
        #         self.logger.warning("raw_results中未找到sql_sample_search_result键")
        #         error_list.append("raw_results中未找到sql_sample_search_result键")
        # except Exception as e:
        #     self.logger.error(f"解析sql_sample_search_result失败: {e}")
        #     error_list.append(f"解析sql_sample_search_result失败: {e}")
        
        # 设置解析结果
        step_result.parsed_result = {
            "status": "success",
            "data": {
                "fewshot_examples": fewshot_examples,
                "candidate_columns": candidate_columns
            },
            "error": error_list if error_list else None
        }
        state.results = step_result.parsed_result["data"]
        return state
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        """
        
        return {
            "fewshot_examples":step_outputs.parsed_result["data"]["fewshot_examples"],
            "candidate_columns":step_outputs.parsed_result["data"]["candidate_columns"]
        }
        
    async def catalog_search(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        根据用户问题生成数据库表的schema
        """
        if state.index_type == "compute":
            partition_name = 'index'+state.model_id
            search_field = ['col_name_cn', "col_desc"]
        else:
            partition_name = state.model_id
            search_field = ['col_name_cn', "col_desc", "col_name"]
        general_kwargs={
            # 判断是pgvector还是milvus
            "db_type":state.vector_db_type,
            # 判断 get_all_content 参数
            "vector_client":state.pgvector_client,
            "mysql_client":state.mysql_client,
            "mysql_name":"model_info",
            "partition":partition_name,
            "search_types":search_field,
            "topk":state.global_vector_db_config.get(state.vector_db_type,'pgvector').get('search_schema',{}).get('limit',3)
        }
        # 创建一个空字典用于存储合并结果
        merged_catalog_results = {}

        # 1. 根据keywords
        for key_word in state.keywords:
            search_results = hybrid_search_vector_db(
                query=key_word,
                **general_kwargs,
            )
            # 处理搜索结果
            search_results = merge_search_results(search_results,state.index_type,state.model_id)
            # 合并到主字典中
            merged_catalog_results.update(search_results)
            
        # 2. 根据user_question
        user_catalog_search_result = hybrid_search_vector_db(
            query=state.user_question,
            **general_kwargs,
        )
        user_catalog_search_result = merge_search_results(user_catalog_search_result,state.index_type,state.model_id)
        # 合并到主字典中
        merged_catalog_results.update(user_catalog_search_result)

        # 直接返回合并后的字典
        return merged_catalog_results

    async def sql_sample_search(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        根据用户问题生成数据库表的schema
        """
        # kwargs={
        #     "query":state.user_question,
        #     "table_name":"sql_demo",
        #     "db_type":state.vector_db_type,
        #     "vector_field":"embedding_user_question",
        #     "output_fields":["user_question","knowledge_evidence","sql"],
        #     "topk":state.global_vector_db_config.get('topk',3),
        #     "expr":state.global_vector_db_config.get('expr',''),
        #     "partition_name":state.global_vector_db_config.get('partition_name','9888'),
        #     "metric_type":state.global_vector_db_config.get('metric_type','cosine'),
        # }
        # sql_sample_search_result=search_vector_db(
        #     **kwargs,
        # )
        # sql_sample_search_result=parse_search_results(sql_sample_search_result)
        
        # return sql_sample_search_result
        return ''
    
    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        显示结果
        """
        if not results or not isinstance(results, dict):
            return "未获得有效结果或结果格式不正确。"

        display_parts = ["## 知识库查询结果：\n"]

        candidate_columns = results.get("candidate_columns")
        if candidate_columns:
            display_parts.append("### 候选列：\n")
            if isinstance(candidate_columns, dict):
                for table_name, columns in candidate_columns.items():
                    if columns: # 确保列列表不为空
                         display_parts.append(f"- **表名:** `{table_name}`")
                         display_parts.append(f"  - **列名:** {', '.join(f'`{col.split(table_name+'_')[-1]}`' for col in columns)}")
                    else:
                         display_parts.append(f"- **表名:** `{table_name}` (无相关列)")
            else:
                display_parts.append("- 候选列数据格式错误")
            display_parts.append("\n") # 添加空行分隔

        # fewshot_examples = results.get("fewshot_examples")
        # if fewshot_examples:
        #     display_parts.append("### Few-shot 示例：\n")
        #     display_parts.append(f"{fewshot_examples}")
        # else:
        #      display_parts.append("### Few-shot 示例：\n- 未找到相关示例。")


        # 如果两个部分都为空，返回提示信息
        # if not candidate_columns and not fewshot_examples:
        #     return "未从知识库中检索到候选列或 Few-shot 示例。"
        if not candidate_columns:
            return "未从知识库中检索到候选列。"

        return "\n".join(display_parts)