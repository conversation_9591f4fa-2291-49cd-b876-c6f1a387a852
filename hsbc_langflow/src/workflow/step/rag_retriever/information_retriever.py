'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 7:15:07 am
'''

from typing import Dict, Any, List, Union, Optional
import logging
from workflow.base.base_llm_step import Agent
from workflow.base.state import WorkflowState
from utils.llm.processor.prompts import get_prompt
from utils.llm.processor.parsers import get_parser
import json
import re
from datetime import datetime
from utils.common.time_converter import convert_date_format
from copy import deepcopy
from utils.db.vdb_util import condition_corrector
import numpy as np
import json
class InformationRetrieverStep(Agent):
    """
    从用户问题中提取有价值的信息的步骤
    """
    
    def __init__(self, 
                model_name: str = 'qwen_vllm', 
                num_calls: int = 1,
                max_retry_calls: int = 1,
                parser_name: str = "generic_json",
                is_callback: bool = True):
        super().__init__(
            name="information_retriever",
            description="从问题中提取关键信息",
            num_calls=num_calls,
            max_retry_calls=max_retry_calls,
            model_name=model_name,
        )
        self.parser_name = parser_name
        self.is_callback = is_callback
        
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        构建提示词以提取问题中的关键信息
        
        Args:
            state: 工作流状态
            
        Returns:
            更新后的工作流状态
        """
        # 设置提示模板名称
        if state.index_type == "compute":
            state.cache['template_name'] = "information_extractor_compute"
        else:
            state.cache['template_name'] = "information_extractor"
        
        # 调用父类的preprocess方法完成通用的预处理逻辑
        return await super().preprocess(state)

    def get_prompt_variables(self, state: WorkflowState) -> Dict[str, Any]:
        """
        提供格式化提示所需的变量
        
        Args:
            state: 工作流状态
            
        Returns:
            变量字典
        """
        return {
            "system":{
                "QUERY": state.user_question,
            },
            "user":{
                "QUERY": state.user_question,
                "HINT": state.hint
            }
        }
    
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        
        Args:
            state: 工作流状态对象
            step_outputs: 解析后的结果
            
        Returns:
            需要更新的键值对字典
        """
        retriever_result = deepcopy(step_outputs.parsed_result['data'])
        
        keywords = retriever_result.get("keywords", [])
        keywords_en = retriever_result.get("keywords_en", [])
        caliberBiz = retriever_result.get("caliberBiz", "")
        time_period = retriever_result.get("time_period", "day")
        message = retriever_result.get("message", "")
        
        all_keywords = keywords + keywords_en
        time_info = retriever_result.get("time", '')
        
        
        if time_info:
            real_time = convert_date_format(time_info)
            user_question = (
                f"{state.user_question}\n"
                f"【业务口径整理】：\n{caliberBiz}\n"
                f"【口径时间对应真实时间】：\n{real_time['description']}"
            )
        else:
            real_time = {"description": ""}
            user_question = (
                f"{state.user_question}\n"
                f"【业务口径整理】：\n{caliberBiz}\n"
            )


        real_time["period"] = time_period
        # # 如果message不为空，也将其添加到用户问题中
        # if message:
        #     user_question += f"\n【提取信息的反馈】：\n{message}"
        
        return {
            "query_target_time": real_time,
            "time_period": real_time["period"],
            "keywords": all_keywords,
            "biz_caliber": caliberBiz,
            "user_question": user_question
        }
        
    def combine_results(self, results: List[Any]) -> Any:
        """
        合并多个解析结果为单一结果
        
        Args:
            results: 解析结果列表
        
        Returns:
            合并后的结果
        """
        # 解析list[str],str为json，需要将json转为dict
        import json
        # 无论啥情况下其实只有一个结果，安全写法
        result = results[0]
        if isinstance(result, str):
            try:
                result = json.loads(result)
            except json.JSONDecodeError:
                import ast
                try:
                    # 作为备选解析方法，只解析Python字面量，比json.loads更安全
                    result = ast.literal_eval(result)
                except (SyntaxError, ValueError):
                    self.logger.critical(f"combine_results: 无法解析内容: {result[:200]}...")
        # TODO: 简单处理，实际上如果解析失败还是会影响到后面的流程，使得update_values失败
        # TODO: 未对tables:[]的空集情况进行处理
        return result
    
    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        格式化提取的信息用于显示
        
        Args:
            results: 解析后的结果，预期是一个包含提取信息的字典
            
        Returns:
            格式化后的字符串
        """
        if not results or not isinstance(results, dict):
            # 保留对旧格式 (list) 的兼容性，但主要处理字典
            if isinstance(results, list) and results:
                 display_parts = ["## 信息提取结果：\n"]
                 display_parts.append("- **关键词列表:**")
                 display_parts.append("\\n".join(f"  - `{keyword}`" for keyword in results))
                 return "\\n".join(display_parts)
            return "未获得有效的提取信息结果或结果格式不正确。"
            
        display_parts = ["## 信息提取结果：\n"]
        
        # 处理字典类型的结果
        time_info = results.get("time", "")
        real_time_desc = ""
        if time_info:
            try:
                real_time = convert_date_format(time_info)
                real_time_desc = real_time.get('description', '')
                display_parts.append(f"- **时间信息:** `{time_info}`")
                if real_time_desc:
                    display_parts.append(f"- **解析后时间:** `{real_time_desc}`")
            except Exception as e:
                 self.logger.warning(f"display_results: 解析时间信息 '{time_info}' 失败: {e}")
                 display_parts.append(f"- **时间信息:** `{time_info}` (解析失败)")


        time_period = results.get("time_period", "")
        if time_period:
            display_parts.append(f"- **时间粒度:** `{time_period}`")
        
        keywords = results.get("keywords", [])
        if keywords:
            display_parts.append("- **关键词:**")
            display_parts.append("  - " + ", ".join(f"`{keyword}`" for keyword in keywords))

        keywords_en = results.get("keywords_en", [])
        if keywords_en:
            display_parts.append("- **英文关键词:**")
            display_parts.append("  - " + ", ".join(f"`{keyword}`" for keyword in keywords_en))
        
        caliber_biz = results.get("caliberBiz", "")
        if caliber_biz:
            display_parts.append(f"- **业务口径:** `{caliber_biz}`")

        # 如果没有任何有效信息被提取，显示提示
        if len(display_parts) == 1: # 只有标题
            display_parts.append("- 未提取到有效信息。")
        
        return "\\n".join(display_parts)
        
