from typing import Dict, Any, List, Union, Optional
from workflow.base.base_llm_step import Agent
from workflow.base.state import WorkflowState
from copy import deepcopy
from utils.db.mschema.db_check import execute_sql_candidates
class SQLGeneratorStep(Agent):
    """
    根据mschema生成SQL查询的步骤
    """
    
    def __init__(self, 
                model_name: str = 'qwen_vllm', 
                num_calls: int = 1,
                max_retry_calls: int = 1,
                parser_name: str = "generated_candidate_finetuned",
                is_callback: bool = True):
        super().__init__(
            name="sql_generator",
            description="根据mschema生成SQL查询",
            num_calls=num_calls,
            max_retry_calls=max_retry_calls,
            model_name=model_name,
            is_callback=is_callback
        )
        self.parser_name = parser_name
        
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        构建提示词以生成SQL查询
        
        Args:
            state: 工作流状态
            
        Returns:
            更新后的工作流状态
        """
        # 设置提示模板名称
        state.cache["template_name"] = "icl_generator"
        
        # 调用父类的preprocess方法完成通用的预处理逻辑
        return await super().preprocess(state)

    def get_prompt_variables(self, state: WorkflowState) -> Dict[str, Any]:
        """
        提供格式化提示所需的变量
        
        Args:
            state: 工作流状态
            
        Returns:
            变量字典
        """
        return {
            "system":{
                "EXAMPLES": state.fewshot_examples,
                "DATABASE_TYPE": state.db_type
            },
            "user":{
                "QUESTION": state.user_question,
                "DATABASE_SCHEMA": state.db_schema,
                "HINT": state.hint,
            }
        }
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        
        Args:
            state: 工作流状态对象
            step_outputs: 解析后的结果
            
        Returns:
            需要更新的键值对字典
        """
        sql_result = deepcopy(step_outputs.parsed_result['data'])
        self.logger.info(f"sql_result: {sql_result}")
        # execution_results = execute_sql_candidates(state.db_env, sql_result)
        # self.logger.info(f"execution_results: {execution_results}")
        return {"sql_candidates": sql_result}
    
    def combine_results(self, results: List[str]) -> List[str]:
        """
        合并多个SQL结果，提取每个结果中的SQL语句并去重
        
        Args:
            results: 解析结果列表，每个元素是包含SQL的字符串，如 "{'SQL': 'SELECT...'}"
            
        Returns:
            去重后的SQL语句列表
        """
        import json
        import ast
        sql_set = set()  # 使用集合去重
        
        for result in results:
            try:
                if isinstance(result, str):
                    # 先尝试json解析（更快）
                    try:
                        # 替换单引号为双引号以符合JSON格式
                        # json_str = result.replace("'", "\"")
                        result_dict = json.loads(result)
                    except:
                        # 如果json解析失败，再使用ast（更安全）
                        result_dict = ast.literal_eval(result)
                    
                    # 提取SQL值
                    if 'SQL' in result_dict:
                        sql_set.add(result_dict['SQL'])
                    elif 'sql' in result_dict:
                        sql_set.add(result_dict['sql'])
                elif isinstance(result, dict):
                    # 如果已经是字典形式
                    if 'SQL' in result:
                        sql_set.add(result['SQL'])
                    elif 'sql' in result:
                        sql_set.add(result['sql'])
            except Exception as e:
                self.logger.warning(f"提取SQL语句失败: {str(e)}, 原始结果: {result[:200]}...")
        
        # 转换为dict返回
        return list(sql_set)
    
    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        格式化生成的 SQL 查询候选列表用于显示
        
        Args:
            results: 解析后的结果，预期是包含 SQL 候选列表的字典或直接是 SQL 列表
            
        Returns:
            格式化后的字符串
        """
        if not results or (isinstance(results, list) and not results):
             return "未生成有效的 SQL 查询候选。"
            
        display_parts = ["## 生成的 SQL 查询候选：\n"]
        
        # 将 results 统一处理为列表
        sql_candidates = []
        if isinstance(results, dict):
            # 尝试从常见的 key 中提取 SQL 列表
            if 'sql_candidates' in results and isinstance(results['sql_candidates'], list):
                 sql_candidates = results['sql_candidates']
            elif 'data' in results and isinstance(results['data'], list):
                 sql_candidates = results['data']
            elif 'sql' in results and isinstance(results['sql'], list):
                 sql_candidates = results['sql']
            elif isinstance(results, dict) and all(isinstance(v, str) for v in results.values()): # 类似 {"1": sql1, "2": sql2}
                 sql_candidates = list(results.values())
        elif isinstance(results, list):
            sql_candidates = results
        else:
             return "SQL 生成结果格式无法识别。"

        if not sql_candidates:
            display_parts.append("- 未生成任何 SQL 查询候选。")
        else:
            for i, item in enumerate(sql_candidates):
                 sql = ""
                 explanation = ""
                 if isinstance(item, str):
                     sql = item
                 elif isinstance(item, dict):
                     # 假设字典可能包含 SQL 和解释
                     sql = item.get("SQL", item.get("sql", ""))
                     explanation = item.get("explanation", item.get("chain_of_thought_reasoning", ""))
                 
                 if sql:
                     display_parts.append(f"### 候选 {i+1}:\n```sql")
                     display_parts.append(sql)
                     display_parts.append("```")
                     if explanation:
                         display_parts.append(f"**解释/思考过程:** {explanation}")
                     display_parts.append("") # 添加空行分隔
                 else:
                      display_parts.append(f"### 候选 {i+1}: (无效格式或无 SQL)")

        return "\n".join(display_parts)
