'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 7:15:47 am
'''

from typing import Dict, Any, List, Union, Optional
from workflow.base.base_llm_step import Agent
from workflow.base.state import WorkflowState
from copy import deepcopy
from utils.llm.processor.prompts import get_prompt
from utils.db.mschema.db_check import execute_sql_candidates

class SQLRefinerStep(Agent):
    """
    优化和完善SQL查询的步骤
    """
    
    def __init__(self, 
                model_name: str = 'qwen_vllm', 
                num_calls: int = 1,
                max_retry_calls: int = 1,
                parser_name: str = "generated_candidate_finetuned",
                is_callback: bool = True):
        super().__init__(
            name="sql_refiner",
            description="优化和完善SQL查询",
            num_calls=num_calls,
            max_retry_calls=max_retry_calls,
            model_name=model_name,
            is_callback=is_callback
        )
        self.parser_name = parser_name
        
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        构建提示词以优化SQL查询
        
        Args:
            state: 工作流状态
            
        Returns:
            更新后的工作流状态
        """
        # 设置提示模板名称
        execution_results = execute_sql_candidates(state.db_env, state.sql_candidates)
        self.logger.info(f"候选SQL执行结果: {execution_results}")
        state.execution_results = execution_results
        state.cache["template_name"] = "refiner"
        
        # 如果状态中设置了template_name，则使用它加载并格式化提示
        if state.cache['template_name']:
            # 使用模板系统加载prompt
            prompt_template_user = get_prompt(state.cache['template_name']+"_user")
            prompt_template_system = get_prompt(state.cache['template_name']+"_system")
            
            
            # 初始化messages列表，用于存储所有候选项的提示
            all_messages = []
            
            # 遍历每个SQL候选项及其执行结果
            for key, execution_result in state.execution_results.items():
                # 为每个候选项构建输入参数
                current_vars = {
                    "system": {
                        "DATABASE_TYPE": state.db_type,
                        "DATABASE_SCHEMA": state.db_schema,
                        "HINT": state.hint,
                        "QUESTION": state.user_question,
                    },
                    "user": {
                        "SQL": execution_result["sql"],
                        "ERROR": execution_result.get("error") if execution_result.get("error") else "",
                        "EXECUTION_RESULT": execution_result.get("result") if execution_result.get("result") else ""
                    }
                }
                
                # 格式化prompt
                formatted_prompt_user = prompt_template_user.format_prompt(**current_vars['user']).to_string()
                formatted_prompt_system = prompt_template_system.format_prompt(**current_vars['system']).to_string()
                
                # 构建当前候选项的消息
                current_messages = [
                    {"role": "system", "content": formatted_prompt_system},
                    {"role": "user", "content": formatted_prompt_user}
                ]
                
                # 将当前消息添加到总消息列表中
                all_messages.append(current_messages)
            
            # 将所有消息存储到状态中
            state.prompt[self.name] = all_messages
            
        
        return state

    def get_prompt_variables(self, state: WorkflowState) -> Dict[str, Any]:
        """
        提供格式化提示所需的变量
        
        Args:
            state: 工作流状态
            
        Returns:
            变量字典或变量字典列表
        """
        pass
        
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        
        Args:
            state: 工作流状态对象
            step_outputs: 步骤输出
            
        Returns:
            需要更新的键值对字典
        """
        refined_sql = deepcopy(step_outputs.parsed_result['data'])
        execution_results = execute_sql_candidates(state.db_env, refined_sql)
        self.logger.info(f"refined_sql: {refined_sql}")
        self.logger.info(f"execution_results: {execution_results}")
        return {
            "refined_sql": refined_sql or state.sql_candidates,
            "refined_execution_results": execution_results
        }
    
    def combine_results(self, results: List[str]) -> Dict[str, Any]:
        """
        合并多个SQL结果，提取每个结果中的SQL语句并去重
        
        Args:
            results: 解析结果列表，每个元素是包含SQL的字符串，如 "{'SQL': 'SELECT...'}"
            
        Returns:
            去重后的SQL语句列表
        """
        import json
        import ast
        sql_set = set()  # 使用集合去重
        
        for result in results:
            try:
                if isinstance(result, str):
                    # 先尝试json解析（更快）
                    try:
                        # 替换单引号为双引号以符合JSON格式
                        # json_str = result.replace("'", "\"")
                        result_dict = json.loads(result)
                    except:
                        # 如果json解析失败，再使用ast（更安全）
                        result_dict = ast.literal_eval(result)
                    
                    # 提取SQL值
                    if 'SQL' in result_dict:
                        sql_set.add(result_dict['SQL'])
                    elif 'sql' in result_dict:
                        sql_set.add(result_dict['sql'])
                elif isinstance(result, dict):
                    # 如果已经是字典形式
                    if 'SQL' in result:
                        sql_set.add(result['SQL'])
                    elif 'sql' in result:
                        sql_set.add(result['sql'])
            except Exception as e:
                self.logger.warning(f"提取SQL语句失败: {str(e)}, 原始结果: {result[:200]}...")
        
        # 转换为dict返回
        return list(sql_set)
        
    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        格式化优化后的 SQL 查询结果用于显示
        
        Args:
            results: 解析后的结果，预期是包含优化后 SQL 的列表或字典
            
        Returns:
            格式化后的字符串
        """
        if not results or (isinstance(results, list) and not results):
            return "未获得有效的 SQL 优化结果。"
            
        display_parts = ["## 优化后的 SQL 查询：\n"]
        
        # 将 results 统一处理为列表，以便迭代
        results_list = []
        if isinstance(results, dict): # 假设字典的 value 是 SQL 列表或单个 SQL
            if isinstance(results.get('data'), list): # 检查是否是 {data: [sql1, sql2]} 结构
                 results_list = results['data']
            elif isinstance(results.get('refined_sql'), list):
                 results_list = results.get('refined_sql')
            elif isinstance(results.get('sql'), str):
                 results_list = [results.get('sql')] # 单个SQL
            elif isinstance(results, dict) and all(isinstance(v, str) for v in results.values()): # 类似 {"1": sql1, "2": sql2}
                 results_list = list(results.values())
        elif isinstance(results, list):
            results_list = results
        else:
             return "SQL 优化结果格式无法识别。"
        
        if not results_list:
             display_parts.append("- 未生成任何优化后的 SQL 查询。")
        else:
            for i, item in enumerate(results_list):
                 sql = ""
                 explanation = ""
                 if isinstance(item, str):
                     sql = item
                 elif isinstance(item, dict):
                     sql = item.get("SQL", item.get("sql", ""))
                     explanation = item.get("explanation", "")
                 
                 if sql:
                     display_parts.append(f"### 候选 {i+1}:\n```sql")
                     display_parts.append(sql)
                     display_parts.append("```")
                     if explanation:
                         display_parts.append(f"**优化说明:** {explanation}")
                     display_parts.append("") # 添加空行分隔
                 else:
                     display_parts.append(f"### 候选 {i+1}: (无效格式或无 SQL)")
        
        return "\n".join(display_parts)