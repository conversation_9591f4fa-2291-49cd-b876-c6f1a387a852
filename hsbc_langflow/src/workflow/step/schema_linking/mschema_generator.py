'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Thursday, 29th May 2025 9:37:17 am
'''

from workflow.base.base_tool_step import Tool
from workflow.base.state import WorkflowState
from typing import Dict, Any, List, Union, Optional
from utils.db.mschema.database_env import DataBaseEnv
from utils.db.mschema.db_source import HITLSQLDatabase
from utils.db.mschema.db_util import init_db_conn

class SchemaGeneratorStep(Tool):
    def __init__(self):
        super().__init__(
            name="Schema Generator",
            description="根据用户问题生成数据库表的mschema",
        )
    
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        预处理步骤
        """
        formatted_columns = [
            f"{table_name}.{column}" 
            for table_name, columns in state.selected_columns.items()
            for column in columns
        ]
        state.formatted_columns=formatted_columns
        return state
    
    async def use_tool(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        根据用户问题生成数据库表的schema
        """
        try:
            # 方式1: 使用原有的db_config方式（保持兼容）
            db_engine = init_db_conn(state.nl2sql_db_config)
            db_source = HITLSQLDatabase(db_engine)
            mschema_str = db_source.mschema.to_mschema(selected_columns=state.formatted_columns)
        except Exception as e:
            # 如果原有方式失败，尝试新的客户端系统
            try:
                from utils.db.mschema.db_util import init_db_from_client_system
                db_source = init_db_from_client_system('mysql', 'hsbc_data')
                mschema_str = db_source.mschema.to_mschema(selected_columns=state.formatted_columns)
            except Exception as e2:
                raise RuntimeError(f"Both database initialization methods failed: {e}, {e2}")
        
        # 修复join操作，确保mschema_str和state.associated_keys_schema都是字符串
        if isinstance(mschema_str, list):
            mschema_str = "\n".join(mschema_str)
        
        state.db_schema = f"{mschema_str}\n{state.associated_keys_schema}"
        state.db_env = DataBaseEnv(db_source)
        return mschema_str  # 返回生成的schema字符串
    
    async def display_results(self, results: Any) -> str:
        """
        显示生成的数据库 Schema
        """
        if not results or not isinstance(results, str):
            return "未生成有效的数据库 Schema 或结果格式不正确。"
            
        display_parts = ["## 生成的数据库 Schema：\n"]
        display_parts.append(results)
        
        return "\n".join(display_parts)
    
    