from workflow.base.base_tool_step import Tool
from workflow.base.state import WorkflowState
from typing import Dict, Any, List, Union, Optional
from utils.db.mschema.database_env import DataBaseEnv
from utils.db.mschema.db_source import HITLSQLDatabase
from utils.db.mschema.db_util import init_db_conn
from utils.db.schema_util import generate_schema_string
class SchemaGeneratorStep(Tool):
    def __init__(self):
        super().__init__(
            name="Schema Generator",
            description="根据用户问题生成数据库表的mschema",
        )
    
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        预处理步骤
        """
        return state

    
    async def use_tool(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        根据用户问题生成数据库表的schema
        """
        try:
            mschema_str = generate_schema_string(state.selected_columns, state.merged_column_info,state.col_data_examples)
        except Exception as e:
            mschema_str = ''
            self.logger.warning(f"生成schema失败: {e},使用空")
        
        
        state.db_schema = f"{mschema_str}\n{state.associated_keys_schema}"

        return mschema_str  # 返回生成的schema字符串


    async def display_results(self, results: Any) -> str:
        """
        显示生成的数据库 Schema
        """
        if not results or not isinstance(results, str):
            return "未生成有效的数据库 Schema 或结果格式不正确。"
            
        display_parts = ["## 生成的数据库 Schema：\n"]
        display_parts.append(results)
        
        return "\n".join(display_parts)
    
    