from typing import Dict, Any, List, Union, Optional

from workflow.base.base_llm_step import Agent
from workflow.base.state import WorkflowState
from copy import deepcopy

class ColumnSelectorStep(Agent):
    """
    从用户问题中提取有价值的信息的步骤
    """
    
    def __init__(self, 
                model_name: str = 'qwen_vllm', 
                num_calls: int = 1,
                max_retry_calls: int = 1,
                parser_name: str = "select_columns",
                is_callback: bool = True):
        super().__init__(
            name="column_selector",
            description="根据知识库挑选的列，选择最相关的列",
            num_calls=num_calls,
            max_retry_calls=max_retry_calls,
            model_name=model_name,
            )
        self.parser_name = parser_name
        self.is_callback = is_callback
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        构建提示词以提取问题中的关键信息
        
        Args:
            state: 工作流状态
            
        Returns:
            更新后的工作流状态
        """
        # 设置提示模板名称
        state.cache['template_name'] = "select_columns"
        
        # 调用父类的preprocess方法完成通用的预处理逻辑
        return await super().preprocess(state)

    def get_prompt_variables(self, state: WorkflowState) -> Dict[str, Any]:
        """
        提供格式化提示所需的变量
        
        Args:
            state: 工作流状态
            
        Returns:
            变量字典
        """
        return {
            "system":{
                "QUESTION": state.user_question,
                "HINT": state.hint,
                "DATABASE_SCHEMA": state.temp_db_schema
            },
            "user":{
                "CANDIDATE_COLUMNS": state.candidate_columns
            }
        }
    def combine_results(self, results: List[Any]) -> Any:
        """
        合并多个解析结果为单一结果
        
        Args:
            results: 解析结果列表
        
        Returns:
            合并后的结果
        """
        # 解析list[str],str为json，需要将json转为dict
        import json
        # 无论啥情况下其实只有一个结果，安全写法
        result = results[0]
        if isinstance(result, str):
            try:
                result = json.loads(result)
            except json.JSONDecodeError:
                import ast
                try:
                    # 作为备选解析方法，只解析Python字面量，比json.loads更安全
                    result = ast.literal_eval(result)
                except (SyntaxError, ValueError):
                    self.logger.critical(f"combine_results: 无法解析内容: {result[:200]}...")
        # TODO: 简单处理，实际上如果解析失败还是会影响到后面的流程，使得update_values失败
        # TODO: 未对tables:[]的空集情况进行处理
        return result
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        
        Args:
            state: 工作流状态对象
            results: 解析后的结果
            
        Returns:
            需要更新的键值对字典
        """
        columns = deepcopy(step_outputs.parsed_result['data'])
        try:
            columns.pop('chain_of_thought_reasoning', None)
            self.logger.success(f"get_update_values: 筛选出的列: {columns}")
            
        except:
            self.logger.warning(f"get_update_values: 筛选出的列出问题: {columns}")
        return {"selected_columns": columns}
        
    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        格式化结果用于显示, 包括筛选的列和思考过程
        
        Args:
            results: 解析后的结果，预期是一个字典 
                     {
                       "chain_of_thought_reasoning": "...", 
                       "table_name1": ["column1", "column2", ...], 
                       ...
                     }
            
        Returns:
            格式化后的字符串
        """
        if not results or not isinstance(results, dict):
            return "未获得有效的筛选列结果或结果格式不正确。"
            
        display_parts = ["## 筛选出的相关字段：\n"]
        
        # 提取思考过程，并创建副本用于迭代列
        reasoning = results.get("chain_of_thought_reasoning")
        columns_to_display = {k: v for k, v in results.items() if k != "chain_of_thought_reasoning"}

        if not columns_to_display: # 检查除了思考过程外，是否还有其他内容
             display_parts.append("- 未筛选出任何相关字段。")
        else:
            for table_name, columns in columns_to_display.items():
                # 确保 columns 是列表类型，以防解析错误
                if isinstance(columns, list):
                    if columns: # 确保列列表不为空
                        display_parts.append(f"- **表名:** `{table_name}`")
                        display_parts.append(f"  - **选中列:** {', '.join(f'`{col}`' for col in columns)}")
                    else:
                        # 如果某个表没有选中列
                        display_parts.append(f"- **表名:** `{table_name}` (未选中列)")
                else:
                     # 如果某个键的值不是列表，提示格式问题
                     display_parts.append(f"- **表名:** `{table_name}` (数据格式错误，非列表)")


        # 在列信息后添加思考过程
        if reasoning:
            display_parts.append("\n### 筛选思考过程：\n")
            display_parts.append(f"> {reasoning}") # 使用引用块格式化

        # 如果只有标题，说明没有有效列信息，也没有思考过程
        if len(display_parts) == 1:
             display_parts.append("- 未筛选出任何相关字段。")

        return "\n".join(display_parts)
        