from workflow.base.base_tool_step import Tool
from workflow.base.state import WorkflowState
from typing import Dict, Any, List, Union, Optional
from utils.db.mschema.database_env import DataBaseEnv
from utils.db.mschema.db_source import HITLSQLDatabase
from utils.db.mschema.db_util import init_db_conn
from utils.db.vdb_util import *

class KeyAssociatorStep(Tool):
    def __init__(self):
        super().__init__(
            name="key_associator",
            description="根据获取到的columns，添加到selected_columns中",
        )
    
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        预处理步骤
        """
        return state
    
    async def use_tool(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        使用工具
        """
        catalog_search_results=await self.catalog_search(state,**kwargs)
        sql_sample_search_result=await self.sql_sample_search(state,**kwargs)
        return {
            "catalog_search_results":catalog_search_results,
            "sql_sample_search_result":sql_sample_search_result
            }
    async def parse_results(self, raw_results: Any, state: WorkflowState) -> WorkflowState:
        """
        解析结果
        """
        step_result = state.step_results[self.name]
        if isinstance(raw_results, dict):
            step_result.raw_response = raw_results
        candidate_columns = {}
        error_list = []
        try:
            if "catalog_search_results" in raw_results:
                if not raw_results["catalog_search_results"]:
                    self.logger.warning(f"catalog_search_results is empty")
                    error_list.append("catalog_search_results is empty")
                else:
                    for results in raw_results["catalog_search_results"]:
                        for result in results:
                            table_name=result["table_name"]
                            column_name=result["column_name"]
                            # 如果表名不在字典中，创建一个新的空列表
                            if table_name not in candidate_columns:
                                candidate_columns[table_name] = []
                            
                            # 将列名添加到对应表的列表中
                            if column_name not in candidate_columns[table_name]:
                                candidate_columns[table_name].append(column_name)
                self.logger.success(f"记录candidate_columns: {candidate_columns}")
        except Exception as e:
            self.logger.error(f"cannot parse catalog_search_results: {e}")
            error_list.append(f"cannot parse catalog_search_results: {e}")
        
        fewshot_examples = ''
        try:
            if "sql_sample_search_result" in raw_results:
                if not raw_results["sql_sample_search_result"]:
                    self.logger.warning(f"sql_sample_search_result is empty")
                    error_list.append("sql_sample_search_result is empty")
                else:
                    for i, result in enumerate(raw_results["sql_sample_search_result"], 1):
                        user_question = result["user_question"]
                        sql = result["sql"]
                        knowledge_evidence = result["knowledge_evidence"]
                        
                        fewshot_examples += f"[Example {i}]\n"
                        fewshot_examples += f"[用户问题]\n{user_question}\n"
                        fewshot_examples += f"[SQL]\n{sql}\n"
                        fewshot_examples += f"[知识证据]\n{knowledge_evidence}\n\n"
                    self.logger.success(f"记录fewshot_examples: {fewshot_examples}")
            else:
                self.logger.warning(f"sql_sample_search_result key not found in raw_results")
                error_list.append("sql_sample_search_result key not found in raw_results")
        except Exception as e:
            self.logger.error(f"cannot parse sql_sample_search_result: {e}")
            error_list.append(f"cannot parse sql_sample_search_result: {e}")
        
        step_result.parsed_result = {
            "status":"success",
            "data":{
                "fewshot_examples":fewshot_examples,
                "candidate_columns":candidate_columns
                    },
            "error":error_list if error_list else None
            }
        state.results = step_result.parsed_result["data"]
        return state
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        """
        
        return {
            "fewshot_examples":step_outputs.parsed_result["data"]["fewshot_examples"],
            "candidate_columns":step_outputs.parsed_result["data"]["candidate_columns"]
        }
        
    async def catalog_search(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        根据用户问题生成数据库表的schema
        """
        general_kwargs={
            "db_type":state.vector_db_type,
            "topk":state.global_vector_db_config.get('topk',3),
            "expr":state.global_vector_db_config.get('expr',''),
            "partition_name":state.global_vector_db_config.get('partition_name','9888'),
            "metric_type":state.global_vector_db_config.get('metric_type','cosine'),
        }
        # 1. 根据keywords
        catalog_search_results=[]
        catalog_search_kwargs={
            "table_name":"cata_log",
            **general_kwargs,
            "embedding_fields":["embedding_column_name",
                                "embedding_column_description",
                                "embedding_value_description"
                                ],
            "rank_type":state.global_vector_db_config.get('rank_type','hybrid-weighted'),
            "rank_rule":[
                {
                    "embedding_column_name":0.8,
                    "embedding_column_description":0.1,
                    "embedding_value_description":0.1
                },
                {   "embedding_column_name":0.1,
                    "embedding_column_description":0.8,
                    "embedding_value_description":0.1
                },
                {
                    "embedding_column_name":0.1,
                    "embedding_column_description":0.1,
                    "embedding_value_description":0.8
                }
            ],
            "out_fields":["table_name","column_name"],
        }
        for key_word in state.keywords:
            search_results=hybrid_search_vector_db(
                query=key_word,
                **catalog_search_kwargs,
            )
            search_results=parse_search_results(search_results)
            catalog_search_results.append(search_results)
            
        # 2. 根据user_question
        user_catalog_search_result = hybrid_search_vector_db(
            query=state.user_question,
            **catalog_search_kwargs,
        )
        user_catalog_search_result=parse_search_results(user_catalog_search_result)
        catalog_search_results.append(user_catalog_search_result)
        
        return catalog_search_results

    async def sql_sample_search(self, state: WorkflowState,**kwargs: Any) -> Any:
        """
        根据用户问题生成数据库表的schema
        """
        kwargs={
            "query":state.user_question,
            "table_name":"sql_demo",
            "db_type":state.vector_db_type,
            "vector_field":"embedding_user_question",
            "output_fields":["user_question","knowledge_evidence","sql"],
            "topk":state.global_vector_db_config.get('topk',3),
            "expr":state.global_vector_db_config.get('expr',''),
            "partition_name":state.global_vector_db_config.get('partition_name','9888'),
            "metric_type":state.global_vector_db_config.get('metric_type','cosine'),
        }
        sql_sample_search_result=search_vector_db(
            **kwargs,
        )
        sql_sample_search_result=parse_search_results(sql_sample_search_result)
        
        return sql_sample_search_result
    
    async def display_results(self, results: Optional[Union[Dict[str, Any], List[str]]] = None) -> str:
        """
        显示结果
        """
        if not results or not isinstance(results, dict):
            return "未获得有效结果或结果格式不正确。"

        display_parts = ["## 关键关联器结果：\n"]

        candidate_columns = results.get("candidate_columns")
        if candidate_columns:
            display_parts.append("### 候选列：\n")
            if isinstance(candidate_columns, dict):
                if candidate_columns: # 检查字典是否为空
                    for table_name, columns in candidate_columns.items():
                        if columns: # 确保列列表不为空
                            display_parts.append(f"- **表名:** `{table_name}`")
                            display_parts.append(f"  - **列名:** {', '.join(f'`{col}`' for col in columns)}")
                        else:
                            display_parts.append(f"- **表名:** `{table_name}` (无相关列)")
                else:
                     display_parts.append("- 未找到候选列。")
            else:
                display_parts.append("- 候选列数据格式错误")
            display_parts.append("\n") # 添加空行分隔

        fewshot_examples = results.get("fewshot_examples")
        if fewshot_examples:
            display_parts.append("### Few-shot 示例：\n")
            display_parts.append(f"{fewshot_examples}")
        elif candidate_columns is not None: # 仅在候选列存在时显示未找到示例
             display_parts.append("### Few-shot 示例：\n- 未找到相关示例。")

        # 如果两个部分都为空，返回提示信息
        if not candidate_columns and not fewshot_examples:
            return "未检索到候选列或 Few-shot 示例。"

        return "\n".join(display_parts)
