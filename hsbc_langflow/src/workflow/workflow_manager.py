import asyncio
from typing import List, Callable, Any, Dict, Optional, Type
from workflow.base.base_step import BaseStep
from workflow.base.state import WorkflowState
from utils.common.logger_util import logger

class WorkflowManager:
    """
    工作流管理器：负责协调step的执行顺序，支持动态配置和API暴露
    """
    
    def __init__(self):
        self.steps = []
        self.step_registry = {}
    
    def register_step(self, step_name: str, step_class: Type[BaseStep]):
        """注册步骤到步骤注册表"""
        self.step_registry[step_name] = step_class
    
    def configure_workflow(self, step_names: List[str], **kwargs) -> None:
        """
        配置工作流步骤顺序，支持动态配置
        
        Args:
            step_names: 步骤名称列表，按执行顺序排列
            **kwargs: 传递给步骤构造函数的参数
        """
        self.steps = []
        for name in step_names:
            if name in self.step_registry:
                step_class = self.step_registry[name]
                step_instance = step_class(**kwargs.get(name, {}))
                self.steps.append(step_instance)
    
    async def execute_workflow(self, 
                              initial_state: WorkflowState,
                              result_callback: Optional[Callable] = None) -> WorkflowState:
        """
        执行工作流
        
        Args:
            initial_state: 初始工作流状态
            result_callback: 回调函数，用于向前端发送结果
            
        Returns:
            最终工作流状态
        """
        state = initial_state
        
        # 保存回调函数到状态中，用于流式步骤
        state.callback = result_callback
        
        for i, step in enumerate(self.steps):
            # 执行当前步骤
            logger.info(f"【STEP-{i}：<{step.name}>】【START】")
            state = await step(state)
            logger.info(f"【STEP-{i}：<{step.name}>】【DONE】")
            # logger.info(f"step-{i}<{step.name}> 模块运行时间{state.parsed_results[step.name].duration}")
            # 向前端发送当前步骤的结果
            if result_callback:
                # 异步发送结果到前端，不等待完成
                step_info = {
                    "step_index": i,
                    "step_name": step.name,
                    "display": state.display,
                }
                logger.info(f"向前端发送结果：{step_info}")
                asyncio.create_task(result_callback(step_info))
        
        return state
    
    # async def execute_parallel_workflow(self,
    #                                    step_groups: List[List[BaseStep]],
    #                                    initial_state: WorkflowState,
    #                                    result_callback: Optional[Callable] = None) -> WorkflowState:
    #     """
    #     并行执行工作流步骤组
        
    #     Args:
    #         step_groups: 步骤组列表，每个组内的步骤将按顺序执行，不同组并行执行
    #         initial_state: 初始工作流状态
    #         result_callback: 回调函数，用于向前端发送结果
            
    #     Returns:
    #         最终工作流状态
    #     """
    #     state = initial_state
        
    #     for group_index, step_group in enumerate(step_groups):
    #         # 创建该组的任务列表
    #         group_tasks = []
    #         for step in step_group:
    #             # 为每个步骤创建一个异步任务
    #             task = asyncio.create_task(step(state.copy()))
    #             group_tasks.append(task)
            
    #         # 等待该组中所有步骤完成
    #         group_results = await asyncio.gather(*group_tasks)
            
    #         # 合并所有步骤结果到主状态
    #         for result_state in group_results:
    #             # 更新合并策略，根据实际需求自定义
    #             self._merge_states(state, result_state)
            
    #         # 异步发送结果到前端
    #         if result_callback and group_index < len(step_groups) - 1:
    #             asyncio.create_task(result_callback(state.display))
        
    #     return state

    # def _merge_states(self, main_state: WorkflowState, new_state: WorkflowState) -> None:
    #     """
    #     合并两个状态对象
        
    #     Args:
    #         main_state: 主状态对象，将被更新
    #         new_state: 新状态对象，其内容将合并到主状态中
    #     """
    #     # 合并步骤结果
    #     for step_name, step_result in new_state.step_results.items():
    #         main_state.step_results[step_name] = step_result
        
    #     # 合并执行历史
    #     main_state.execution_history.extend(new_state.execution_history)
        
    #     # 合并其他关键字段
    #     if new_state.display:
    #         main_state.display = new_state.display
        
    #     if new_state.results:
    #         if not main_state.results:
    #             main_state.results = {}
    #         main_state.results.update(new_state.results)
        
    #     # 合并缓存数据
    #     main_state.cache.update(new_state.cache) 