'''
File Created: Wednesday, 28th May 2025 1:59:20 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Tuesday, 3rd June 2025 7:14:50 am
'''

from typing import Dict, Any, AsyncGenerator, Optional, List, Union
import asyncio
from workflow.base.state import WorkflowState
from utils.llm.processor.prompts import get_prompt
from utils.llm.processor.parsers import get_parser
from workflow.base.base_step import BaseStep
from abc import abstractmethod
from utils.llm.llm_util import async_call_openai_sdk,  async_call_stream,async_call_opentrek
from typing import Callable

class Agent(BaseStep):
    """所有工作流步骤的基础类"""
    
    def __init__(self, 
                name: str, 
                description: str = "", 
                num_calls: int = 1,
                max_retry_calls: int = 0,
                model_name: str = 'qwen_coder',
                is_callback: bool = False
                ):
        # 调用父类初始化
        super().__init__(name=name, description=description)
        
        # Agent特有的属性
        self.llm_call_count = 0  # 记录调用大模型的次数
        self.max_retry_calls = max_retry_calls  
        self.num_calls = num_calls
        self.model_name = model_name
        self.is_callback = is_callback
        
    async def execute(self, state: WorkflowState) -> WorkflowState:
        """
        执行步骤的核心逻辑
        
        Args:
            state: 工作流状态对象
            
        Returns:
            更新后的工作流状态
        """
        # 记录开始执行的步骤并创建步骤结果
        state.current_step = self.name
        step_result = state.create_step_result(self.name)
        
        try:
            
            # 预处理步骤，构建prompt
            state = await self.preprocess(state)
            state.model_name = self.model_name
            
            
            # 先尝试通过call_llm_chain获取多个结果
            try:
                raw_results = await self.call_llm_chain(
                    messages=state.prompt[self.name],
                    num_calls=self.num_calls,
                    model_config=state.config['model'][self.model_name],  # 从状态获取配置
                    state=state  # 传递状态，包含回调函数
                )
                
                # 如果没有获得有效结果，则尝试重试机制
                if not raw_results:
                    self.logger.warning(f"base_llm_step.execute.call_llm_chain未获得有效结果，尝试使用retry_llm_call")
                    raw_results = await self.retry_llm_call(
                        messages=state.prompt[self.name],
                        model_config=state.config['model'][self.model_name],   # 从状态获取配置
                        state=state  # 传递状态，包含回调函数
                    )
            except Exception as e:
                self.logger.error(f"base_llm_step.execute.call_llm_chain执行失败: {str(e)}，尝试使用retry_llm_call")
                # 如果call_llm_chain失败，则尝试重试机制
                raw_results = await self.retry_llm_call(
                    messages=state.prompt[self.name],
                    model_config=state.config['model'][self.model_name],   # 从状态获取配置
                    state=state  # 传递状态，包含回调函数
                )
                # TODO: 如果上述出错，怎么处理 -- 否则直接将报错信息往后处理了
            
            # 解析多个结果
            state = await self.parse_results(raw_results, state)
            
            
            
            # 成功完成步骤
            step_result.complete(status="success")
            
        except Exception as e:
            error_msg = self.handle_error(e)
            state.display = error_msg
            state.error = str(e)
            step_result.complete(status="error", error=str(e))
        
        # try:
        #     state = await self.postprocess(state, step_result)
        # except Exception as e:
        #     self.logger.error(f"base_llm_step.execute.postprocess执行失败: {str(e)}")
        #     state.error = str(e)
        #     state.status = "error"
            
        self.update_state(state, step_result)
        # 展示结果
        state.display = await self.display_results(state.results)
        # 只记录步骤完成后的状态
        state.add_execution_history(self.name)
        self.logger.info(f"step-{self.name}:Results: {step_result}")
        return state
        
    async def get_progress_message(self) -> List[str]:
        """
        获取该步骤的进度消息
        
        Returns:
            步骤进度消息列表
        """
        return [f"**{self.name}**", f">>   {self.description}..."]
    
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        预处理步骤，主要用于构建prompt
        
        Args:
            state: 工作流状态
            
        Returns:
            更新后的工作流状态
        """
        # 更新LLM配置
        # 如果状态中设置了template_name，则使用它加载并格式化提示
        if state.cache['template_name']:
            # 使用模板系统加载prompt
            # 兼容openai格式
            prompt_template_user = get_prompt(state.cache['template_name']+"_user")
            prompt_template_system = get_prompt(state.cache['template_name']+"_system")
            
            # 获取格式化变量
            format_vars = self.get_prompt_variables(state)
            user_input_params = format_vars['user']
            system_input_params= format_vars['system']
            # 格式化prompt
            formatted_prompt_user = prompt_template_user.format_prompt(**user_input_params).to_string()
            formatted_prompt_system = prompt_template_system.format_prompt(**system_input_params).to_string()
            
            messages = [
                {"role": "system", "content": formatted_prompt_system},
                {"role": "user", "content": formatted_prompt_user}
            ]
            state.prompt[self.name] = messages  # 存储到缓存中
        
        return state
    
    @abstractmethod
    def get_prompt_variables(self, state: WorkflowState) -> Dict[str, Any]:
        """
        获取用于格式化提示的变量
        
        Args:
            state: 工作流状态
            
        Returns:
            变量字典
        """
        # 默认实现，返回空字典，由子类覆盖实现具体逻辑
        return {}
    
    async def parse_results(self, raw_results: Any, state: WorkflowState) -> WorkflowState:
        """
        解析大模型的原始输出并更新状态
        
        Args:
            raw_results: 大模型的原始输出，通常是嵌套列表
            state: 工作流状态对象
            
        Returns:
            更新后的工作流状态
        """
        # 获取当前步骤的结果对象
        step_result = state.step_results[self.name]
        
        try:
            if isinstance(raw_results, list):
                # 使用set进行去重
                raw_results = list(set([str(item) for item in raw_results]))
                
            step_result.raw_response = raw_results
            
            if not raw_results:
                state.error = "未获取到有效结果"
                state.status = "error"
                self.logger.warning(f"base_llm_step.parse_results: 去重后的raw_results为空")
                return state
            
            # 第二步：特定内容解析
            parsed_result = await self.parse_content(raw_results)
            step_result.parsed_result = parsed_result
            
            if parsed_result.get("status") == "error":
                state.error = parsed_result.get("error")
                state.status = "error"
            else:
                state.results = parsed_result.get("data")  # 保持向后兼容
                
            return state
                
        except Exception as e:
            error_msg = f"base_llm_step.parse_results: 解析结果失败: {str(e)}"
            state.error = error_msg
            state.status = "error"
            self.logger.error(error_msg)
            return state


    async def parse_content(self, contents: List[str]) -> Dict[str, Any]:
        """
        通用的内容解析逻辑
        
        Args:
            contents: 提取的内容列表
            
        Returns:
            解析后的结果字典
        """
        if not contents or contents is None:
            return {
                "status": "error",
                "data": None,
                "error": "未获取到有效内容"
            }
        
        try:
            # 过滤掉空字符串、只包含空白字符的字符串，以及字符串形式的空列表
            valid_contents = [
                content for content in contents 
                if content and content.strip() and content.strip() not in ['[]', '[ ]', '{}', '{ }']
            ]
            
            if not valid_contents:
                return {
                    "status": "error",
                    "data": None,
                    "error": "内容为空或仅包含空白字符或空列表"
                }
        except Exception as e:
            self.logger.error(f"解析内容失败: {str(e)}")
            return {
                "status": "error",
                "data": None,
                "error": str(e)
            }
        
        try:
            parser = get_parser(self.parser_name)
            
            # 初始化结果集合（用于去重）和错误列表
            parsed_set = set()
            error_list = []

            
            # 对每个结果尝试解析，收集所有成功的解析结果和错误
            for i, content in enumerate(contents):
                try:
                    parsed_data = parser.parse(content)
                    if isinstance(parsed_data, str):
                        parsed_set.add(parsed_data)
                    elif isinstance(parsed_data, list):
                        parsed_set.update(item if isinstance(item, str) else str(item) for item in parsed_data)
                    elif isinstance(parsed_data, dict):
                        import json
                        parsed_set.add(json.dumps(parsed_data, ensure_ascii=False, sort_keys=True))
                    else:
                        parsed_set.add(str(parsed_data))
                    self.logger.debug(f"成功解析第 {i+1}/{len(contents)} 个结果")
                except Exception as parse_error:
                    error_msg = f"解析第 {i+1}/{len(contents)} 个结果失败: {str(parse_error)}"
                    # 保存原始内容以便排查
                    error_list.append(error_msg)
                    self.logger.warning(f"{error_msg}，原始内容: {content[:200]}...")
            
            # 将结果集合转换为列表
            parsed_list = list(parsed_set)
            # 根据解析结果确定状态
            if parsed_list:
                # 合并结果
                combined_result = self.combine_results(parsed_list)
                return {
                    "status": "success",
                    "data": combined_result,
                    "error": error_list if error_list else None
                }
            else:
                # 所有内容都解析失败
                error_message = "base_llm_step.parse_content: 所有内容解析失败: " + "; ".join(error_list)
                self.logger.warning(error_message)
                return {
                    "status": "error",
                    "data": None,
                    "error": error_message
                }
            
        except Exception as e:
            error_msg = f"base_llm_step.parse_content: 解析过程发生错误: {str(e)}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "data": None,
                "error": error_msg
            }
    
    def combine_results(self, results: List[Any]) -> Any:
        """
        合并多个解析结果为单一结果
        
        Args:
            results: 解析结果列表
            
        Returns:
            合并后的结果
        """
            
        # 默认返回整个列表
        return results
    
    # async def postprocess(self, state: WorkflowState, step_result: Any) -> WorkflowState:
    #     """
    #     后处理步骤，主要用于处理解析后的结果
    #     """
    #     return state
    
    async def _increment_llm_call_count(self) -> None:
        """增加LLM调用计数（内部方法）"""
        self.llm_call_count += 1
        self.logger.debug(f"LLM调用次数: {self.llm_call_count}/{self.max_retry_calls * self.num_calls}")
        
    async def _can_call_llm(self) -> bool:
        """
        检查是否还可以调用LLM
        
        Returns:
            如果未超过最大调用次数，返回True
        """
        return self.llm_call_count < self.max_retry_calls * self.num_calls
    
    async def retry_llm_call(self, messages, model_config, state: WorkflowState) -> Any:
        """
        调用LLM函数并在出错时进行重试
        
        Args:
            llm_func: 要调用的LLM函数
            *args: 传递给LLM函数的位置参数
            **kwargs: 传递给LLM函数的关键字参数
            
        Returns:
            LLM调用的结果
            
        Raises:
            Exception: 如果重试后仍然失败，则抛出最后一次的异常
        """
        
        try:
            
            # 调用call_llm_chain，它会尝试调用num_calls次(这里是1次)
            results = await self.call_llm_chain(messages, model_config, self.max_retry_calls, state)
            
            # 如果结果为空，则表示调用失败
            if not results:
                self.logger.error(f"retry_llm_call.在重试后无法获取有效结果")
                raise Exception(f"retry_llm_call.调用失败且无法获取有效结果")
                
            # 返回第一个结果
            return results 
            
        except Exception as e:
            self.logger.error(f"retry_llm_call.调用失败: {str(e)}")
            return self.handle_error(e)
            
    async def call_llm_chain(self, 
                            messages, 
                            model_config, 
                            num_calls: int,
                            state: WorkflowState = None
                            ) -> List[Any]:
        """
        并行多次调用LLM函数并收集所有结果（内部方法）
        
        Args:
            messages: 消息列表
            num_calls: 调用次数
            state: 工作流状态，用于传递回调函数
            
        Returns:
            包含所有调用结果的列表
        """
        results = []
        remaining_calls = self.num_calls if num_calls is None else num_calls
        
        if remaining_calls <= 0:
            self.logger.warning(f"call_llm_chain.计划调用次数为0，无法执行LLM调用")
            return results
        
        self.logger.info(f"call_llm_chain.开始并行执行 {remaining_calls} 次LLM调用")
        
        # 创建所有任务
        tasks = []
        for i in range(remaining_calls):
            # 检查是否可以调用
            if not await self._can_call_llm():
                self.logger.warning(f"call_llm_chain.已达到最大调用次数限制，仅创建 {len(tasks)} 个任务")
                break
            
            # 增加调用计数
            await self._increment_llm_call_count()
            
            
            # 兼容多prompt 情况
            if isinstance(messages, list) and len(messages) > 0 and isinstance(messages[0], list):
                for message in messages:
                    task = asyncio.create_task(self._execute_call(message, model_config, state))
                    tasks.append(task)
            else:
                # 创建任务并保存任务ID
                task = asyncio.create_task(self._execute_call(messages, model_config, state))
                tasks.append(task)
        
        # 等待所有任务完成
        if tasks:
            # 使用gather收集所有结果
            all_results = await asyncio.gather(*tasks)
            
            # 处理结果
            for i, result in enumerate(all_results):
                if result is None:
                    self.logger.warning(f"call_llm_chain.第 {i+1}/{remaining_calls} 次LLM调用返回空结果")
                elif isinstance(result, Exception):
                    self.logger.error(f"call_llm_chain.第 {i+1}/{remaining_calls} 次LLM调用失败: {str(result)}")
                else:
                    results.append(result)
                    self.logger.debug(f"call_llm_chain.第 {i+1}/{remaining_calls} 次LLM调用成功")
        
        self.logger.info(f"call_llm_chain.完成 {len(results)}/{len(tasks)} 次成功的LLM调用")
        return results

    async def _execute_call(self, 
                            messages, 
                            model_config,
                            state: WorkflowState = None
                            ) -> str:
        """执行LLM调用"""
        if self.is_callback:
            # 从state中获取回调函数，如果有的话
            stream_callback = getattr(state, 'callback', None)
            return await self._execute_stream_call(messages, model_config, stream_callback)
        else:
            return await self._execute_single_call(messages, model_config)
        
    async def _execute_single_call(self, 
                                messages, 
                                model_config
                                ) -> str:
        """单个LLM调用的包装函数，包含错误处理"""
        try:
            param = {"model": model_config['name'], 
                    "messages": messages,
                    "key":model_config['key'],
                    'url':model_config['url'],
                    'max_tokens':model_config.get('max_tokens',30000)}
            
            response = await async_call_openai_sdk(**param)
            content = response.choices[0].message.content
            if model_config.get('separator'):
                sep = model_config['separator']
                content = content.split(sep)[-1]
            # 执行调用
            if not content:  # 如果结果为空
                self.logger.warning(f"{self.name} : LLM调用返回空结果")
                return None  # 返回None而不是抛出异常
            return content
        
        except Exception as e:
            # 记录错误但不重新抛出，直接返回异常对象
            self.logger.error(f"{self.name} : LLM调用失败: {str(e)}")
            return e  # 直接返回异常对象而不是重新抛出
    
    async def _execute_stream_call(self, 
                                messages, 
                                model_config,
                                stream_callback: Optional[Callable] = None) -> str:
        """流式LLM调用，支持实时回调"""
        try:
            # 构建通用参数
            param = {
                "model": model_config['name'],
                "messages": messages,
                "key": model_config['key'],
                'url': model_config['url'],
                'max_tokens': model_config.get('max_tokens', 30000),
                'model_type': model_config.get('model_type', 'openai')  # 默认为openai类型
            }
            
            # 捕获完整内容
            full_content = ""
            
            # 定义通用回调
            async def handle_content(content):
                nonlocal full_content
                if content:
                    full_content += content
                    # 如果提供了外部回调，调用它
                    if stream_callback:
                        # 创建与步骤回调兼容的格式
                        step_info = {
                            "step_name": self.name,
                            "display": content,  # 只发送增量内容
                            "is_stream": True  # 标记这是流式内容
                        }
                        await stream_callback(step_info)
            
            # 使用统一流式API调用
            async for _ in async_call_stream(callback=handle_content, **param):
                pass  # 我们通过回调处理内容，这里不需要处理yield的内容
            
            # 处理分隔符
            if model_config.get('separator'):
                sep = model_config['separator']
                full_content = full_content.split(sep)[-1]
            
            # 发送流式输出结束标记
            if stream_callback:
                step_info = {
                    "step_name": self.name,
                    "display": "",
                    "is_stream": True,
                    "stream_end": True  # 标记流式输出结束
                }
                await stream_callback(step_info)
                
            if not full_content:
                self.logger.warning(f"{self.name} : LLM流式调用返回空结果")
                return None
                
            return full_content
            
        except Exception as e:
            self.logger.error(f"{self.name} : LLM流式调用出错: {str(e)}")
            raise e
        
    async def __call__(self, state: WorkflowState) -> WorkflowState:
        """
        使步骤可以像函数一样被调用
        
        Args:
            state: 工作流状态对象
            
        Returns:
            更新后的工作流状态
        """
        return await self.execute(state)
        
    # def combine_async_response(self, content: str) -> str:
    #     """
    #     合并异步响应，如果内容是字符串形式的JSON数组则合并其中的元素
        
    #     Args:
    #         content: 需要处理的内容字符串，例如：'["测试", "问题"]'
            
    #     Returns:
    #         处理后的内容
    #     """
    #     import json
        
    #     # 检查内容是否是字符串形式的JSON数组
    #     if content and isinstance(content, str):
    #         content = content.strip()
    #         if (content.startswith('[') and content.endswith(']')):
    #             try:
    #                 # 尝试解析JSON数组
    #                 items = json.loads(content)
    #                 if isinstance(items, list):
    #                     # 合并数组中的所有字符串元素
    #                     return ''.join(str(item) for item in items)
    #             except:
    #                 pass  # 解析失败则保持原样返回
                    
    #     return content
