from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from workflow.base.state import WorkflowState
from utils.common.logger_util import logger
import inspect

class BaseStep(ABC):
    """所有工作流步骤的抽象基类"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        # 获取当前类的文件路径
        current_file = inspect.getfile(self.__class__)
        # 绑定多个上下文信息
        self.logger = logger.bind(
            step_name=name,
            file=current_file,
            class_name=self.__class__.__name__
        )
        
    async def __call__(self, state: WorkflowState) -> WorkflowState:
        """使步骤可以像函数一样被调用"""
        return await self.execute(state)
    
    async def preprocess(self, state: WorkflowState) -> WorkflowState:
        """
        预处理步骤
        """
        return state
    
    async def execute(self, state: WorkflowState) -> WorkflowState:
        """
        执行步骤的核心逻辑
        
        Args:
            state: 工作流状态对象
            
        Returns:
            更新后的工作流状态
        """
        # 记录开始执行的步骤并创建步骤结果
        
        return state

    async def display_results(self, results: Any) -> str:
        """
        格式化步骤执行结果用于显示给用户界面
        
        Args:
            result: 步骤执行结果
            
        Returns:
            格式化后的结果字符串，适合用户界面展示
        """
        if results is None:
            return ""
        return str(results)
    
    def handle_error(self, error: Exception) -> str:
        """
        处理步骤执行过程中的错误
        
        Args:
            error: 捕获的异常
            
        Returns:
            错误消息
        """
        self.logger.error(f"步骤 {self.name} 执行失败: {str(error)}")
        return f"步骤 {self.name} 执行失败: {str(error)}" 
    
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """
        获取需要更新到state的键值对
        
        Args:
            state: 工作流状态对象
            step_outputs: 步骤输出
            
        Returns:
            需要更新的键值对字典
        """
        # 默认实现，子类可以覆盖此方法以自定义更新逻辑
        return {}
    
    def update_state(self, state: WorkflowState, step_outputs: Any) -> None:
        """
        更新工作流状态
        
        Args:
            state: 工作流状态对象
            step_outputs: 步骤输出
            
        Returns:
            更新后的工作流状态
        """
        update_values = self.get_update_values(state, step_outputs)
        state.update(**update_values)