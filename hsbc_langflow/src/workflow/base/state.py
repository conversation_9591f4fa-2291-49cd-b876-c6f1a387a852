from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timezone, timedelta
import os
from dotenv import load_dotenv
from utils.common.config_util import get_yml_config
from utils.db.mschema.db_config import DBConfig
from utils.db.mschema.database_env import DataBaseEnv
from utils.db.common.client_util import pgvector_client, mysql_client
beijing_timezone = timezone(timedelta(hours=8))
load_dotenv()

class StepResult(BaseModel):
    """单个步骤的执行结果"""
    step_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: str = "running"  # running, success, error
    error: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="存储错误信息的字典，包含不同级别的错误"
    )
    raw_response: Optional[List[str]] = None
    parsed_result: Optional[Dict[str, Any]] = None
    
    @property
    def duration(self) -> Optional[float]:
        """计算步骤执行时间（秒）"""
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    def complete(self, status: str = "success", error: Optional[str] = None):
        """完成步骤执行
        
        Args:
            status: 步骤状态 ('success' 或 'error')
            error: 错误信息，如果提供则会被记录为 critical 级别的错误
        """
        self.end_time = datetime.now(tz=beijing_timezone)
        self.status = status
        if error:
            self.add_error(message=error, level="critical")

    def add_error(self, message: str, level: str = "warning", details: Optional[Dict] = None):
        """添加错误信息
        Args:
            message: 错误信息
            level: 错误级别 ('critical', 'error', 'warning', 'info')
            details: 额外的错误详情
        """
        if not self.error:
            self.error = {}
        
        error_entry = {
            "message": message,
            "timestamp": datetime.now(tz=beijing_timezone),
            "details": details or {}
        }
        
        if level not in self.error:
            self.error[level] = []
        self.error[level].append(error_entry)
        
        # 如果是严重错误，同时更新状态
        if level == "critical":
            self.status = "error"

def get_config(global_db_config):
    def get_database_config(db_config):
        dialect = db_config.get('dialect','postgresql')
        database_config = DBConfig(dialect=dialect,db_name=db_config['database'], user_name=db_config['user'], db_pwd=db_config['password'], db_host=db_config['host'], port=db_config['port'])
        return database_config
    
    nl2sql_db_config = get_database_config(global_db_config)
    return nl2sql_db_config
    
class WorkflowState(BaseModel):
    """
    工作流的状态管理类，跟踪整个工作流程中的数据流转和状态变化
    """
    status: str = "pending"
    # LLM相关配置
    model_name: str = "qwen_vllm"
    config: Any = get_yml_config()
    
    # DB_CONFIG相关配置
    global_db_config: Any = config.get('database')
    nl2sql_db_config: Any = get_config(global_db_config)
    db_type: str = global_db_config.get('dialect','postgresql')
    db_env: Any = None
    
    # 基础信息
    user_question: str = ""
    hint: str = """**重要说明：数据模型结构**

        *   **真实表名**: 实际操作的表名格式为 **'table_模型ID'** (例如: `table_1`, `table_12345`)，其中 `模型ID` 是具体的模型标识符。
        *   **真实列名**: 列名是 **原始表名** 和 **原始字段名** 的组合，用下划线连接 (例如: `adm_lon_varoius_term_type`, `bdm_acc_creditcard_installment_acct_bal`)。列名中的原始字段名可能对应元数据中的字段ID (如 `term_type` 对应 `adm_lon_varoius_field87`)，实际生成SQL时需确认准确的列名。为便于理解，以下规则使用 `原始表名_字段名` 形式。
        *   **操作基础**: 所有查询和计算都 **基于这张单一的模型宽表** (例如: `table_1`) 进行
    **重要说明：业务逻辑**
    虽然用户查询中不一定会明确提到，但以下业务逻辑是必须遵守的：
    1. 贷款余额的计算必须基于银保监会要求的口径，即 `adm_lon_varoius_is_cbirc_loan = 'Y'`
    
    
    请根据以下指导解析用户查询，生成SQL或表达式：

    1.  **查询模式**:
        *   **原子指标**: "查询[日期] [维度组合] 贷款余额"。维度组合可能包括行业、业务类型、期限等。示例："查询2024年底 制造业 中长期 贷款余额"。
        *   **计算指标**: "计算[指标A]和[指标B]的[和/差等]"。通常涉及多个原子指标的聚合。示例："计算 个人住房按揭贷款 和 个人其他贷款 的总和"。

    2.  **原子指标核心规则**:
        *   **默认口径**: 基于`adm_lon_varoius`相关列的查询基于银保监会要求，必须包含条件 `adm_lon_varoius_is_cbirc_loan = 'Y'` (是银保监各项贷款口径, Y)。
        *   **目标字段**: 主要查询目标为贷款余额，通常是 `adm_lon_varoius_loan_bal_cny`。
        *   **期限**: 通过 `.adm_lon_varoius_term_type` 过滤。
            *   "短期" 对应 `'01'` 。
            *   "中长期" 对应 `'02'` 。
            *   "各项贷款" 通常指所有期限（不按 `term_type` 过滤或包含所有类型）。

    3.  **关键维度映射 (基于 模型表 中的组合列)**:
        *   **行业 (adm_lon_varoius_loan_purpose)**: 通常通过 `like 'X%'` 过滤。
            *   'A%': 农、林、牧、渔业 (代码 'A')
            *   'B%': 采矿业 (代码 'B')
            *   'C%': 制造业 (代码 'C')
            *   'D%': 电力、热力、燃气及水的生产和供应业 (代码 'D')
            *   'E%': 建筑业 (代码 'E')
            *   'F%': 批发和零售业 (代码 'F')
            *   'G%': 交通运输、仓储和邮政业 (代码 'G')
            *   'H%': 住宿和餐饮业 (代码 'H')
            *   'I%': 信息传输、计算机服务和软件业 (代码 'I')
            *   'J%': 金融业 (代码 'J')
            *   'K%': 房地产业 (代码 'K')
            *   'L%': 租赁和商务服务业 (代码 'L')
            *   'M%': 科学研究和技术服务业 (代码 'M')
            *   'N%': 水利、环境和公共设施管理业 (代码 'N')
            *   'O%': 居民服务、修理和其他服务业 (代码 'O')
            *   'P%': 教育 (代码 'P')
            *   'Q%': 卫生、社会工作 (代码 'Q')
            *   'R%': 文化、体育和娱乐业 (代码 'R')
            *   'S%': 公共管理、社会保障和社会组织 (代码 'S')
            *   'T%': 国际组织 (代码 'T')
            *   'Z%': 对境外贷款 (代码 'Z')
        *   **业务类型 (adm_lon_varoius_business_type)**:
            *   '19': 个人贷款-信用卡
            *   '020201': 个人贷款-住房按揭贷款
            *   '020202': 个人贷款-汽车
            *   '020299': 个人贷款-其他
            *   '17': 买断式转贴现
            *   '18': 买断其他票据类资产
        *   **高技术产业 (adm_lon_varoius_is_high_tech_industry)**:
            *   'Y': 是高技术产业
            *   'N': 不是高技术产业
        *   **知识产权密集型产业 (adm_lon_varoius_is_kownledge_industry)**:
            *   'Y': 是知识产权密集型产业
            *   'N': 不是知识产权密集型产业
        *   **数字经济产业分类 (adm_lon_varoius_digital_industry_type)**:
            *   '01': 数字产品制造业
            *   '02': 数字产品服务业
            *   '03': 数字技术应用业
            *   '04': 数字要素驱动业

    4.  **特殊逻辑处理**:
        *   **信用卡中长期贷款**: 需要合并两部分计算（注意列名和代码,以table_1为例），伪代码如下：`SUM_IF(table_1 where adm_lon_varoius_term_type='02' and table_1.adm_lon_varoius_business_type='19', table_1.adm_lon_varoius_loan_bal_cny) + SUM_IF(table_1 where table_1.bdm_acc_creditcard_installment_stageing_term > 12, table_1.bdm_acc_creditcard_installment_acct_bal)`。
        *   **养老产业贷款**: 过滤条件复杂，需同时考虑 `adm_lon_varoius_loan_purpose`  的特定值列表以及 `adm_lon_varoius_indu_type` (注意: indu_type元数据未提供) 与另一组 `loan_purpose` 列表的组合条件。

    请运用这些详细规则和精确的元数据映射来理解用户意图并构建准确的查询逻辑。
    """
    index_type: str = ""
    
    # 步骤执行状态
    current_step: str = ""
    execution_history: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 提取的关键信息
    keywords: List[str] = Field(default_factory=list)
    col_data_examples: Dict[str, List[str]] = {
        "adm_lon_varoius_data_dt": ["2024-01-01", "2024-01-02", "2024-01-03"],
        "adm_lon_varoius_cust_no": ["C10000001", "C10000002", "C10000003"],
        "adm_lon_varoius_busi_no": ["B202401010001", "B202401010002", "B202401010003"],
        "adm_lon_varoius_is_high_tech_industry": ["Y", "Y", "N"],
        "adm_lon_varoius_digital_industry_type": ["01", "02", "03"],
        "adm_lon_varoius_is_kownledge_industry": ["Y", "Y", "N"],
        "adm_lon_varoius_bussiness_type": ["19", "17", "020299"],
        "adm_lon_varoius_loan_bal": ["80000.0000", "150000.0000", "450000.0000"],
        "adm_lon_varoius_loan_bal_cny": ["80000.0000", "150000.0000", "450000.0000"],
        "adm_lon_varoius_loan_bal_usd": ["11034.4828", "20689.6552", "62068.9655"],
        "adm_lon_varoius_term_type": ["01", "02"],
        "adm_lon_varoius_is_cbirc_loan": ["Y", "N"],
        "adm_lon_varoius_loan_purpose": ["A", "B", "C"],
        "bdm_acc_creditcard_installment_acct_bal": ["100000.0000", "200000.0000", "500000.0000"],
        "bdm_acc_creditcard_installment_stageing_term": ["12", "24", "36"],
    }
    # 数据库相关配置
    database_name: str = ""
    selected_columns: Dict[str, List[str]] = Field(default_factory=dict)
    formatted_columns: List[str] = Field(default_factory=list)
    # 临时db schema，用于retrieval后的展示
    temp_db_schema: str = ""
    candidate_columns: Dict[str, List[str]] = Field(default_factory=dict)
    merged_column_info: Dict[str, Any] = Field(default_factory=dict)
    # 最终的db schema
    db_schema: str = ""
    # associated keys schema
    associated_keys_schema: str = ""
    # 数据库客户端
    pgvector_client: Any = pgvector_client
    mysql_client: Any = mysql_client
    # 模型id
    model_id: str = ""
    #--------------------------------
    # Vector DB相关配置
    global_vector_db_config: Any = config.get('vector_database')
    vector_db_type: str = "pgvector"
    fewshot_examples: str = ""
    
    
    template_name: Optional[str] = None  # 当前使用的提示模板名称
    
    # 结果存储
    results: Any = Field(default_factory=dict)
    display: str = ""
    error: Optional[str] = None
    sql_candidates: List[str] = Field(default_factory=list)
    execution_results: Dict[str, Any] = Field(default_factory=dict)
    refined_execution_results: Dict[str, Any] = Field(default_factory=dict)
    refined_sql: List[str] = Field(default_factory=list)
    selected_sql: str = ""
    flat_sql: str = ""
    technical_caliber: str = ""
    index_formula: Dict[str, Any] = Field(default_factory=dict)
    modified_sql: str = ""
    biz_caliber: str = ""
    query_target_time: str = ""
    time_period: str = "day"
    # 缓存数据，用于存储步骤间共享的中间结果
    cache: Dict[str, Any] = Field(default_factory=dict)
    prompt: Dict[str, Any] = Field(default_factory=dict)
    
    # 添加新的属性
    step_results: Dict[str, StepResult] = Field(default_factory=dict)
    
    # 回调函数，用于流式输出
    callback: Optional[Callable] = None
    
    class Config:
        """Pydantic配置"""
        # 允许额外属性
        extra = "allow"
        # 允许任意类型字段
        arbitrary_types_allowed = True
    
    def take_snapshot(self) -> Dict[str, Any]:
        """获取当前状态的简化快照，只包含关键字段"""
        return {
            "current_step": self.current_step,
            "keywords": self.keywords,
            "results": self.results,
            "display": self.display,
            "error": self.error
        }
    
    def add_execution_history(self, step_name: str) -> None:
        """
        记录步骤执行历史，只记录执行后的状态
        """
        # 只记录步骤名称、当前状态和时间戳
        index = len(self.execution_history)
        self.execution_history.append({
            "index": index,
            "step": step_name,
            "state": self.take_snapshot(),
            "timestamp": str(datetime.now(tz=beijing_timezone))
        })
    
    def update(self, **kwargs) -> None:
        """
        更新状态
        
        Args:
            **kwargs: 要更新的键值对
        """
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def get_execution_summary(self) -> str:
        """
        获取执行历史的摘要
        
        Returns:
            执行历史的格式化摘要
        """
        if not self.execution_history:
            return "尚未执行任何步骤"
        
        summary = "### 执行历史\n\n"
        for idx, record in enumerate(self.execution_history):
            summary += f"**步骤 {idx+1}**: {record['step']}\n"
            if record.get("outputs", {}).get("display"):
                summary += f"{record['outputs']['display']}\n\n"
        
        return summary
    
    def to_context_dict(self) -> Dict[str, Any]:
        """
        将状态转换为字典,用于记录输入输出状态
        
        Returns:
            context字典表示
        """
        return {
            "user_question": self.user_question,
            "hint": self.hint,
            "keywords": self.keywords,
            "results": self.results,
            "display": self.display,
            "config": self.config,
            "error": self.error,
            **self.cache  # 包含所有缓存数据
        }
    
    
    def create_step_result(self, step_name: str) -> StepResult:
        """创建新的步骤结果"""
        result = StepResult(
            step_name=step_name,
            start_time=datetime.now(tz=beijing_timezone)
        )
        self.step_results[step_name] = result
        return result