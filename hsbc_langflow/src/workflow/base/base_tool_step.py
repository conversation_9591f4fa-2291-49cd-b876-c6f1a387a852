from abc import  abstractmethod
from typing import Any, Dict
from workflow.base.state import WorkflowState
from workflow.base.base_step import BaseStep

class Tool(BaseStep):
    """工具步骤的基类，用于非LLM步骤"""
        
    async def execute(self, state: WorkflowState,**kwargs) -> WorkflowState:
        """执行工具步骤的处理逻辑"""
        # 记录开始执行的步骤并创建步骤结果
        state.current_step = self.name
        step_result = state.create_step_result(self.name)
        
        try:
            
            # 预处理步骤，构建prompt
            state = await self.preprocess(state)
            try:
                raw_results = await self.use_tool(state,**kwargs)
            except Exception as e:
                self.logger.warning(f"步骤 {self.name} resources执行失败: {str(e)}")
                error_msg = self.handle_error(e)
                state.display = error_msg
                state.error = str(e)
                step_result.complete(status="error", error=str(e))
            
            # 解析多个结果
            state = await self.parse_results(raw_results, state)
            
            
            
            # 成功完成步骤
            step_result.complete(status="success")
            
        except Exception as e:
            error_msg = self.handle_error(e)
            state.display = error_msg
            state.error = str(e)
            step_result.complete(status="error", error=str(e))
        
        self.update_state(state, step_result)
        
        # 展示结果
        state.display = await self.display_results(state.results)
        # 只记录步骤完成后的状态
        state.add_execution_history(self.name)
        self.logger.info(f"step-{self.name}:Results: {step_result}")
        return state
    
    @abstractmethod
    async def use_tool(self, state: WorkflowState,**kwargs: Any) -> Any:
        """执行具体的工具操作，由子类实现"""
        pass

    async def parse_results(self, raw_results: Any, state: WorkflowState) -> WorkflowState:
        """解析工具返回的结果"""
        step_result = state.step_results[self.name]
        step_result.raw_response = raw_results
        # Some logic
        try:
            step_result.parsed_result = await self.parse_content(raw_results)
            state.results = step_result.parsed_result.get("data")
            return state
        except Exception as e:
            step_result.add_error(message=f"解析结果失败: {e}", level="error")
            return state
    
    async def display_results(self, results: Any) -> str:
        """展示工具返回的结果"""
        return str(results)
    
    async def parse_content(self, contents: Any) -> Any:
        """解析工具返回的内容"""
        
        return {
            "status": "success",
            "data": contents,
            "error": None
        }
    def update_state(self, state: WorkflowState, step_outputs: Any) -> None:
        """
        更新工作流状态
        
        Args:
            state: 工作流状态对象
            step_outputs: 步骤输出
            
        Returns:
            更新后的工作流状态
        """
        update_values = self.get_update_values(state, step_outputs)
        state.update(**update_values)
            
    def get_update_values(self, state: WorkflowState, step_outputs: Any) -> Dict[str, Any]:
        """获取需要更新到state的键值对"""
        return {}