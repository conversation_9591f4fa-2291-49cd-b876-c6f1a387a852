# 默认加载的配置组合
defaults:
  - _self_
  - api_server: defaults
  - logging: defaults                  # 加载我们干净的日志包
  # - database: default
  # --- 加载所有可用的数据库和模型配置 ---
  # 下面的配置项会委托给对应配置组（如'database'）下的defaults.yaml文件，
  # 来完成该模块的完整配置组合。
  - database: defaults
  - model: defaults
  # --- 工作流加载方式变更 ---
  # 直接加载工作流配置文件
  - workflow/<EMAIL>
  - workflow/<EMAIL>

# --- active选择 ---
# 在这里指定本次运行要使用的具体配置
active_rdb: mysql
active_vdb: pgvector
active_llm: opentrek
active_embedding: moka-m3e-base
