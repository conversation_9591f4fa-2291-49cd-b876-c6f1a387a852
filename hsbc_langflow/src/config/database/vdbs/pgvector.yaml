# @package database.vdb
# config/vector.y/ml/pgvector.yaml

_target_: base.db.implementations.vs.pgvector.pgvector_client.PGVectorClient

db_type: "pgvector"
connection:
  host: "**************"
  port: 30146
  user: "pgvector"
  password: "pgvector"
  db_name: "postgres"
usage:
  general: 
    table_name: "hsbc_embedding_data"
    search_schema:
      vector_field: "embedding_column_name"
      limit: 3
      metric_type: "cosine"
      output_fields: ["id", "partition_key"]
      expr: ""
      partition_name: ""
    query_schema:
      limit: 3
      expr: ""
      partition_name: ""
      output_fields: ["id", "partition_key"] 