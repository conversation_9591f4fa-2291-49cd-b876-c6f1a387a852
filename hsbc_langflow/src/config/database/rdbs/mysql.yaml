# @package database.rdb
# config/database/rdb/mysql.yaml

_target_: base.db.implementations.rdb.mysql.mysql_sqlalchemy_client.MySQLSQLAlchemyClient

db_type: "mysql"
connection:
  host: "**************"
  port: 37615
  user: "root"
  password: "idea@1008"
  db_name: "hsbc_data"
usage:
  model_info:
    table_name: "model_info"
  where_info:
    table_name: "where_info"
  partition_info:
    table_name: "partition_key" 