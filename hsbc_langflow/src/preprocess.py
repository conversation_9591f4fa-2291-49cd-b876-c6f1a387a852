import os
import argparse
import logging
import yaml
from pathlib import Path

from database_utils.retrieval.db_values.preprocess import make_db_lsh

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_config() -> dict:
    """
    加载配置文件
    
    返回:
        dict: 配置信息
    """
    config_path = Path(__file__).parent / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def preprocess_db():
    """
    为PostgreSQL数据库创建MinHash LSH索引
    """
    # 加载配置
    config = load_config()
    db_config = config.get('database', {})
    
    # 设置数据库相关参数
    db_id = db_config.get('database', 'adm')
    db_directory_path = Path(__file__).parent.parent / "database" / db_id
    
    # 确保目录存在
    db_directory_path.mkdir(parents=True, exist_ok=True)
    
    # LSH参数
    signature_size = 128
    n_gram = 3
    threshold = 0.5
    
    logging.info(f"开始为数据库 {db_id} 创建LSH索引")
    
    # 调用make_db_lsh函数创建LSH索引
    make_db_lsh(
        str(db_directory_path),
        db_type="postgresql",
        db_id=db_id,
        signature_size=signature_size,
        n_gram=n_gram,
        threshold=threshold,
        verbose=True
    )
    
    logging.info(f"数据库 {db_id} 的LSH索引创建完成")

if __name__ == '__main__':
    preprocess_db()
